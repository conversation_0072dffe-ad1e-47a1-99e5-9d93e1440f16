/*
* @desc:缓存相关
* @company:云南奇讯科技有限公司
* @Author: yixiaohu
* @Date:   2022/3/9 11:25
 */

package consts

import "fmt"

const (
	CacheModelMem   = "memory"
	CacheModelRedis = "redis"
	CacheGroupRedis = "cache"
	//PersistenceRedis 持久化的redis
	PersistenceRedis = "persistence"
	// CacheSysDict 字典缓存菜单KEY
	CacheSysDict = "sysDict"

	// CacheSysDictTag 字典缓存标签
	CacheSysDictTag = "sysDictTag"
	// CacheSysConfigTag 系统参数配置
	CacheSysConfigTag = "sysConfigTag"
)

const (
	// PlatAdminAdLink 导流链接配置
	PlatAdminAdLink = "PLAT:ADMIN:AD:LINK:"
	// PlatAdConfig 广告配置信息
	PlatAdConfig = "PLAT:AD:CONFIG:"
	// DyClientToken 抖音client token
	DyClientToken = "DY:CLIENT:TOKEN:"
	// WxAccessToken 微信access token
	WxAccessToken = "WX:ACCESS:TOKEN:"
	//PlatTheaterInfo 主剧情
	PlatTheaterInfo = "PLAT:PLAYLET:THEATER_INFO:INFO:"
	//PlatTheaterDetail 详情Key
	PlatTheaterDetail = "PLAT:PLAYLET:THEATER_DETAIL:INFO:"

	PlatQrCodeConfig       = "PLAT:QR:CODE:CONFIG:"
	PlatUserChannelInfo    = "PLAT:USER:CHANNEL:INFO:"
	PlatUserNewChannelInfo = "PLAT:USER:NEW:CHANNEL:INFO:"
	PlatDistributionStat   = "PLAT:DISTRIBUTION:STAT"
	WxVideoPlayLinkSwitch  = "WX:VIDEO:PLAY:LINK:SWITCH"
	WxVideoPlayLink        = "WX:VIDEO:PLAY:LINK:"

	PlatUserDayPlayEpisodes    = "PLAT:USER:DAY:PLAY:EPISODES:"
	PlatUserDayPlayerNums      = "PLAT:USER:DAY:PLAYER:NUMS:"
	PlatUserDayVipPlayEpisodes = "PLAT:USER:DAY:VIP:PLAY:EPISODES:"
	PlatUserDayVipPlayerNums   = "PLAT:USER:DAY:VIP:PLAYER:NUMS:"
	PlatUserDayLikeNums        = "PLAT:USER:DAY:LIKE:NUMS:"    // 点赞数
	PlatUserDayFollowNums      = "PLAT:USER:DAY:FOLLOW:NUMS:"  // 追剧数
	PlatUserDayShareNums       = "PLAT:USER:DAY:SHARE:NUMS:"   // 转发数
	PlatUserHourLikeNums       = "PLAT:USER:HOUR:LIKE:NUMS:"   // 小时点赞数
	PlatUserHourFollowNums     = "PLAT:USER:HOUR:FOLLOW:NUMS:" // 小时追剧数
	PlatUserHourShareNums      = "PLAT:USER:HOUR:SHARE:NUMS:"  // 小时转发数
	PlatUserHourPlayerNums     = "PLAT:USER:HOUR:PLAYER:NUMS:" // 小时播放次数

	PlatUserMemberVipRecord        = "PLAT:USER:VIP:RECORD:"                   //vip缓存key
	PlatUserTheaterVipKey          = "PLAT:USER:VIP:UNLOCK:"                   // 用户解锁剧（剧vip）缓存key 前缀 + saasId
	PlatUserOrderSuccessRateSwitch = "PLAT:USER:ORDER:SUCCESS:RATE:SWITCH_NEW" //用户订单支付成功率检测开关

	PlatUserOrderSuccessRateLock = "PLAT:USER:ORDER:SUCCESS:RATE" //支付成功率检测的锁

	PlatSystem = "PLAT:SYSTEM:"

	// PlatAlbumReviewResultLock 检测短剧审核结果分布式锁key
	PlatAlbumReviewResultLock = "PLAT:ALBUM:REVIEW:RESULT"
	PlatDpaAlbumStatusLock    = "PLAT:DPA:ALBUM:STATUS:LOCK"
	// PlatUserWatchVideoStatLock 用户看剧统计分布式锁key
	PlatUserWatchVideoStatLock = "PLAT:USER:WATCH:VIDEO:STAT"
	// PlatDistributionStatLock 分销统计分布式锁key
	PlatDistributionStatLock      = "PLAT:DISTRIBUTION:STAT:LOCK"
	PlatDistributionStatTodayLock = "PLAT:DISTRIBUTION:STAT:TODAY:LOCK"
	// PlatAdCallbackStatLock 用户广告统计分布式锁key
	PlatAdCallbackStatLock  = "PLAT:AD:CALLBACK:LOCK"
	PlatAdCallbackStat      = "PLAT:AD:CALLBACK:STAT"
	PlatAdCallbackVideoStat = "PLAT:AD:CALLBACK:VIDEO:STAT"
	PlatAdCallbackDayUpStat = "PLAT:AD:CALLBACK:DAY:UP:STAT"
	// PlatAdCallbackAppletStatLock 小程序广告统计分布式锁key
	PlatAdCallbackAppletStatLock    = "PLAT:AD:CALLBACK:APPLET:LOCK"
	PlatAdCallbackAppletStat        = "PLAT:AD:CALLBACK:APPLET:STAT"
	PlatAdCallbackHourStatLock      = "PLAT:AD:CALLBACK:HOUR:LOCK"
	PlatAdCallbackWxPitcherStatLock = "PLAT:AD:CALLBACK:WX:PITCHER:LOCK"
	// PlatUserTaskStatLock 用户任务统计分布式锁key
	PlatUserTaskStatLock = "PLAT:USER:TASK:STAT:LOCK"
	PlatUserTaskStat     = "PLAT:USER:TASK:STAT"
	// PlatOrderRefundLock 订单退款分布式锁key
	PlatOrderRefundLock        = "PLAT:ORDER:REFUND:LOCK"
	PlatSyncAdvertiserInfoLock = "PLAT:SYNC:ADVERTISER:INFO:LOCK"
	// DYAdInfoKeyPrefix 抖音广告缓存key 前缀 + saasId
	DYAdInfoKeyPrefix = "PLAT:DYINFO:"
	// 用户解锁剧集缓存key 前缀 + saasId
	UserPurchasedKeyPrefix = "PLAT:USER:PURCHASE:RECORD:"
	// AdvertisingInfoKeyPrefix 其他广告缓存key 前缀 + saasId
	AdvertisingInfoKeyPrefix        = "PLAT:AD:"
	PlatTheaterPlayRechargeStatLock = "PLAT:THEATER:PLAY:RECHARGE:STAT:LOCK"
	PlatAdCallbackCostStatLock      = "PLAT:AD:CALLBACK:COST:LOCK"
	PlatCouponCodeList              = "PLAT:COUPON:CODE:LIST:" //平台兑换码队列存通过编号存多个队列

	PlatCouponCodePreList = "PLAT:COUPON:CODE:PRE:LIST" //补货后上一次遗留的code数据

	PlatCouponCodeListNum = "PLAT:COUPON:CODE:LIST:NUM" //劵队列数目

	PlatCouponCodeCurrentIndex = "PLAT:COUPON:CODE:LIST:INDEX"

	PlatCouponCodePreListSpecs      = "PLAT:COUPON:CODE:PRE:LIST:SPECS"
	PlatCouponCodeListSpecs         = "PLAT:COUPON:CODE:LIST:SPECS"
	PlatCouponCodeListNumSpecs      = "PLAT:COUPON:CODE:LIST:NUM:SPECS"
	PlatCouponCodeCurrentIndexSpecs = "PLAT:COUPON:CODE:LIST:INDEX:SPECS"

	PlatCouponCockStockLocK = "PLAT:COUPON:CODE:STOCK:GET:LOCK"

	PlatCouponCockStockNotifyLocK        = "PLAT:COUPON:CODE:STOCK:NOTIFY:LOCK"
	PlatCouponCockStockNotifyStatus      = "PLAT:COUPON:CODE:STOCK:NOTIFY:STATUS"
	PlatCouponCockStockNotifyStatusSpecs = "PLAT:COUPON:CODE:STOCK:NOTIFY:STATUS:SPECS"
	//PlatAppletRechargeLock
	PlatAppletRechargeStaticsLock = "PLAT:APPLET:RECHARGE:STATICS:LOCK"

	PlatPitcherVideoRechargeStaticsLock = "PLAT:PITCHER:VIDEO:RECHARGE:STATICS:LOCK"

	PlatPitcherVideoHourRechargeStaticsLock = "PLAT:PITCHER:HOUR:VIDEO:RECHARGE:STATICS:LOCK"

	PlatOrderCouponRecoveryTaskLock = "PLAT:ORDER:COUPON:RECOVERY:TASK:LOCK"

	// PlatOrderRefreshCourseKey 更新订单充值类型
	PlatOrderRefreshCourseKey = "PLAT:ORDER:REFRESH:COURSE:KEY"
	// PlatUserDelAdInfoPageKey 删除旧用户广告缓存页数
	PlatUserDelAdInfoPageKey = "PLAT:USER:DEL:AD:INFO:PAGE"

	PlatTheaterHourTimesStatLock  = "PLAT:THEATER:HOUR:TIMES:STAT"
	PlatTheaterHotRankingStatLock = "PLAT:THEATER:HOT:RANKING:STAT"
	PlatCreateSignPayLock         = "PLAT:CREATE:SIGN:PAY:LOCK"

	// PlatUserDayActiveCount 渠道激活数
	PlatUserDayActiveCount   = "PLAT:USER:DAY:ACTIVE:COUNT:"
	PlatDyCheckPanelBindLock = "PLAT:DY:CHECK:PANEL:BIND:LOCK"
	// AdAdvertiserAccountReport 广告平台的报表数据  包含指标数据和流水
	AdAdvertiserAccountReport   = "AD:ADVERTISER:ACCOUNT:REPORT:"
	AdAdvertiserProjectReport   = "AD:ADVERTISER:PROJECT:REPORT:"
	AdAdvertiserPromotionReport = "AD:ADVERTISER:Promotion:REPORT:"

	PlatSyncFqPromotionUrlTaskLock = "PLAT:SYNC:FQ:PROMOTION:URL:TASK:LOCK"
	PlatSyncDzPromotionUrlTaskLock = "PLAT:SYNC:DZ:PROMOTION:URL:TASK:LOCK"
	FQAdBookInfo                   = "PLAT:FQ:BOOK:INFO:"
	// 新剧数据播报任务锁
	SendNewTheaterDataReportTaskLock = "PLAT:SEND:NEW:THEATER:DATA:REPORT:TASK:LOCK"
	// 外采剧数据推送任务锁
	SendOutsourcedTheaterDataReportTaskLock     = "PLAT:SEND:OUTSOURCED:THEATER:DATA:REPORT:TASK:LOCK"
	ChangduBookInfoApiCookie                    = "CHANGDU:BOOK:INFO:API:COOKIE"
	ChangduBookMentionKeywords                  = "CHANGDU:BOOK:MENTION:KEYWORDS"
	SendChangduBookDeliveryStatusReportTaskLock = "PLAT:SEND:CHANGDU:BOOK:DELIVERY:STATUS:REPORT:TASK:LOCK"
	PlatSyncByteAppletAssetLock                 = "PLAT:SYNC:BYTE:APPLET:ASSET:LOCK"
	PlatSyncWechatAppletAssetLock               = "PLAT:SYNC:WECHAT:APPLET:ASSET:TASK:LOCK"
	PlatDzNativeLinkCrawlTaskLock               = "PLAT:DZ:NATIVE:LINK:CRAWL:TASK"
)

const (
	// TaskSign 签到任务
	TaskSign = "PLAT:USER:TASK:SIGN"
	// TaskWatchVideo 看视频任务
	TaskWatchVideo = "PLAT:USER:TASK:WATCH:VIDEO"
	// WatchVideoTimes 看视频完成次数
	WatchVideoTimes = "PLAT:USER:TASK:WATCH:VIDEO:TIMES"
	// WatchVideoNums 看视频完成人数
	WatchVideoNums = "PLAT:USER:TASK:WATCH:VIDEO:NUMS"
	// TaskWatchAd 看广告任务
	TaskWatchAd = "PLAT:USER:TASK:WATCH:AD"
	// WatchAdTimes 看广告完成次数
	WatchAdTimes = "PLAT:USER:TASK:WATCH:AD:TIMES"
	// WatchAdNums 看广告完成人数
	WatchAdNums = "PLAT:USER:TASK:WATCH:AD:NUMS"
	// UnlockEpisodes 解锁剧集
	UnlockEpisodes = "PLAT:USER:TASK:UNLOCK:EPISODES"
	// AddToDesktop 添加至桌面
	AddToDesktop = "PLAT:USER:TASK:ADD:TO:DESKTOP"
	// SubMsg 订阅
	SubMsg = "PLAT:USER:TASK:SUB"
	// PlatUserDayVisitCount 访问人数
	PlatUserDayVisitCount = "PLAT:USER:DAY:VISIT:COUNT:"

	Share = "PLAT:USER:TASK:SUB:SHARE"
	//每日分享次数
	ShareTimes = "PLAT:USER:TASK:SUB:SHARE:TIMES"
	//每日分享人数
	ShareNums = "PLAT:USER:TASK:SUB:SHARE:NUMS"
	// 添加追剧
	AddCatchUp = "PLAT:USER:TASK:SUB:ADD:CATCHUP"
	// 浏览剧场
	BrowseTheTheater = "PLAT:USER:TASK:SUB:BROWSE:THE:THEATER"
	// 邀请好友
	InviteFriends = "PLAT:USER:TASK:SUB:INVITE:FRIENDS"
)

const (
	PlatAdxMaterialLock       = "PLAT:ADX:MATERIAL:LOCK"
	PlatAdxMaterialTodayLock  = "PLAT:ADX:MATERIAL:TODAY:LOCK"
	PlatAdxMaterialPageId     = "PLAT:ADX:MATERIAL:PAGE:ID"
	PlatAdxMaterialDayPageId  = "PLAT:ADX:MATERIAL:DAY:PAGE:ID:"
	PlatAdxCreativeLock       = "PLAT:ADX:CREATIVE:LOCK"
	PlatAdxCreativeTodayLock  = "PLAT:ADX:CREATIVE:TODAY:LOCK"
	PlatAdxCreativePageId     = "PLAT:ADX:CREATIVE:PAGE:ID"
	PlatAdxCreativeDayPageId  = "PLAT:ADX:CREATIVE:DAY:PAGE:ID:"
	PlatAdxProductLock        = "PLAT:ADX:PRODUCT:LOCK"
	PlatAdxProductPageId      = "PLAT:ADX:PRODUCT:PAGE:ID"
	PlatAdxProductDayPageId   = "PLAT:ADX:PRODUCT:DAY:PAGE:ID:"
	PlatAdxPublisherLock      = "PLAT:ADX:PUBLISHER:LOCK"
	PlatAdxPublisherPageId    = "PLAT:ADX:PUBLISHER:PAGE:ID"
	PlatAdxPublisherDayPageId = "PLAT:ADX:PUBLISHER:DAY:PAGE:ID:"
	PlatAdxMediaLock          = "PLAT:ADX:MEDIA:LOCK"
	PlatAdxPageIdTtlSeconds   = 30 * 24 * 60 * 60
)

// GetShareTimesStatKey 每日分享
func GetShareTimesStatKey(date, account, appId string) string {
	return ShareTimes + date + account + appId
}

// GetShareNumsStatKey 每日分享人数key
func GetShareNumsStatKey(date, account, appId string) string {
	return ShareNums + date + account + appId
}

// GetAddCatchUpStatKey 添加追剧统计
func GetAddCatchUpStatKey(date, account, appId string) string {
	return AddCatchUp + date + account + appId
}

// GetBrowseTheTheaterStatKey 浏览剧场统计
func GetBrowseTheTheaterStatKey(date, account, appId string) string {
	return BrowseTheTheater + date + account + appId
}

// GetInviteFriendsStatKey 邀请好友统计
func GetInviteFriendsStatKey(date, account, appId string) string {
	return InviteFriends + date + account + appId
}

// GetTaskSignStatKey 统计次数需要使用 day 为签到天数
func GetTaskSignStatKey(date, account, appId, day string) string {
	return TaskSign + date + account + appId + day
}

// GetWatchVideoTimesStatKey 获取当前用户看视频完成次数key
func GetWatchVideoTimesStatKey(date, account, appId, watchTime string) string {
	return WatchVideoTimes + date + account + appId + watchTime
}

// GetUserPurchasedKey 用户购买记录
func GetUserPurchasedKey(saasId string) string {
	return UserPurchasedKeyPrefix + saasId
}

// GetWatchVideoNumsStatKey 获取当前用户看视频完成人数key
func GetWatchVideoNumsStatKey(date, account, appId string) string {
	return WatchVideoNums + date + account + appId
}

// GetWatchAdTimesStatKey 获取当前看广告完成次数key
func GetWatchAdTimesStatKey(date, account, appId string) string {
	return WatchAdTimes + date + account + appId
}

// GetWatchAdNumsStatKey 获取当前看广告完成人数key
func GetWatchAdNumsStatKey(date, account, appId string) string {
	return WatchAdNums + date + account + appId
}

// GetUnlockEpisodesStatKey 解锁剧集统计
func GetUnlockEpisodesStatKey(date, account, appId string) string {
	return UnlockEpisodes + date + account + appId
}

// GetAddToDesktopStatKey 添加桌面统计
func GetAddToDesktopStatKey(date, account, appId string) string {
	return AddToDesktop + date + account + appId
}

// GetSubMsgStatKey 订阅统计
func GetSubMsgStatKey(date, account, appId string) string {
	return SubMsg + date + account + appId
}

func GetPlatUserDayVisitCountKey(date string, account string, appId string) string {
	return fmt.Sprintf("%s%s:%s:%s", PlatUserDayVisitCount, date, account, appId)
}

const (
	PlatChannelPromotionIdList              = "PLAT:CHANNEL:PROMOTION:ID:LIST:"
	PlatChannelSingleNonCallbackQuantity    = "PLAT:CHANNEL:SINGLE:NON:CALLBACK:QUANTITY:"
	PlatChannelIaaPromotionIdList           = "PLAT:CHANNEL:IAA:PROMOTION:ID:LIST:"
	PlatChannelIaaSingleNonCallbackQuantity = "PLAT:CHANNEL:IAA:SINGLE:NON:CALLBACK:QUANTITY:"
)

func GetPlatChannelPromotionIdListKey(account string) string {
	return fmt.Sprintf("%s%s", PlatChannelPromotionIdList, account)
}

func GetPlatChannelSingleNonCallbackQuantityKey(account string, promotionId string) string {
	if promotionId != "" {
		return fmt.Sprintf("%s%s:%s", PlatChannelSingleNonCallbackQuantity, account, promotionId)
	} else {
		return fmt.Sprintf("%s%s", PlatChannelSingleNonCallbackQuantity, account)
	}
}

func GetPlatChannelIaaPromotionIdListKey(account string) string {
	return fmt.Sprintf("%s%s", PlatChannelIaaPromotionIdList, account)
}

func GetPlatChannelIaaSingleNonCallbackQuantityKey(account string, promotionId string) string {
	if promotionId != "" {
		return fmt.Sprintf("%s%s:%s", PlatChannelIaaSingleNonCallbackQuantity, account, promotionId)
	} else {
		return fmt.Sprintf("%s%s", PlatChannelIaaSingleNonCallbackQuantity, account)
	}
}

func GetPlatUserDayActiveCountKey(date string, account string) string {
	return fmt.Sprintf("%s%s:%s", PlatUserDayActiveCount, date, account)
}

const (
	PlatKsAdSalerCopyRightStatLock = "PLAT:KS:AD:SALER:COPY:RIGHT:STAT:LOCK"
	PlatKsAdOrderSettleStatLock    = "PLAT:KS:AD:ORDER:SETTLE:STAT:LOCK"
	PlatKsAdOrderDetailStatLock    = "PLAT:KS:AD:ORDER:DETAIL:STAT:LOCK"
)

const (
	ChannelPushStream    = "channel_push_stream"
	ChannelPushGroup1    = "channel_push_group_1"
	ChannelPushConsumer1 = "channel_push_consumer_1"
)

const (
	PlatAdAccountSubjectDataStatLock      = "PLAT:AD:ACCOUNT:SUBJECT:DATA:STAT:LOCK"
	PlatAdAccountSubjectDataStatTodayLock = "PLAT:AD:ACCOUNT:SUBJECT:DATA:STAT:TODAY:LOCK"
	PlatAdOptimizerDataStatLock           = "PLAT:AD:OPTIMIZER:DATA:STAT:LOCK"
	PlatAdOptimizerDataStatTodayLock      = "PLAT:AD:OPTIMIZER:DATA:STAT:TODAY:LOCK"
	PlatAdAccountHourMetricsDataStatLock  = "PLAT:AD:ACCOUNT:HOUR:METRICS:DATA:STAT:LOCK"
)
