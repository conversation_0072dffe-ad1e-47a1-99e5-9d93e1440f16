// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-11 14:01:13
// 生成路径: internal/app/theater/logic/changdu_book_info.go
// 生成人：cq
// desc:常读短剧
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConst "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterConsts "github.com/tiger1103/gfast/v3/internal/app/theater/consts"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/theater/dao"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/theater/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterChangduBookInfo(New())
}

func New() service.IChangduBookInfo {
	return &sChangduBookInfo{}
}

type sChangduBookInfo struct{}

func (s *sChangduBookInfo) List(ctx context.Context, req *model.ChangduBookInfoSearchReq) (listRes *model.ChangduBookInfoSearchRes, err error) {
	listRes = new(model.ChangduBookInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.ChangduBookInfo.Ctx(ctx).WithAll()
		if req.AlbumIdDy != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().AlbumIdDy+" = ?", req.AlbumIdDy)
		}
		if req.AmountLimitStatus != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().AmountLimitStatus+" = ?", gconv.Int(req.AmountLimitStatus))
		}
		if req.BookId != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().BookId+" = ?", req.BookId)
		}
		if req.BookPool != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().BookPool+" = ?", gconv.Int(req.BookPool))
		}
		if req.Category != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().Category+" = ?", req.Category)
		}
		if req.CategoryText != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().CategoryText+" = ?", req.CategoryText)
		}
		if req.CopyrightExpirationTime != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().CopyrightExpirationTime+" = ?", req.CopyrightExpirationTime)
		}
		if req.CreationStatus != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().CreationStatus+" = ?", gconv.Int(req.CreationStatus))
		}
		if req.DeliveryStatus != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().DeliveryStatus+" = ?", gconv.Int(req.DeliveryStatus))
		}
		if req.EpisodeAmount != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().EpisodeAmount+" = ?", gconv.Int(req.EpisodeAmount))
		}
		if req.EpisodePrice != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().EpisodePrice+" = ?", gconv.Int(req.EpisodePrice))
		}
		if req.FreeEpisodeCount != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().FreeEpisodeCount+" = ?", gconv.Int(req.FreeEpisodeCount))
		}
		if req.LatestUpdateTime != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().LatestUpdateTime+" = ?", req.LatestUpdateTime)
		}
		if req.NeedShowPublishTag != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().NeedShowPublishTag+" = ?", gconv.Int(req.NeedShowPublishTag))
		}
		if req.OnShelfTime != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().OnShelfTime+" = ?", req.OnShelfTime)
		}
		if req.OriginalThumbUrl != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().OriginalThumbUrl+" = ?", req.OriginalThumbUrl)
		}
		if req.PermissionStatus != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().PermissionStatus+" = ?", gconv.Int(req.PermissionStatus))
		}
		if req.PriceChanged != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().PriceChanged+" = ?", gconv.Int(req.PriceChanged))
		}
		if req.PublishTime != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().PublishTime+" = ?", req.PublishTime)
		}
		if req.SeriesName != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().SeriesName+" like ?", "%"+req.SeriesName+"%")
		}
		if req.ThumbUri != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().ThumbUri+" = ?", req.ThumbUri)
		}
		if req.ThumbUrl != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().ThumbUrl+" = ?", req.ThumbUrl)
		}
		if req.WxAuditStatus != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().WxAuditStatus+" = ?", gconv.Int(req.WxAuditStatus))
		}
		if req.WxIsReject != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().WxIsReject+" = ?", gconv.Int(req.WxIsReject))
		}
		if req.DyAuditStatus != "" {
			m = m.Where(dao.ChangduBookInfo.Columns().DyAuditStatus+" = ?", gconv.Int(req.DyAuditStatus))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "book_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.ChangduBookInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.ChangduBookInfoListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.ChangduBookInfoListRes{
				AlbumIdDy:               v.AlbumIdDy,
				AmountLimitStatus:       v.AmountLimitStatus,
				BookId:                  v.BookId,
				BookPool:                v.BookPool,
				Category:                v.Category,
				CategoryText:            v.CategoryText,
				CopyrightExpirationTime: v.CopyrightExpirationTime,
				CreationStatus:          v.CreationStatus,
				DeliveryStatus:          v.DeliveryStatus,
				EpisodeAmount:           v.EpisodeAmount,
				EpisodePrice:            v.EpisodePrice,
				FreeEpisodeCount:        v.FreeEpisodeCount,
				LatestUpdateTime:        v.LatestUpdateTime,
				NeedShowPublishTag:      v.NeedShowPublishTag,
				OnShelfTime:             v.OnShelfTime,
				OriginalThumbUrl:        v.OriginalThumbUrl,
				PermissionStatus:        v.PermissionStatus,
				PriceChanged:            v.PriceChanged,
				PublishTime:             v.PublishTime,
				SeriesName:              v.SeriesName,
				ThumbUri:                v.ThumbUri,
				ThumbUrl:                v.ThumbUrl,
				WxAuditStatus:           v.WxAuditStatus,
				WxIsReject:              v.WxIsReject,
				DyAuditStatus:           v.DyAuditStatus,
			}
		}
	})
	return
}

func (s *sChangduBookInfo) GetByBookId(ctx context.Context, bookId string) (res *model.ChangduBookInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.ChangduBookInfo.Ctx(ctx).WithAll().Where(dao.ChangduBookInfo.Columns().BookId, bookId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sChangduBookInfo) Add(ctx context.Context, req *model.ChangduBookInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.ChangduBookInfo.Ctx(ctx).Insert(do.ChangduBookInfo{
			BookId:                  req.BookId,
			AlbumIdDy:               req.AlbumIdDy,
			AmountLimitStatus:       req.AmountLimitStatus,
			BookPool:                req.BookPool,
			Category:                req.Category,
			CategoryText:            req.CategoryText,
			CopyrightExpirationTime: req.CopyrightExpirationTime,
			CreationStatus:          req.CreationStatus,
			DeliveryStatus:          req.DeliveryStatus,
			EpisodeAmount:           req.EpisodeAmount,
			EpisodePrice:            req.EpisodePrice,
			FreeEpisodeCount:        req.FreeEpisodeCount,
			LatestUpdateTime:        req.LatestUpdateTime,
			NeedShowPublishTag:      req.NeedShowPublishTag,
			OnShelfTime:             req.OnShelfTime,
			OriginalThumbUrl:        req.OriginalThumbUrl,
			PermissionStatus:        req.PermissionStatus,
			PriceChanged:            req.PriceChanged,
			PublishTime:             req.PublishTime,
			SeriesName:              req.SeriesName,
			ThumbUri:                req.ThumbUri,
			ThumbUrl:                req.ThumbUrl,
			WxAuditStatus:           req.WxAuditStatus,
			WxIsReject:              req.WxIsReject,
			DyAuditStatus:           req.DyAuditStatus,
			PremiereTime:            req.PremiereTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sChangduBookInfo) Edit(ctx context.Context, req *model.ChangduBookInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.ChangduBookInfo.Ctx(ctx).WherePri(req.BookId).Update(do.ChangduBookInfo{
			AlbumIdDy:               req.AlbumIdDy,
			AmountLimitStatus:       req.AmountLimitStatus,
			BookPool:                req.BookPool,
			Category:                req.Category,
			CategoryText:            req.CategoryText,
			CopyrightExpirationTime: req.CopyrightExpirationTime,
			CreationStatus:          req.CreationStatus,
			DeliveryStatus:          req.DeliveryStatus,
			EpisodeAmount:           req.EpisodeAmount,
			EpisodePrice:            req.EpisodePrice,
			FreeEpisodeCount:        req.FreeEpisodeCount,
			LatestUpdateTime:        req.LatestUpdateTime,
			NeedShowPublishTag:      req.NeedShowPublishTag,
			OnShelfTime:             req.OnShelfTime,
			OriginalThumbUrl:        req.OriginalThumbUrl,
			PermissionStatus:        req.PermissionStatus,
			PriceChanged:            req.PriceChanged,
			PublishTime:             req.PublishTime,
			SeriesName:              req.SeriesName,
			ThumbUri:                req.ThumbUri,
			ThumbUrl:                req.ThumbUrl,
			WxAuditStatus:           req.WxAuditStatus,
			WxIsReject:              req.WxIsReject,
			DyAuditStatus:           req.DyAuditStatus,
			PremiereTime:            req.PremiereTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sChangduBookInfo) Delete(ctx context.Context, bookIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.ChangduBookInfo.Ctx(ctx).Delete(dao.ChangduBookInfo.Columns().BookId+" in (?)", bookIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// SendBookDeliveryStatusReportTask 常读短剧更新通知任务
func (s *sChangduBookInfo) SendBookDeliveryStatusReportTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		// 创建一个新的上下文，以便在函数结束时不会取消
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()

		// 获取锁，避免任务重复执行
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConst.SendChangduBookDeliveryStatusReportTaskLock, redsync.WithRetryDelay(50*time.Millisecond))

		// 尝试获取锁
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConst.SendChangduBookDeliveryStatusReportTaskLock))

		// 释放锁
		defer mutex.UnlockContext(ctx)

		err = s.SendBookDeliveryStatusReport(innerContext)
		liberr.ErrIsNil(ctx, err, "常读短剧更新通知失败")

		// 记录任务执行日志
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SendBookDeliveryStatusReportTask",
			CreatedAt:  gtime.Now(),
			Result:     "常读短剧更新通知，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// SendBookDeliveryStatusReport 发送短剧上下架状态通知
func (s *sChangduBookInfo) SendBookDeliveryStatusReport(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 1. 查询数据库的所有常读短剧
		var dbBooks []*model.ChangduBookInfoInfoRes
		err = dao.ChangduBookInfo.Ctx(ctx).WithAll().Scan(&dbBooks)
		if err != nil {
			g.Log().Errorf(ctx, "查询数据库常读短剧失败: %v", err)
			return
		}

		// 创建数据库书籍映射，以BookId为key
		dbBookMap := make(map[string]*model.ChangduBookInfoInfoRes)
		for _, book := range dbBooks {
			dbBookMap[book.BookId] = book
		}
		g.Log().Infof(ctx, "数据库中共有 %d 本常读短剧", len(dbBooks))

		// 2. 同步常读短剧信息
		err, syncBooks := s.SyncBookInfo(ctx)
		if err != nil {
			g.Log().Errorf(ctx, "同步常读短剧信息失败: %v", err)
			errChatId := g.Cfg().MustGet(context.Background(), "fs.card.errChatId").String()
			libUtils.SendFeiShuAppMsg("同步常读短剧信息失败", err.Error(), errChatId)
		}

		if len(dbBooks) == 0 {
			return
		}

		// 3. 同步的数据在数据库中不存在，放入新增列表
		var newBooks []*model.ChangduBookItem
		// 4. 同步的数据在数据库中存在，判断上架状态是否一致，不一致则放入修改列表
		var updateBooks []*model.ChangduBookItem

		for _, syncBook := range syncBooks {
			if dbBook, exists := dbBookMap[syncBook.BookId]; exists {
				// 数据库中存在，检查投放状态是否变化
				if dbBook.DeliveryStatus != syncBook.DeliveryStatus {
					syncBook.BeforeDeliveryStatus = dbBook.DeliveryStatus
					updateBooks = append(updateBooks, syncBook)
				}
			} else {
				// 数据库中不存在，放入新增列表
				newBooks = append(newBooks, syncBook)
			}
		}

		// 5. 批量插入新增列表
		if len(newBooks) > 0 {
			_ = s.batchInsertBooks(ctx, newBooks)
		}

		// 6. 批量更新修改列表
		if len(updateBooks) > 0 {
			_ = s.batchInsertBooks(ctx, updateBooks)
		}

		// 7. 发送状态变更通知
		_ = s.sendDeliveryStatusNotification(ctx, newBooks, updateBooks)
	})
	return
}

// SyncBookInfo 同步常读短剧信息
func (s *sChangduBookInfo) SyncBookInfo(ctx context.Context) (err error, data []*model.ChangduBookItem) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 从redis获取cookie信息
		cookieCache, _ := commonService.RedisCache().Get(ctx, commonConst.ChangduBookInfoApiCookie)
		if cookieCache.IsNil() {
			return
		}
		var cookie = cookieCache.String()
		// 创建HTTP客户端
		httpClient := libUtils.NewHTTPClientWithTransport(5 * time.Second)

		// 基础URL和请求头
		baseURL := "https://www.changdunovel.com/novelsale/distributor/content/series/list/v1"
		headers := map[string]string{
			"accept":             "application/json, text/plain, */*",
			"accept-language":    "zh-CN,zh;q=0.9",
			"aduserid":           "2798678782776617",
			"agw-js-conv":        "str",
			"appid":              "40015682",
			"apptype":            "10",
			"cookie":             cookie,
			"distributorid":      "1832086537603129",
			"priority":           "u=1, i",
			"referer":            "https://www.changdunovel.com/sale/short-play/list?page_index=1&page_size=10",
			"sec-ch-ua":          `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`,
			"sec-ch-ua-mobile":   "?0",
			"sec-ch-ua-platform": `"Windows"`,
			"sec-fetch-dest":     "empty",
			"sec-fetch-mode":     "cors",
			"sec-fetch-site":     "same-origin",
			"user-agent":         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
		}

		// 分页参数
		pageIndex := 0
		pageSize := 100
		total := 0
		for {
			// 构建请求参数
			params := map[string]interface{}{
				"page_index": pageIndex,
				"page_size":  pageSize,
			}
			// 发送HTTP请求
			respBody, err := httpClient.GetWithHeader(baseURL, params, headers)
			if err != nil {
				g.Log().Errorf(ctx, "请求常读短剧API失败，页码：%d", pageIndex)
				break
			}

			// 解析响应
			var apiResp model.ChangduApiResponse
			err = json.Unmarshal(respBody, &apiResp)
			if err != nil {
				g.Log().Errorf(ctx, "解析常读短剧API响应失败，页码：%d, err: %v", pageIndex, err)
				break
			}

			// 检查响应状态
			if apiResp.Code != 0 {
				g.Log().Errorf(ctx, "常读短剧API返回错误：%s，页码：%d", apiResp.Message, pageIndex)
				liberr.ErrIsNil(ctx, errors.New(apiResp.Message))
				break
			}

			g.Log().Infof(ctx, "常读短剧API返回数据：页码：%d, 数据量大小：%v", pageIndex, len(apiResp.Data.Data))

			// 检查是否有数据
			if apiResp.Data == nil || len(apiResp.Data.Data) == 0 {
				g.Log().Infof(ctx, "第 %d 页没有数据，同步完成", pageIndex)
				break
			}

			total += len(apiResp.Data.Data)
			data = append(data, apiResp.Data.Data...)
			// 批量插入数据
			if len(apiResp.Data.Data) > 0 {
				_ = s.batchInsertBooks(ctx, apiResp.Data.Data)
			}

			// 检查是否还有更多数据
			if (pageIndex+1)*pageSize >= apiResp.Data.Total {
				break
			}

			// 下一页
			pageIndex++
		}

		g.Log().Infof(ctx, "数据同步完成，总数据量：%d", total)
	})
	return
}

// batchInsertBooks 批量插入书籍数据
func (s *sChangduBookInfo) batchInsertBooks(ctx context.Context, books []*model.ChangduBookItem) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(books) == 0 {
			return
		}
		// 转换数据格式
		var insertData []do.ChangduBookInfo
		for _, book := range books {
			// 处理布尔类型转换
			needShowPublishTag := 0
			if book.NeedShowPublishTag {
				needShowPublishTag = 1
			}
			priceChanged := 0
			if book.PriceChanged {
				priceChanged = 1
			}
			wxIsReject := 0
			if book.WxIsReject {
				wxIsReject = 1
			}

			insertData = append(insertData, do.ChangduBookInfo{
				BookId:                  book.BookId,
				AlbumIdDy:               book.AlbumIdDy,
				AmountLimitStatus:       book.AmountLimitStatus,
				BookPool:                book.BookPool,
				Category:                book.Category,
				CategoryText:            book.CategoryText,
				CopyrightExpirationTime: book.CopyrightExpirationTime,
				CreationStatus:          book.CreationStatus,
				DeliveryStatus:          book.DeliveryStatus,
				EpisodeAmount:           book.EpisodeAmount,
				EpisodePrice:            book.EpisodePrice,
				FreeEpisodeCount:        book.FreeEpisodeCount,
				LatestUpdateTime:        book.LatestUpdateTime,
				NeedShowPublishTag:      needShowPublishTag,
				OnShelfTime:             book.OnShelfTime,
				OriginalThumbUrl:        book.OriginalThumbUrl,
				PermissionStatus:        book.PermissionStatus,
				PriceChanged:            priceChanged,
				PublishTime:             book.PublishTime,
				SeriesName:              book.SeriesName,
				ThumbUri:                book.ThumbUri,
				ThumbUrl:                book.ThumbUrl,
				WxAuditStatus:           book.WxAuditStatus,
				WxIsReject:              wxIsReject,
				DyAuditStatus:           book.DyAuditStatus,
				PremiereTime:            book.PremiereTime,
			})
		}
		_, err = dao.ChangduBookInfo.Ctx(ctx).Save(insertData)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

// sendDeliveryStatusNotification 发送投放状态变更通知
func (s *sChangduBookInfo) sendDeliveryStatusNotification(ctx context.Context, newBooks []*model.ChangduBookItem, updateBooks []*model.ChangduBookItem) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 普通通知内容
		var notificationContent strings.Builder
		// 关键词通知内容 需要@所有人
		var keywordNotificationContent strings.Builder
		var newBookTitle = "<font color='blue'>----------短剧新增----------</font>\n"
		var newBookTemp = "<font color='orange'>**%s**</font>\n- 短剧ID：%v\n- 首发时间：%s\n- 状态：%s\n- 短剧集数：%v集\n- 是否首发：%s\n\n"
		var newBookCount int
		var newBookContainsKeywordCount int
		for _, book := range newBooks {
			bookContent := fmt.Sprintf(newBookTemp,
				book.SeriesName,
				book.BookId,
				book.PremiereTime,
				s.buildDeliveryStatus(book.DeliveryStatus),
				book.EpisodeAmount,
				s.buildBookPublishTag(book.NeedShowPublishTag))
			if s.CheckKeywordsForMention(book.SeriesName) {
				if newBookContainsKeywordCount == 0 {
					keywordNotificationContent.WriteString(newBookTitle)
				}
				newBookContainsKeywordCount++
				keywordNotificationContent.WriteString(bookContent)
			} else {
				if newBookCount == 0 {
					notificationContent.WriteString(newBookTitle)
				}
				newBookCount++
				notificationContent.WriteString(bookContent)
			}
		}
		var updateBookTitle = "<font color='green'>----------短剧更新----------</font>\n"
		var updateBookTemp = "<font color='orange'>**%s**</font>\n- 短剧ID：%v\n- 首发时间：%s\n- 状态：%s\n- 原状态：%s\n- 短剧集数：%v集\n- 是否首发：%s\n\n"
		var updateBookContainsKeywordCount int
		var updateBookCount int
		for _, book := range updateBooks {
			bookContent := fmt.Sprintf(updateBookTemp,
				book.SeriesName,
				book.BookId,
				book.PremiereTime,
				s.buildDeliveryStatus(book.DeliveryStatus),
				s.buildDeliveryStatus(book.BeforeDeliveryStatus),
				book.EpisodeAmount,
				s.buildBookPublishTag(book.NeedShowPublishTag))
			if s.CheckKeywordsForMention(book.SeriesName) {
				if updateBookContainsKeywordCount == 0 {
					// 有新增短剧 则添加分割线
					if newBookContainsKeywordCount > 0 {
						keywordNotificationContent.WriteString("<hr>")
					}
					keywordNotificationContent.WriteString(updateBookTitle)
				}
				updateBookContainsKeywordCount++
				keywordNotificationContent.WriteString("\n")
				keywordNotificationContent.WriteString(bookContent)
			} else {
				if updateBookCount == 0 {
					// 有新增短剧 则添加分割线
					if newBookCount > 0 {
						notificationContent.WriteString("<hr>")
					}
					notificationContent.WriteString(updateBookTitle)
				}
				updateBookCount++
				notificationContent.WriteString(bookContent)
			}
		}
		chatId := g.Cfg().MustGet(context.Background(), "fs.card.chatId3").String()
		if notificationContent.Len() > 0 {
			libUtils.SendFeiShuAppMsg(theaterConsts.ChangDuBookDeliveryStatusNotification, notificationContent.String(), chatId)
		}
		if keywordNotificationContent.Len() > 0 {
			var msg = keywordNotificationContent.String()
			if s.CheckKeywordsForMention(msg) {
				var atAll = "<at id=all></at>\n"
				msg = atAll + msg
				libUtils.SendFeiShuAppMsg(theaterConsts.ChangDuBookDeliveryStatusNotification, msg, chatId)
			}
		}
	})
	return
}

func (s *sChangduBookInfo) buildDeliveryStatus(deliveryStatus int) string {
	var bookStatus string
	if deliveryStatus == 1 {
		bookStatus = "<font color='green'>已上架</font>"
	} else if deliveryStatus == 2 {
		bookStatus = "<font color='red'>已下架</font>"
	} else if deliveryStatus == 0 {
		bookStatus = "<font color='grey'>未上架</font>"
	}
	return bookStatus
}

func (s *sChangduBookInfo) buildBookPublishTag(needShowPublishTag bool) string {
	if needShowPublishTag {
		return "是"
	} else {
		return "否"
	}
}

// CheckKeywordsForMention 检测文本中是否包含需要@所有人的关键词
// 参数：text - 待检测的文本内容
// 返回：bool - 如果包含关键词返回true，否则返回false
func (s *sChangduBookInfo) CheckKeywordsForMention(text string) bool {
	// 从redis获取关键词列表
	ctx := context.Background()
	keywordsCache, err := commonService.RedisCache().Get(ctx, commonConst.ChangduBookMentionKeywords)
	if err != nil || keywordsCache.IsNil() {
		return false
	}
	// 解析redis中的关键词列表（假设存储为JSON数组格式）
	var keywords []string
	err = json.Unmarshal([]byte(keywordsCache.String()), &keywords)
	if err != nil {
		g.Log().Errorf(ctx, "解析关键词列表失败: %v", err)
		return false
	}
	return s.checkKeywordsInText(text, keywords)
}

// checkKeywordsInText 检查文本中是否包含关键词
func (s *sChangduBookInfo) checkKeywordsInText(text string, keywords []string) bool {
	// 遍历关键词列表，检查是否包含任何一个关键词
	for _, keyword := range keywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}
	return false
}
