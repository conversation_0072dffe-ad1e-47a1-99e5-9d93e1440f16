// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/model/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdBatchTaskInfoRes is the golang structure for table ad_batch_task.
type AdBatchTaskInfoRes struct {
	gmeta.Meta `orm:"table:ad_batch_task"`
	Id         int64       `orm:"id,primary" json:"id" dc:""`                                          //
	TaskId     string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                     // 任务ID
	TaskName   string      `orm:"task_name" json:"taskName" dc:"任务名称"`                                 // 任务名称
	MediaType  int         `orm:"media_type" json:"mediaType" dc:"媒体类型 1：巨量 "`                         // 媒体类型 1：巨量
	OptObject  int         `orm:"opt_object" json:"optObject" dc:"操作对象类型 1：账户 2：项目 3：广告"`              // 操作对象类型 1：账户 2：项目 3：广告
	OptType    int         `orm:"opt_type" json:"optType" dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`        // 操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像
	OptNum     int         `orm:"opt_num" json:"optNum" dc:"操作数量"`                                     // 操作数量
	OptStatus  string      `orm:"opt_status" json:"optStatus" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"` // 执行状态：EXECUTING：执行中  COMPLETED：执行完成
	SuccessNum int         `orm:"success_num" json:"successNum" dc:"成功数量"`                             // 成功数量
	FailNum    int         `orm:"fail_num" json:"failNum" dc:"失败数量"`                                   // 失败数量
	UserId     int         `orm:"user_id" json:"userId" dc:"归属人员"`                                     // 归属人员
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                               // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                               // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                               // 删除时间
}

type AdBatchTaskListRes struct {
	Id            int64       `json:"id" dc:""`
	TaskId        string      `json:"taskId" dc:"任务ID"`
	TaskName      string      `json:"taskName" dc:"任务名称"`
	MediaType     int         `json:"mediaType" dc:"媒体类型 1：巨量 "`
	MediaTypeName string      `json:"mediaTypeName" dc:"媒体类型名称"`
	OptObject     int         `json:"optObject" dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptObjectName string      `json:"optObjectName" dc:"操作对象类型名称"`
	OptType       int         `json:"optType" dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptTypeName   string      `json:"optTypeName" dc:"操作类型名称"`
	OptNum        int         `json:"optNum" dc:"操作数量"`
	OptStatus     string      `json:"optStatus" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	OptStatusName string      `json:"optStatusName" dc:"执行状态名称"`
	SuccessNum    int         `json:"successNum" dc:"成功数量"`
	FailNum       int         `json:"failNum" dc:"失败数量"`
	UserId        int         `json:"userId" dc:"归属人员"`
	UserName      string      `json:"userName" dc:"归属人员名称"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdBatchTaskSearchReq 分页请求参数
type AdBatchTaskSearchReq struct {
	comModel.PageReq
	MediaType string `p:"mediaType" v:"mediaType@integer#媒体类型 1：巨量 需为整数" dc:"媒体类型 1：巨量 "`
	OptObject string `p:"optObject" v:"optObject@integer#操作对象类型 1：账户 2：项目 3：广告需为整数" dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptType   string `p:"optType" v:"optType@integer#操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像需为整数" dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptStatus string `p:"optStatus" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	StartTime string `p:"startTime" dc:"开始时间"`
	EndTime   string `p:"endTime" dc:"结束时间"`
}

// AdBatchTaskSearchRes 列表返回结果
type AdBatchTaskSearchRes struct {
	comModel.ListRes
	List []*AdBatchTaskListRes `json:"list"`
}

// AdBatchTaskAddReq 添加操作请求参数
type AdBatchTaskAddReq struct {
	TaskId     string `p:"taskId"  dc:"任务ID"`
	TaskName   string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	MediaType  int    `p:"mediaType"  dc:"媒体类型 1：巨量 "`
	OptObject  int    `p:"optObject"  dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptType    int    `p:"optType"  dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptNum     int    `p:"optNum"  dc:"操作数量"`
	OptStatus  string `p:"optStatus" v:"required#执行状态：EXECUTING：执行中  COMPLETED：执行完成不能为空" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	SuccessNum int    `p:"successNum"  dc:"成功数量"`
	FailNum    int    `p:"failNum"  dc:"失败数量"`
	UserId     uint64 `p:"userId"  dc:"归属人员"`
}

// AdBatchTaskEditReq 修改操作请求参数
type AdBatchTaskEditReq struct {
	Id         int64  `p:"id" v:"required#主键ID不能为空" dc:""`
	TaskId     string `p:"taskId"  dc:"任务ID"`
	TaskName   string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	MediaType  int    `p:"mediaType"  dc:"媒体类型 1：巨量 "`
	OptObject  int    `p:"optObject"  dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptType    int    `p:"optType"  dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptNum     int    `p:"optNum"  dc:"操作数量"`
	OptStatus  string `p:"optStatus" v:"required#执行状态：EXECUTING：执行中  COMPLETED：执行完成不能为空" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	SuccessNum int    `p:"successNum"  dc:"成功数量"`
	FailNum    int    `p:"failNum"  dc:"失败数量"`
	UserId     int    `p:"userId"  dc:"归属人员"`
}

type AdBatchTaskEditAdvertiserReq struct {
	OptType        int                       `p:"optType"  dc:"操作类型 1: 修改账户名称 2: 修改账户备注 3: 修改账户头像"`
	AdvertiserList []BatchEditAdvertiserInfo `p:"advertiserList" dc:"账户信息"`
	File           *ghttp.UploadFile         `p:"file" type:"file" dc:"选择上传头像文件"`
}

type BatchEditAdvertiserInfo struct {
	AdvertiserId             string `p:"advertiserId" dc:"账户ID"`
	OriginalAdvertiserName   string `p:"originalAdvertiserName" dc:"原账户名称"`
	NewAdvertiserName        string `p:"newAdvertiserName" dc:"新账户名称"`
	OriginalAdvertiserRemark string `p:"originalAdvertiserRemark" dc:"原账户备注"`
	NewAdvertiserRemark      string `p:"newAdvertiserRemark" dc:"新账户备注"`
}
