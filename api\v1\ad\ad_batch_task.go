// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: api/v1/ad/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdBatchTaskSearchReq 分页请求参数
type AdBatchTaskSearchReq struct {
	g.Meta `path:"/list" tags:"广告批量操作任务" method:"post" summary:"广告批量操作任务列表"`
	commonApi.Author
	model.AdBatchTaskSearchReq
}

// AdBatchTaskSearchRes 列表返回结果
type AdBatchTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdBatchTaskSearchRes
}

// AdBatchTaskAddReq 添加操作请求参数
type AdBatchTaskAddReq struct {
	g.Meta `path:"/add" tags:"广告批量操作任务" method:"post" summary:"广告批量操作任务添加"`
	commonApi.Author
	*model.AdBatchTaskAddReq
}

// AdBatchTaskAddRes 添加操作返回结果
type AdBatchTaskAddRes struct {
	commonApi.EmptyRes
}

// AdBatchTaskEditReq 修改操作请求参数
type AdBatchTaskEditReq struct {
	g.Meta `path:"/edit" tags:"广告批量操作任务" method:"put" summary:"广告批量操作任务修改"`
	commonApi.Author
	*model.AdBatchTaskEditReq
}

// AdBatchTaskEditRes 修改操作返回结果
type AdBatchTaskEditRes struct {
	commonApi.EmptyRes
}

// AdBatchTaskGetReq 获取一条数据请求
type AdBatchTaskGetReq struct {
	g.Meta `path:"/get" tags:"广告批量操作任务" method:"get" summary:"获取广告批量操作任务信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdBatchTaskGetRes 获取一条数据结果
type AdBatchTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdBatchTaskInfoRes
}

// AdBatchTaskDeleteReq 删除数据请求
type AdBatchTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告批量操作任务" method:"post" summary:"删除广告批量操作任务"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdBatchTaskDeleteRes 删除数据返回
type AdBatchTaskDeleteRes struct {
	commonApi.EmptyRes
}

// AdBatchTaskEditAdvertiserReq 批量修改账户信息请求参数
type AdBatchTaskEditAdvertiserReq struct {
	g.Meta `path:"/batchEditAdvertiser" tags:"广告批量操作任务" method:"post" summary:"批量修改账户信息"`
	commonApi.Author
	model.AdBatchTaskEditAdvertiserReq
}

// AdBatchTaskEditAdvertiserRes 批量修改账户信息返回结果
type AdBatchTaskEditAdvertiserRes struct {
	g.Meta `mime:"application/json"`
}
