// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/model/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdBatchTaskDetailInfoRes is the golang structure for table ad_batch_task_detail.
type AdBatchTaskDetailInfoRes struct {
	gmeta.Meta               `orm:"table:ad_batch_task_detail"`
	Id                       int64       `orm:"id,primary" json:"id" dc:""`                                            //
	TaskId                   string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                       // 任务ID
	SerialNumber             int         `orm:"serial_number" json:"serialNumber" dc:"序号 任务中的执行顺序"`                    // 序号 任务中的执行顺序
	AdvertiserId             string      `orm:"advertiser_id" json:"advertiserId" dc:"媒体账户ID"`                         // 媒体账户ID
	OriginalAdvertiserName   string      `orm:"original_advertiser_name" json:"originalAdvertiserName" dc:"原账户名称"`     // 原账户名称
	NewAdvertiserName        string      `orm:"new_advertiser_name" json:"newAdvertiserName" dc:"新账户名称"`               // 新账户名称
	OriginalAdvertiserRemark string      `orm:"original_advertiser_remark" json:"originalAdvertiserRemark" dc:"原账户备注"` // 原账户备注
	NewAdvertiserRemark      string      `orm:"new_advertiser_remark" json:"newAdvertiserRemark" dc:"新账户备注"`           // 新账户备注
	OptResult                string      `orm:"opt_result" json:"optResult" dc:"执行结果：SUCCESS：成功  FAIL：失败"`             // 执行结果：SUCCESS：成功  FAIL：失败
	ErrMsg                   string      `orm:"err_msg" json:"errMsg" dc:"失败原因"`                                       // 失败原因
	CreatedAt                *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                 // 创建时间
	UpdatedAt                *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                 // 更新时间
	DeletedAt                *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                 // 删除时间
}

type AdBatchTaskDetailListRes struct {
	Id                       int64       `json:"id" dc:""`
	TaskId                   string      `json:"taskId" dc:"任务ID"`
	SerialNumber             int         `json:"serialNumber" dc:"序号 任务中的执行顺序"`
	AdvertiserId             string      `json:"advertiserId" dc:"媒体账户ID"`
	OriginalAdvertiserName   string      `json:"originalAdvertiserName" dc:"原账户名称"`
	NewAdvertiserName        string      `json:"newAdvertiserName" dc:"新账户名称"`
	OriginalAdvertiserRemark string      `json:"originalAdvertiserRemark" dc:"原账户备注"`
	NewAdvertiserRemark      string      `json:"newAdvertiserRemark" dc:"新账户备注"`
	OptResult                string      `json:"optResult" dc:"执行结果：SUCCESS：成功  FAIL：失败"`
	OptResultName            string      `json:"optResultName" dc:"执行结果名称"`
	ErrMsg                   string      `json:"errMsg" dc:"失败原因"`
	CreatedAt                *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdBatchTaskDetailSearchReq 分页请求参数
type AdBatchTaskDetailSearchReq struct {
	comModel.PageReq
	Id           string `p:"id" dc:""`
	TaskId       string `p:"taskId" dc:"任务ID"`
	AdvertiserId string `p:"advertiserId" dc:"媒体账户ID"`
	OptResult    string `p:"optResult" dc:"执行结果：SUCCESS：成功  FAIL：失败"`
}

// AdBatchTaskDetailSearchRes 列表返回结果
type AdBatchTaskDetailSearchRes struct {
	comModel.ListRes
	List []*AdBatchTaskDetailListRes `json:"list"`
}

// AdBatchTaskDetailAddReq 添加操作请求参数
type AdBatchTaskDetailAddReq struct {
	TaskId                   string `p:"taskId"  dc:"任务ID"`
	SerialNumber             int    `p:"serialNumber"  dc:"序号 任务中的执行顺序"`
	AdvertiserId             string `p:"advertiserId"  dc:"媒体账户ID"`
	OriginalAdvertiserName   string `p:"originalAdvertiserName" v:"required#原账户名称不能为空" dc:"原账户名称"`
	NewAdvertiserName        string `p:"newAdvertiserName" v:"required#新账户名称不能为空" dc:"新账户名称"`
	OriginalAdvertiserRemark string `p:"originalAdvertiserRemark"  dc:"原账户备注"`
	NewAdvertiserRemark      string `p:"newAdvertiserRemark"  dc:"新账户备注"`
	OptResult                string `p:"optResult"  dc:"执行结果：SUCCESS：成功  FAIL：失败"`
	ErrMsg                   string `p:"errMsg"  dc:"失败原因"`
}

// AdBatchTaskDetailEditReq 修改操作请求参数
type AdBatchTaskDetailEditReq struct {
	Id                       int64  `p:"id" v:"required#主键ID不能为空" dc:""`
	TaskId                   string `p:"taskId"  dc:"任务ID"`
	SerialNumber             int    `p:"serialNumber"  dc:"序号 任务中的执行顺序"`
	AdvertiserId             string `p:"advertiserId"  dc:"媒体账户ID"`
	OriginalAdvertiserName   string `p:"originalAdvertiserName" v:"required#原账户名称不能为空" dc:"原账户名称"`
	NewAdvertiserName        string `p:"newAdvertiserName" v:"required#新账户名称不能为空" dc:"新账户名称"`
	OriginalAdvertiserRemark string `p:"originalAdvertiserRemark"  dc:"原账户备注"`
	NewAdvertiserRemark      string `p:"newAdvertiserRemark"  dc:"新账户备注"`
	OptResult                string `p:"optResult"  dc:"执行结果：SUCCESS：成功  FAIL：失败"`
	ErrMsg                   string `p:"errMsg"  dc:"失败原因"`
}
