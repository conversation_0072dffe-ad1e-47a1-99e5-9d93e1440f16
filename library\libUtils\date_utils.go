package libUtils

import (
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// GetNowDate 获取当前日期
func GetNowDate() string {
	now := time.Now()
	date := fmt.Sprintf("%d-%02d-%02d", now.Year(), now.Month(), now.Day())
	return date
}

// SafeGo 安全的协程
func SafeGo(fn func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic: %v\n", r)
			}
		}()
		fn()
	}()
}
func ContainsVideoIds(input, target string) bool {
	// 使用逗号分割字符串成切片
	items := strings.Split(input, ",")
	// 遍历切片，检查是否包含目标字符串
	for _, item := range items {
		if strings.TrimSpace(item) == target {
			return true
		}
	}
	return false
}

// 获取当前最近一个小时的时间
func GetBeforeHour() (string, string, int, string) {
	now := time.Now()
	// 获取当前小时的开始时间
	currentHourStart := now.Truncate(time.Hour)

	// 计算上一个小时的开始时间和结束时间
	lastHourEnd := currentHourStart
	lastHourStart := lastHourEnd.Add(-1 * time.Hour) // 上一个小时的开始时间
	// 将时间格式化为 MySQL 可以识别的格式
	format := time.DateTime
	lastHourStartStr := lastHourStart.Format(format)
	lastHourEndStr := lastHourEnd.Format(format)
	return lastHourStartStr, lastHourEndStr, lastHourStart.Hour(), lastHourStart.Format(time.DateOnly)
}

func GetBeforeHour2(hour int) (string, string, int, string) {
	now := time.Now()
	// 获取当前小时的开始时间
	currentHourStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	currentHourStart = currentHourStart.Add(time.Duration(hour) * time.Hour)
	// 计算上一个小时的开始时间和结束时间
	lastHourEnd := currentHourStart
	lastHourStart := lastHourEnd.Add(-1 * time.Hour) // 上一个小时的开始时间
	// 将时间格式化为 MySQL 可以识别的格式
	format := time.DateTime
	lastHourStartStr := lastHourStart.Format(format)
	lastHourEndStr := lastHourEnd.Format(format)
	return lastHourStartStr, lastHourEndStr, lastHourStart.Hour(), lastHourStart.Format(time.DateOnly)
}

func GetBeforeHourByDate(dateStr string, hour int) (string, string, int, string) {
	date, _ := ParseDateString(dateStr)
	// 获取当前小时的开始时间
	currentHourStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	currentHourStart = currentHourStart.Add(time.Duration(hour) * time.Hour)
	// 计算上一个小时的开始时间和结束时间
	lastHourEnd := currentHourStart
	lastHourStart := lastHourEnd.Add(-1 * time.Hour) // 上一个小时的开始时间
	// 将时间格式化为 MySQL 可以识别的格式
	format := time.DateTime
	lastHourStartStr := lastHourStart.Format(format)
	lastHourEndStr := lastHourEnd.Add(-time.Nanosecond).Format(format)
	return lastHourStartStr, lastHourEndStr, lastHourStart.Hour(), lastHourStart.Format(time.DateOnly)
}

// GetDayStartEnd 根据传入时间生成开始时间和结束时间
func GetDayStartEnd(dateStr string) (string, string, error) {
	// Parse the input date string
	parsedDate, err := time.Parse(time.DateOnly, dateStr)
	if err != nil {
		return "", "", err
	}

	// Get the start of the day
	dayStart := parsedDate.Format(time.DateTime)

	// Get the start of the next day
	dayEnd := parsedDate.Add(24 * time.Hour).Format(time.DateTime)
	return dayStart, dayEnd, nil
}

// GetDateTimeRanges 获取日期范围
func GetDateTimeRanges(timeType uint8) (string, string) {
	// 获取当前时间
	now := time.Now()

	// 获取东八区时区
	location, _ := time.LoadLocation("Asia/Shanghai")

	var startTime, endTime time.Time

	// 今日的日期范围
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	todayEnd := todayStart.AddDate(0, 0, 1).Add(-time.Nanosecond)

	if timeType == 1 {
		startTime = todayStart
		endTime = todayEnd
	} else if timeType == 2 {
		// 昨日的日期范围
		startTime = todayStart.AddDate(0, 0, -1)
		endTime = todayStart.Add(-time.Nanosecond)
	} else if timeType == 3 {
		// 本周的日期范围
		weekday := int(now.Weekday())
		startTime = todayStart.AddDate(0, 0, -(weekday - 1))
		endTime = startTime.AddDate(0, 0, 7).Add(-time.Nanosecond)
	} else if timeType == 4 {
		// 本月的日期范围
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, location)
		nextMonthStart := startTime.AddDate(0, 1, 0)
		endTime = nextMonthStart.Add(-time.Nanosecond)
	}

	// 格式化日期范围为字符串
	startTimeStr := startTime.Format(time.DateOnly)
	endTimeStr := endTime.Format(time.DateOnly)

	return startTimeStr, endTimeStr
}

func GetDateTimeStrByStr(startDate string) string {
	startTime, _ := time.Parse(time.DateOnly, startDate)
	dayStart := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())
	return dayStart.Format(time.DateTime)
}

// GetDayTimestamps 根据传入的日期字符串（格式："2006-01-02"），
// 返回当天开始和结束时间的13位毫秒级时间戳。
// 例如，输入 "2025-03-06"，则返回当天 00:00:00.000 和 23:59:59.999 对应的时间戳。
func GetDayTimestamps(dateStr string) (start, end int64, err error) {
	// 使用本地时区解析日期字符串
	t, err := time.ParseInLocation("2006-01-02", dateStr, time.Local)
	if err != nil {
		return 0, 0, fmt.Errorf("无法解析日期字符串: %v", err)
	}

	// 构造当天的开始时间（00:00:00.000）
	startTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	// 构造当天的结束时间（23:59:59.999）
	endTime := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999, time.Local)

	// 将时间转换为Unix时间戳（单位：毫秒，13位）
	start = startTime.UnixNano() / 1e6
	end = endTime.UnixNano() / 1e6

	return start, end, nil
}

func GetDayTimestamps3(dateStr string) (start, end int64, err error) {
	start, end, err = GetDayTimestamps(dateStr)
	return start / 1000, end / 1000, err
}

// GetDayTimestamps 根据传入的日期字符串（格式："2006-01-02"），
// 返回当天的起始和结束时间戳（秒级和毫秒级）。
// - 起始时间：当天 00:00:00（含）
// - 结束时间：次日 00:00:00（不含）
func GetDayTimestamps2(dateStr string) (startSec, endSec, startMs, endMs int64, err error) {
	// 使用本地时区解析日期字符串
	t, err := time.ParseInLocation("2006-01-02", dateStr, time.Local)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("无法解析日期字符串: %v", err)
	}

	// 当天 00:00:00
	startTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)

	// 次日 00:00:00（作为当天的“结束时间点”，非含义上的“最后一秒”）
	endTime := startTime.Add(24 * time.Hour)

	// 秒级时间戳
	startSec = startTime.Unix()
	endSec = endTime.Unix()

	// 毫秒级时间戳
	startMs = startTime.UnixNano() / 1e6
	endMs = endTime.UnixNano() / 1e6

	return
}

// GetPreviousFiveMinuteTimestamps 获取当前时间前一个完整 5 分钟段的起止时间戳（秒和毫秒）。
// 例如当前为 10:10:xx，则返回时间段为 10:00:00 ~ 10:05:00
func GetPreviousFiveMinuteTimestamps() (startSec, endSec, startMs, endMs int64) {
	now := time.Now().In(time.Local)

	// 去掉秒和纳秒
	trimmedNow := now.Truncate(time.Minute)

	// 向前取整到 5 分钟段（例如 10:10 -> 10:10, 10:07 -> 10:05）
	minute := trimmedNow.Minute()
	roundedMinute := minute - (minute % 5)
	roundedTime := time.Date(trimmedNow.Year(), trimmedNow.Month(), trimmedNow.Day(),
		trimmedNow.Hour(), roundedMinute, 0, 0, time.Local)

	// 上一个 5 分钟段的开始和结束
	endTime := roundedTime
	startTime := endTime.Add(-5 * time.Minute)

	// 秒级时间戳
	startSec = startTime.Unix()
	endSec = endTime.Unix()

	// 毫秒级时间戳
	startMs = startTime.UnixNano() / 1e6
	endMs = endTime.UnixNano() / 1e6

	return
}

func GetTwoStepsBackFiveMinuteWindow() (startSec, endSec, startMs, endMs int64) {
	now := time.Now().In(time.Local)

	// 向下取整到分钟
	trimmedNow := now.Truncate(time.Minute)

	// 当前分钟整除 5 向下取整
	minute := trimmedNow.Minute()
	roundedMinute := minute - (minute % 5)

	// 当前5分钟段的开始时间
	currentSegmentStart := time.Date(
		trimmedNow.Year(), trimmedNow.Month(), trimmedNow.Day(),
		trimmedNow.Hour(), roundedMinute, 0, 0, time.Local)

	// 回退两个段，得到目标时间段
	endTime := currentSegmentStart.Add(-5 * time.Minute) // 例如 18:15
	startTime := endTime.Add(-5 * time.Minute)           // 例如 18:10

	// 时间戳（秒级 + 毫秒级）
	startSec = startTime.Unix()
	endSec = endTime.Unix()
	startMs = startTime.UnixNano() / 1e6
	endMs = endTime.UnixNano() / 1e6
	return
}

func GetDayTimestampsStr(dateStr string) (startStr, endStr string, err error) {
	// 使用本地时区解析日期字符串
	start, end, err := GetDayTimestamps(dateStr)
	startStr, endStr = gconv.String(start), gconv.String(end)
	return
}

func GetDateTimeStr(inTime time.Time) string {
	return inTime.Format(time.DateTime)
}

// GetDayStartAndEnd 获取指定日期的一天开始时间和结束时间
func GetDayStartAndEnd(startDate string, endDate string) (string, string) {
	startTime, _ := time.Parse(time.DateOnly, startDate)
	endTime, _ := time.Parse(time.DateOnly, endDate)

	// 获取startDate开始时间（即 00:00:00）
	dayStart := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())

	// 获取endDate结束时间（即 23:59:59）
	dayEnd := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, endTime.Location()).
		AddDate(0, 0, 1).Add(-time.Nanosecond)

	// 格式化日期范围为字符串
	startTimeStr := dayStart.Format(time.DateTime)
	endTimeStr := dayEnd.Format(time.DateTime)
	return startTimeStr, endTimeStr
}
func GetCurrentTimeString() string {
	// 获取当前时间
	currentTime := time.Now()

	// 格式化日期为字符串
	dateString := currentTime.Format(time.DateTime)

	return fmt.Sprintf("'%s'", dateString)
}
func GetCurrentTime() string {
	// 获取当前时间
	currentTime := time.Now()

	// 格式化日期为字符串
	dateString := currentTime.Format(time.DateTime)

	return fmt.Sprintf("%s", dateString)
}

func GetYeasterCurrentTime() string {
	// 获取当前时间
	currentTime := time.Now()
	currentTime = currentTime.Add(-24 * time.Hour)
	// 格式化日期为字符串
	dateString := currentTime.Format(time.DateTime)

	return fmt.Sprintf("%s", dateString)
}

// AreDatesEqual 判断两个时间类型的time 是否是同一天
func AreDatesEqual(time1, time2 time.Time) bool {
	// 将时间部分截断，只保留日期部分
	date1 := time1.Truncate(24 * time.Hour)
	date2 := time2.Truncate(24 * time.Hour)

	// 比较日期部分是否相等
	return date1.Equal(date2)
}

// MinDateString 获取两个string 类型的time 更小的那个string 报错默认返回第一个
func MinDateString(dateString1, dateString2 string) (string, error) {
	time1, err := ParseDateString(dateString1)
	if err != nil {
		return dateString1, err
	}
	time2, err := ParseDateString(dateString2)
	if err != nil {
		return dateString1, err
	}
	if time1.Before(time2) {
		return dateString1, nil
	} else {
		return dateString2, nil
	}
}

// GetDayStartEnd 根据传入时间生成开始时间和结束时间
//func GetDayStartEnd(dateStr string) (string, string, error) {
//	// Parse the input date string
//	parsedDate, err := time.Parse(time.DateOnly, dateStr)
//	if err != nil {
//		return "", "", err
//	}
//
//	// Get the start of the day
//	dayStart := parsedDate.Format(time.DateTime)
//
//	// Get the start of the next day
//	dayEnd := parsedDate.Add(24 * time.Hour).Format(time.DateTime)
//	return dayStart, dayEnd, nil
//}

// CompareDateString 参考time compare方法
func CompareDateString(dateString1, dateString2 string) int {
	time1, err := ParseDateString(dateString1)
	if err != nil {
		return -1
	}
	time2, err := ParseDateString(dateString2)
	if err != nil {

		return 1
	}
	return time1.Compare(time2)
}

// FindMinMaxTimeStrings 从timeString list 中获取最小的time 和最大的time string
func FindMinMaxTimeStrings(timeStrings []string) (string, string, error) {
	if len(timeStrings) == 0 {
		return "", "", fmt.Errorf("空的时间字符串列表")
	}

	// 初始化最小和最大时间的索引
	minIndex, maxIndex := 0, 0
	// 遍历列表，找到最小和最大时间的索引
	for i, timeStr := range timeStrings[1:] {
		currTime, err := ParseDateString(timeStr)
		if err != nil {
			return "", "", err
		}
		minTime, _ := ParseDateString(timeStrings[minIndex])
		maxTime, _ := ParseDateString(timeStrings[maxIndex])

		if currTime.Before(minTime) {
			minIndex = i + 1
		} else if currTime.After(maxTime) {
			maxIndex = i + 1
		}
	}
	// 返回时间字符串
	minTimeString := timeStrings[minIndex]
	maxTimeString := timeStrings[maxIndex]
	return minTimeString, maxTimeString, nil
}

// MaxDateString 获取两个string 类型的time 更大的那个string 报错默认返回第一个
func MaxDateString(dateString1, dateString2 string) (string, error) {
	time1, err := ParseDateString(dateString1)
	if err != nil {
		return dateString1, err
	}
	time2, err := ParseDateString(dateString2)
	if err != nil {
		return dateString1, err
	}
	if time1.After(time2) {
		return dateString1, nil
	} else {
		return dateString2, nil
	}
}
func ParseDateString(dateString string) (time.Time, error) {
	for _, layout := range predefinedLayouts {
		resultTime, err := time.Parse(layout, dateString)
		if err == nil {
			return resultTime, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析日期字符串：%s", dateString)
}

func ParseDateString2(dateString string) time.Time {
	for _, layout := range predefinedLayouts {
		resultTime, err := time.Parse(layout, dateString)
		if err == nil {
			return resultTime
		}
	}

	return time.Time{}
}

func UintToString(num uint64) string {
	return strconv.FormatUint(num, 10)
}

// StringTimeAddDay string 类型的字符串添加天数 返回添加天数之后的string 字符串
func StringTimeAddDay(dateString string, day int) string {
	for _, layout := range predefinedLayouts {
		resultTime, err := time.Parse(layout, dateString)
		if err == nil {
			resultTime = resultTime.AddDate(0, 0, day)
			return resultTime.Format(layout)
		}
	}
	return dateString
}

// GetNowZhDateString 获取中文的年月日的时间
func GetNowZhDateString() string {
	// 获取当前时间
	currentTime := time.Now()

	// 定义自定义时间格式
	const layout = "2006年1月2日"

	// 将当前时间格式化为指定格式
	return currentTime.Format(layout)
}

// GetDateString 只获取时间的date部分
func GetDateString(date string) string {
	dateRegex := regexp.MustCompile(`^\d{4}-\d{1,2}-\d{1,2}`)
	return dateRegex.FindString(date)
}

// GetCurrentDateString 获取当天的时间date
func GetCurrentDateString() string {
	// 获取当前时间
	currentTime := time.Now()

	// 格式化日期为字符串
	dateString := currentTime.Format(time.DateOnly)

	return fmt.Sprintf("'%s'", dateString)
}

// GetYesterdayDateString 获取昨天的时间date
func GetYesterdayDateString() string {
	// 获取当前时间
	currentTime := time.Now()

	// 获取昨天的时间
	yesterdayTime := currentTime.Add(-24 * time.Hour)

	// 格式化日期为字符串
	dateString := yesterdayTime.Format(time.DateOnly)

	return fmt.Sprintf("'%s'", dateString)
}

func DelSingleQuotes(idx string) string {
	return strings.Replace(idx, "'", "", -1)
}

// GetLastMonthDateRange 上个月的第一天 上个月的最后一天
func GetLastMonthDateRange() (string, string) {
	// 获取当前时间
	currentTime := time.Now()

	// 计算上个月的第一天
	firstDayOfLastMonth := time.Date(currentTime.Year(), currentTime.Month()-1, 1, 0, 0, 0, 0, currentTime.Location())

	// 计算上个月的最后一天
	lastDayOfLastMonth := firstDayOfLastMonth.AddDate(0, 1, -1)

	// 格式化日期为字符串
	firstDayString := firstDayOfLastMonth.Format(time.DateOnly)
	lastDayString := lastDayOfLastMonth.Format(time.DateOnly)

	return fmt.Sprintf("'%s'", firstDayString), fmt.Sprintf("'%s'", lastDayString)
}

// GetCurrentMonthDateRange 本月的第一天 本月的最后一天
func GetCurrentMonthDateRange() (string, string) {
	// 获取当前时间
	currentTime := time.Now()

	// 获取本月的第一天
	firstDayOfCurrentMonth := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, currentTime.Location())

	// 获取下个月的第一天
	firstDayOfNextMonth := firstDayOfCurrentMonth.AddDate(0, 1, 0)

	// 获取本月的最后一天
	lastDayOfCurrentMonth := firstDayOfNextMonth.Add(-24 * time.Hour)

	// 格式化日期为字符串
	firstDayString := firstDayOfCurrentMonth.Format(time.DateOnly)
	lastDayString := lastDayOfCurrentMonth.Format(time.DateOnly)

	return fmt.Sprintf("'%s'", firstDayString), fmt.Sprintf("'%s'", lastDayString)
}

// GetCurrentEarliestTime 获取当天最早时间
func GetCurrentEarliestTime() string {
	startTime := gtime.Now().Time

	dayStart := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())

	startTimeStr := dayStart.Format(time.DateTime)
	return startTimeStr
}
func GetCurrentLatestTime() string {
	startTime := gtime.Now().Time

	dayStart := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, startTime.Location())

	startTimeStr := dayStart.Format(time.DateTime)
	return startTimeStr
}
func GetYesterDayEarliestTime() string {
	startTime := time.Now()
	yesterdayTime := startTime.Add(-24 * time.Hour)
	dayStart := time.Date(yesterdayTime.Year(), yesterdayTime.Month(), yesterdayTime.Day(), 0, 0, 0, 0, yesterdayTime.Location())

	startTimeStr := dayStart.Format(time.DateTime)
	return startTimeStr
}
func GetYesterDayLatestTime() string {
	startTime := time.Now()
	yesterdayTime := startTime.Add(-24 * time.Hour)
	dayStart := time.Date(yesterdayTime.Year(), yesterdayTime.Month(), yesterdayTime.Day(), 23, 59, 59, 0, yesterdayTime.Location())

	startTimeStr := dayStart.Format(time.DateTime)
	return startTimeStr
}

var predefinedLayouts = []string{
	time.DateTime,
	time.DateOnly,
	time.TimeOnly,
	"02 Jan 2006",
	"2006-01-02 15:04:05 -0700",
	time.Layout,
	time.ANSIC,
	time.UnixDate,
	time.RubyDate,
	time.RFC822,
	time.RFC822Z,
	time.RFC850,
	time.RFC1123,
	time.RFC1123Z,
	time.RFC3339,
	time.RFC3339Nano,
	time.Kitchen,
	time.Stamp,
	time.StampMilli,
	time.StampMicro,
	time.StampNano,
}

// 根据当前秒时间戳获取当前日期部分字符串
func GetDateBySec(sec int64) string {
	if sec > 0 {
		t := time.Unix(sec, 0)
		localDate := t.In(time.Local)
		return localDate.Format(time.DateOnly)
	} else {
		return ""
	}
}

// GetDateBySec2 将秒级时间戳转日期时分秒字符串
func GetDateBySec2(sec int64) string {
	if sec > 0 {
		t := time.Unix(sec, 0)
		localDate := t.In(time.Local)
		return localDate.Format(time.DateTime)
	} else {
		return ""
	}
}

// 判断小程序类型 判断openid是否wx前缀
func IsWxOpenId(openId string) bool {
	return strings.HasPrefix(openId, "wx")
}

// 判断是否是tt
func IsTtOpenId(openId string) bool {
	return strings.HasPrefix(openId, "tt")
}

func GetAppTypeByOpenId(openId string) string {
	if IsWxOpenId(openId) {
		return "微小"
	} else if IsTtOpenId(openId) {
		return "抖小"
	} else {
		return "其他"
	}
}

// 获取现在到凌晨的时间秒
func SecondsUntilTomorrow() int64 {
	now := time.Now()
	// 构造明天凌晨 00:00:00 时间
	tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	// 计算秒数差
	seconds := tomorrow.Unix() - now.Unix()
	return seconds
}

// 根据当前时间字符串转秒级别时间戳 使用本地时间不是东八度时间

func GetSecByDate(dateStr string) int64 {
	// 将字符串解析为时间类型
	date, _ := time.ParseInLocation("2006-01-02", dateStr, time.Local)

	// 获取秒级别的时间戳
	sec := date.Unix()

	return sec
}

func GetSecByDateTime(dateStr string) int64 {
	// 将字符串解析为时间类型
	date, err := time.ParseInLocation(time.DateTime, dateStr, time.Local)
	if err != nil {
		date, _ = time.ParseInLocation(time.DateOnly, dateStr, time.Local)
	}
	// 获取秒级别的时间戳
	sec := date.Unix()

	return sec
}
func GetYesterdayDate() string {
	// 获取当前时间
	currentTime := time.Now()

	// 获取昨天的时间
	yesterdayTime := currentTime.Add(-24 * time.Hour)

	// 格式化日期为字符串
	dateString := yesterdayTime.Format(time.DateOnly)

	return fmt.Sprintf("%s", dateString)
}

func PlusDays(dateStr string, days int64) string {
	format := "2006-01-02"
	// 将字符串解析为时间类型
	date, _ := time.Parse(format, dateStr)

	// 将日期加days天
	duration := time.Duration(days) * 24 * time.Hour
	newDate := date.Add(duration)

	// 格式化输出结果
	return newDate.Format(format)
}

// CalcNextDeductionTimeRange 计算下次扣款时间范围
func CalcNextDeductionTimeRange(signTime *gtime.Time) (startTime *gtime.Time, endTime *gtime.Time) {
	var now = gtime.Now()
	cycle := now.Sub(signTime.StartOfDay()).Hours() / 24 / 30
	// 当前是第几个周期
	curCycle := int(cycle) + 1
	nextSubTime := signTime.StartOfDay().AddDate(0, 0, curCycle*30)
	// 下次扣款开始时间为当前周期的开始时间的前一天
	startTime = nextSubTime.StartOfDay().AddDate(0, 0, -1)
	// 下次扣款结束时间为当前周期的开始时间加6天
	endTime = nextSubTime.StartOfDay().AddDate(0, 0, 6)
	// 如果下次扣款时间距离现在超过7天，则将下次扣款时间开始时间设置为nextSubTime的前31天
	if int(nextSubTime.Sub(now).Hours()/24) > 7 {
		startTime = nextSubTime.AddDate(0, -1, 0)
		endTime = startTime.StartOfDay().AddDate(0, 0, 6)
	}
	return
}

func GetNexSubTime(signTime *gtime.Time) *gtime.Time {
	var now = gtime.Now()
	cycle := now.Sub(signTime.StartOfDay()).Hours() / 24 / 30
	// 当前是第几个周期
	curCycle := int(cycle) + 1
	nextSubTime := signTime.StartOfDay().AddDate(0, 0, curCycle*30)
	if int(nextSubTime.Sub(now).Hours()/24) > 7 {
		nextSubTime = nextSubTime.AddDate(0, -1, 0)
	}
	return nextSubTime
}

// IsEndTimeThreeDaysAgo 判断当前时间是否是传入时间的3天前
func IsEndTimeThreeDaysAgo(endTime *gtime.Time) bool {
	// 获取 endTime 的本地时区零点日期
	endDate := time.Date(endTime.Year(), time.Month(endTime.Month()), endTime.Day(), 0, 0, 0, 0, endTime.Time.Local().Location())

	// 获取当前本地时区的零点日期
	now := time.Now().Local()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	threeDaysAgo := today.AddDate(0, 0, 3)

	// 比较日期
	return endDate.Equal(threeDaysAgo)
}

// GetBetweenDays 获取两个时间间隔天数
func GetBetweenDays(startDate, endDate string) int {
	startTime, _ := ParseDateString(startDate)
	endTime, _ := ParseDateString(endDate)
	// 获取两个时间对象的差值
	diff := endTime.Sub(startTime)
	// 将差值转换为小时数
	return int(diff.Hours())/24 + 1
}

// GetCreateMonth 年月日格式转年月格式
func GetCreateMonth(startDate string) string {
	startTime, _ := ParseDateString(startDate)
	return startTime.Format("2006-01")
}
