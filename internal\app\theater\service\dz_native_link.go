// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/service/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

type IDzNativeLink interface {
	List(ctx context.Context, req *model.DzNativeLinkSearchReq) (res *model.DzNativeLinkSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.DzNativeLinkInfoRes, err error)
	Add(ctx context.Context, req *model.DzNativeLinkAddReq) (err error)
	Edit(ctx context.Context, req *model.DzNativeLinkEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
	DzNativeLinkCrawlTask(ctx context.Context)
	DzNativeLinkCrawl(ctx context.Context) (err error)
}

var localDzNativeLink IDzNativeLink

func DzNativeLink() IDzNativeLink {
	if localDzNativeLink == nil {
		panic("implement not found for interface IDzNativeLink, forgot register?")
	}
	return localDzNativeLink
}

func RegisterDzNativeLink(i IDzNativeLink) {
	localDzNativeLink = i
}
