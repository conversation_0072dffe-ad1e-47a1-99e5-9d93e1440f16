// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-11 13:50:11
// 生成路径: internal/app/ad/logic/ad_common_asset_title.go
// 生成人：cq
// desc:通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdCommonAssetTitle(New())
}

func New() service.IAdCommonAssetTitle {
	return &sAdCommonAssetTitle{}
}

type sAdCommonAssetTitle struct{}

func (s *sAdCommonAssetTitle) List(ctx context.Context, req *model.AdCommonAssetTitleSearchReq) (listRes *model.AdCommonAssetTitleSearchRes, err error) {
	listRes = new(model.AdCommonAssetTitleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdCommonAssetTitle.Ctx(ctx).As("ad").LeftJoin("sys_user u", "ad.user_id = u.id")
		if len(req.UserIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetTitle.Columns().UserId, req.UserIds)
		} else if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetTitle.Columns().UserId, userIds)
		}
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetTitle.Columns().UserId, userIds)
		}
		if req.Title != "" {
			m = m.WhereLike("ad."+dao.AdCommonAssetTitle.Columns().Title, "%"+req.Title+"%")
		}
		if req.CategoryIds != nil && len(req.CategoryIds) > 0 {
			var condition string
			for index, categoryId := range req.CategoryIds {
				if index == 0 {
					condition += fmt.Sprintf("FIND_IN_SET('%v', ad.category_ids) > 0", categoryId)
				} else {
					condition += " OR " + fmt.Sprintf("FIND_IN_SET('%v', ad.category_ids) > 0", categoryId)
				}
			}
			m = m.Where(condition)
		}
		if req.TitleIds != nil && len(req.TitleIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetTitle.Columns().Id, req.TitleIds)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdCommonAssetTitleListRes
		err = m.Fields("ad.id as id").
			Fields("ad.title as title").
			Fields("ad.category_ids as categoryIds").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ad.last_3_day_click_rate as last3DayClickRate").
			Fields("ad.last_3_day_cost as last3DayCost").
			Fields("ad.history_click_rate as historyClickRate").
			Fields("ad.history_cost as historyCost").
			Fields("ad.ad_count as adCount").
			Fields("ad.created_at as createdAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdCommonAssetTitleListRes, len(res))
		for k, v := range res {
			assetTitle := &model.AdCommonAssetTitleListRes{
				Id:                v.Id,
				Title:             v.Title,
				CategoryIds:       v.CategoryIds,
				UserId:            v.UserId,
				UserName:          v.UserName,
				Last3DayClickRate: v.Last3DayClickRate,
				Last3DayCost:      v.Last3DayCost,
				HistoryClickRate:  v.HistoryClickRate,
				HistoryCost:       v.HistoryCost,
				AdCount:           v.AdCount,
				CreatedAt:         v.CreatedAt,
			}
			categoryRes, _ := service.AdCommonAssetCategory().List(ctx, &model.AdCommonAssetCategorySearchReq{
				CategoryIds: gconv.Ints(strings.Split(v.CategoryIds, commonConsts.Delimiter)),
			})
			assetTitle.CategoryList = categoryRes.List
			listRes.List[k] = assetTitle
		}
	})
	return
}

// GetRandomTitles
func (s *sAdCommonAssetTitle) GetRandomTitles(ctx context.Context, req *model.AdCommonAssetTitleGetRandomTitlesReq) (listRes *model.AdCommonAssetTitleSearchRes, err error) {
	listRes = new(model.AdCommonAssetTitleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdCommonAssetTitle.Ctx(ctx).As("ad").LeftJoin("sys_user u", "ad.user_id = u.id")

		if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetTitle.Columns().UserId, userIds)
		}
		//if req.Title != "" {
		//	m = m.WhereLike("ad."+dao.AdCommonAssetTitle.Columns().Title, "%"+req.Title+"%")
		//}

		if req.CategoryId > 0 {
			var condition string
			condition += fmt.Sprintf("FIND_IN_SET('%v', ad.category_ids) > 0", req.CategoryId)
			m = m.Where(condition)
		}

		// 添加redis缓存 一天过期
		key := fmt.Sprintf("ad_common_asset_title_used_%v_%v", libUtils.GetNowDate(), userInfo.Id)
		ids := make([]int, 0)
		if req.ExcludeTodayIsUsed == true {
			val := commonService.Cache().Get(ctx, key)
			if val != nil {
				todayUsedIds := val.String()
				ids = gconv.Ints(strings.Split(todayUsedIds, commonConsts.Delimiter))
			}
			if len(ids) > 0 {
				m = m.WhereNotIn("ad."+dao.AdCommonAssetTitle.Columns().Id, ids)
			}
		}

		if len(req.StartDate) > 0 {
			m = m.Where("ad."+dao.AdCommonAssetTitle.Columns().CreatedAt+" >= ?", req.StartDate)
		}

		if len(req.EndDate) > 0 {
			m = m.Where("ad."+dao.AdCommonAssetTitle.Columns().CreatedAt+" <= ?", libUtils.StringTimeAddDay(req.EndDate, 1))
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		if req.LimitNum > 0 {
			req.PageSize = req.LimitNum
		}
		order := "RAND()"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdCommonAssetTitleListRes
		err = m.Fields("ad.id as id").
			Fields("ad.title as title").
			Fields("ad.category_ids as categoryIds").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ad.last_3_day_click_rate as last3DayClickRate").
			Fields("ad.last_3_day_cost as last3DayCost").
			Fields("ad.history_click_rate as historyClickRate").
			Fields("ad.history_cost as historyCost").
			Fields("ad.ad_count as adCount").
			Fields("ad.created_at as createdAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdCommonAssetTitleListRes, len(res))
		for k, v := range res {
			assetTitle := &model.AdCommonAssetTitleListRes{
				Id:                v.Id,
				Title:             v.Title,
				CategoryIds:       v.CategoryIds,
				UserId:            v.UserId,
				UserName:          v.UserName,
				Last3DayClickRate: v.Last3DayClickRate,
				Last3DayCost:      v.Last3DayCost,
				HistoryClickRate:  v.HistoryClickRate,
				HistoryCost:       v.HistoryCost,
				AdCount:           v.AdCount,
				CreatedAt:         v.CreatedAt,
			}
			if req.ExcludeTodayIsUsed {
				ids = append(ids, v.Id)
			}

			categoryRes, _ := service.AdCommonAssetCategory().List(ctx, &model.AdCommonAssetCategorySearchReq{
				CategoryIds: gconv.Ints(strings.Split(v.CategoryIds, commonConsts.Delimiter)),
			})
			assetTitle.CategoryList = categoryRes.List
			listRes.List[k] = assetTitle
		}

		if req.ExcludeTodayIsUsed {
			commonService.Cache().Set(ctx, key, strings.Join(gconv.Strings(ids), commonConsts.Delimiter), time.Duration(libUtils.SecondsUntilTomorrow())*time.Second)
		}
	})
	return
}

func (s *sAdCommonAssetTitle) GetExportData(ctx context.Context, req *model.AdCommonAssetTitleSearchReq) (listRes []*model.AdCommonAssetTitleListRes, err error) {
	res, _ := s.List(ctx, req)
	listRes = res.List
	return
}

func (s *sAdCommonAssetTitle) GetById(ctx context.Context, id int) (res *model.AdCommonAssetTitleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdCommonAssetTitle.Ctx(ctx).WithAll().Where(dao.AdCommonAssetTitle.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		categoryRes, _ := service.AdCommonAssetCategory().List(ctx, &model.AdCommonAssetCategorySearchReq{
			CategoryIds: gconv.Ints(strings.Split(res.CategoryIds, commonConsts.Delimiter)),
		})
		res.CategoryList = categoryRes.List
	})
	return
}

func (s *sAdCommonAssetTitle) Add(ctx context.Context, req *model.AdCommonAssetTitleAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var categoryIds string
		if req.CategoryIds != nil && len(req.CategoryIds) > 0 {
			categoryIds = strings.Join(gconv.Strings(req.CategoryIds), commonConsts.Delimiter)
		}
		user := sysService.Context().GetLoginUser(ctx)
		assetTitles := make([]*do.AdCommonAssetTitle, 0)
		for _, title := range req.Titles {
			assetTitles = append(assetTitles, &do.AdCommonAssetTitle{
				Title:       title,
				CategoryIds: categoryIds,
				UserId:      user.Id,
			})
		}
		_, err = dao.AdCommonAssetTitle.Ctx(ctx).Data(assetTitles).Insert()
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdCommonAssetTitle) AddAndGetId(ctx context.Context, req *model.AdCommonAssetTitleAddReq) (id int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		id, err = dao.AdCommonAssetTitle.Ctx(ctx).InsertAndGetId(do.AdCommonAssetTitle{
			Title:  req.Titles[0],
			UserId: req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdCommonAssetTitle) Edit(ctx context.Context, req *model.AdCommonAssetTitleEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.Ids == nil || len(req.Ids) == 0 {
			return
		}
		assetTitle := do.AdCommonAssetTitle{}
		if req.Titles != nil && len(req.Titles) > 0 {
			assetTitle.Title = req.Titles[0]
		}
		if req.CategoryIds != nil && len(req.CategoryIds) > 0 {
			assetTitle.CategoryIds = strings.Join(gconv.Strings(req.CategoryIds), commonConsts.Delimiter)
		}
		_, err = dao.AdCommonAssetTitle.Ctx(ctx).WhereIn(dao.AdCommonAssetTitle.Columns().Id, req.Ids).Update(assetTitle)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdCommonAssetTitle) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdCommonAssetTitle.Ctx(ctx).Delete(dao.AdCommonAssetTitle.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
