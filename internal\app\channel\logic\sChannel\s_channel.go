// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-01-23 15:45:10
// 生成路径: internal/app/system/logic/s_channel.go
// 生成人：cyao
// desc:渠道表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/redis/go-redis/v9"
	adModel "github.com/tiger1103/gfast/v3/internal/app/ad/model"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	dao2 "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model/entity"
	service2 "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/system/service"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterService "github.com/tiger1103/gfast/v3/internal/app/theater/service"
	dzApi "github.com/tiger1103/gfast/v3/library/advertiser/dz/api"
	fqApi "github.com/tiger1103/gfast/v3/library/advertiser/fq/api"
	fqModel "github.com/tiger1103/gfast/v3/library/advertiser/fq/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"github.com/xuri/excelize/v2"
	"math"
	"strconv"
	"strings"
	"time"
)

func init() {
	service2.RegisterSChannel(New())
}

func New() service2.ISChannel {
	return &sSChannel{}
}

type sSChannel struct{}

// BatchEdit 单个/批量修改渠道
func (s *sSChannel) BatchEdit(ctx context.Context, req *model.SChannelBatchEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.List) > 0 {
			// 校验广告回传价格区间
			if req.List[0].AdSetting != nil && req.List[0].AdSetting.Id != 0 {
				err = service2.AdSetting().CheckPriceRange(req.List[0].AdSetting)
				liberr.ErrIsNil(ctx, err)
			}
			// 使用回传模板
			var adSettingTemplate *model.AdSettingTemplateInfoRes
			if req.List[0].AdSettingTemplateId != nil && *req.List[0].AdSettingTemplateId != 0 {
				adSettingTemplate, err = service2.AdSettingTemplate().GetById(ctx, *req.List[0].AdSettingTemplateId)
				liberr.ErrIsNil(ctx, err)
			}

			var editChannelList []*model.SChannelEditReq
			// 导流链接配置列表
			var adDiversionLinkEditList []*model.AdDiversionLinkEditReq
			var adDiversionLinkAddList []*model.AdDiversionLinkAddReq

			//插入数据
			for _, v := range req.List {
				//渠道数据
				addChannel := new(model.SChannelEditReq)
				addChannel.Id = v.Id
				addChannel.BatchEdit = true
				addChannel.Updatetime = gtime.Now()
				if v.BuyPriceGold != "" {
					addChannel.BuyPriceGold = v.BuyPriceGold
				}
				if v.LockNum != "" {
					addChannel.LockNum = v.LockNum
				}
				if v.TemplateId != "" {
					templateIdInt, _ := strconv.Atoi(v.TemplateId)
					addChannel.TemplateId = templateIdInt
				}
				if v.AdSettingTemplateId != nil {
					addChannel.AdSettingTemplateId = v.AdSettingTemplateId
				}
				if v.Remark != "" {
					addChannel.Remark = v.Remark
				}
				if v.OpenRetainWindow != nil {
					addChannel.OpenRetainWindow = v.OpenRetainWindow
				}
				if v.RetainTitle != "" {
					addChannel.RetainTitle = v.RetainTitle
				}
				if v.RetainUnlockEpisodesNum > 0 {
					addChannel.RetainUnlockEpisodesNum = v.RetainUnlockEpisodesNum
				}
				if v.RetainDayViewsNum > 0 {
					addChannel.RetainDayViewsNum = v.RetainDayViewsNum
				}
				if v.RetainWindowSeconds > 0 {
					addChannel.RetainWindowSeconds = v.RetainWindowSeconds
				}
				if v.ContinuousIaaRetain != nil {
					addChannel.ContinuousIaaRetain = v.ContinuousIaaRetain
				}
				if v.ContinuousUnlockInfo != nil {
					addChannel.ContinuousUnlockInfo = v.ContinuousUnlockInfo
				}
				editChannelList = append(editChannelList, addChannel)
				// 导流链接数据
				if v.AdLinkConfig != nil {
					v.AdLinkConfig.Account = v.ChannelCode
					if v.AdLinkConfig.Id > 0 {
						adDiversionLinkEditList = append(adDiversionLinkEditList, v.AdLinkConfig)
					} else {
						var adDiversionLinkAddReq *model.AdDiversionLinkAddReq
						_ = gconv.Struct(v.AdLinkConfig, &adDiversionLinkAddReq)
						adDiversionLinkAddList = append(adDiversionLinkAddList, adDiversionLinkAddReq)
					}
				}
			}
			//修改渠道信息
			for _, v := range editChannelList {
				s.Edit(ctx, v)
			}
			// 修改导流链接
			for _, v := range adDiversionLinkEditList {
				_ = service2.AdDiversionLink().Edit(ctx, v)
			}
			// 新增导流链接
			if len(adDiversionLinkAddList) > 0 {
				for _, addReq := range adDiversionLinkAddList {
					_ = service2.AdDiversionLink().Add(ctx, addReq)
				}
			}
			// 修改广告回传配置 不修改广告主ID
			for _, edit := range req.List {
				if edit.AdSetting == nil {
					continue
				}
				var adSettingId = edit.AdSetting.Id
				var rewardId = edit.AdSetting.RewardId
				var adSetting = edit.AdSetting
				if adSettingTemplate != nil {
					_ = gconv.Struct(adSettingTemplate.AdSetting, adSetting)
				}
				adSetting.Id = adSettingId
				adSetting.RewardId = rewardId
				adSetting.TemplateId = 0
				adSetting.AdvertiserIds = nil
				_ = service2.AdSetting().Edit(ctx, adSetting)
			}
		}

	})
	return
}

// BatchEditAdvertiserIds 批量修改广告主ID
func (s *sSChannel) BatchEditAdvertiserIds(ctx context.Context, req *model.SChannelBatchEditAdvertiserIdsReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, v := range req.List {
			err = service2.AdSetting().EditAdvertiserIds(ctx, &model.AdSettingEditAdverserIdsReq{
				AdvertiserIds: v.AdvertiserIds,
				AdSettingId:   v.AdSettingId,
			})
			liberr.ErrIsNil(ctx, err)
		}
	})
	return
}

// EditDzChannel 修改点众渠道
func (s *sSChannel) EditDzChannel(ctx context.Context, req *model.SChannelEditDzReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dzChannelInfo, _ := s.GetById(ctx, gconv.Int64(req.Id))
		if dzChannelInfo == nil {
			liberr.ErrIsNil(ctx, errors.New("渠道不存在"))
		}
		dzAdAccount, _ := adService.DzAdAccount().GetByAccountId(ctx, dzChannelInfo.DzAccountId)
		if dzAdAccount == nil {
			liberr.ErrIsNil(ctx, errors.New("点众账户不存在"))
		}
		channel := do.SChannel{
			Updatetime: gtime.Now(),
		}
		referralUpdateReq := dzApi.ReferralUpdateRequest{
			ReferralId: dzChannelInfo.DzReferralId,
			ChannelId:  dzChannelInfo.DzChannelId,
			ClientId:   dzChannelInfo.DzAccountId,
		}
		if req.DzRechargeTemplateId != nil {
			channel.DzRechargeTemplateId = req.DzRechargeTemplateId
			referralUpdateReq.BatchId = req.DzRechargeTemplateId
		}
		if req.DzRechargeTemplateName != nil {
			channel.DzRechargeTemplateName = req.DzRechargeTemplateName
		}
		if req.DzCallbackConfigId != nil {
			channel.DzCallbackConfigId = req.DzCallbackConfigId
			referralUpdateReq.CallbackConfigId = req.DzCallbackConfigId
		}
		if req.DzCallbackConfigName != nil {
			channel.DzCallbackConfigName = req.DzCallbackConfigName
		}
		if req.BuyPriceGold != nil {
			channel.BuyPriceGold = req.BuyPriceGold
			referralUpdateReq.ChapterKandian = req.BuyPriceGold
		}
		if req.LockNum != nil {
			channel.LockNum = req.LockNum
			referralUpdateReq.ChapterChargeStart = req.LockNum
		}
		_, err1 := dzApi.GetAdDZIClient().ReferralUpdateService.
			SetToken(dzAdAccount.Token).
			SetReq(referralUpdateReq).Do()
		liberr.ErrIsNil(ctx, err1)
		_, err1 = dao2.SChannel.Ctx(ctx).Where(dao2.SChannel.Columns().Id, req.Id).Update(channel)
		liberr.ErrIsNil(ctx, err1, "修改点众渠道失败")
	})
	return
}

func (s *sSChannel) GetChannelByChannelCodes(ctx context.Context, channelCodes []string) (response []*model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().WhereIn(dao2.SChannel.Columns().ChannelCode, channelCodes).Scan(&response)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

func (s *sSChannel) GetChannelByPromotionIds(ctx context.Context, promotionIds []string) (response []*model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().WhereIn(dao2.SChannel.Columns().FqPromotionId, promotionIds).Scan(&response)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

func (s *sSChannel) GetChannelByReferralIds(ctx context.Context, referralIds []string) (response []*model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().WhereIn(dao2.SChannel.Columns().DzReferralId, referralIds).Scan(&response)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

// BatchCopyAdd 批量新增渠道和复制
func (s *sSChannel) BatchCopyAdd(ctx context.Context, req *model.SChannelBatchAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		sourceChannelCode := commonConsts.DefaultChannel
		// 渠道复制，查询目标渠道的回传数据
		if req.SourceChannelCode != "" {
			sourceChannelCode = req.SourceChannelCode
		}
		sourceAdSetting, err1 := service2.AdSetting().GetByChannel(ctx, sourceChannelCode)
		liberr.ErrIsNil(ctx, err1)

		// 查询默认充值模板，没有传充值模板ID就用默认充值模板ID
		defaultRechargeTemplate, err2 := service2.RechargeTemplate().GetByName(ctx, commonConsts.DefaultChannel)
		liberr.ErrIsNil(ctx, err2)

		//判断渠道是否重复
		if len(req.List) > 0 {
			var channelCodes []string
			for _, v := range req.List {
				channelCodes = append(channelCodes, v.ChannelCode)
			}
			s.VerifyChannelCodes(ctx, channelCodes)
			// 批量新增校验广告主ID
			if sourceChannelCode == commonConsts.DefaultChannel {
				var advertiserList = make([]string, 0)
				for _, v := range req.List {
					if len(v.AdvertiserIds) > 0 {
						advertiserList = append(advertiserList, v.AdvertiserIds...)
					}
				}
				err = s.VerifyAdvertiserId(ctx, advertiserList)
				liberr.ErrIsNil(ctx, err)
			}

			var adSettingList []*model.AdSettingAddReq
			var addChannelList []*model.SChannelAddReq
			var adDiversionLinkList []*model.AdDiversionLinkAddReq
			//插入数据
			for _, v := range req.List {
				//渠道数据
				addChannel := new(model.SChannelAddReq)
				addChannel.ChannelCode = v.ChannelCode
				addChannel.Remark = v.Remark
				addChannel.Createtime = gtime.Now()
				addChannel.BuyPriceGold = v.BuyPriceGold
				addChannel.UserId = strconv.FormatUint(user.Id, 10)
				addChannel.LockNum = strconv.Itoa(v.LockNum)
				addChannel.OpenRetainWindow = v.OpenRetainWindow
				addChannel.RetainTitle = v.RetainTitle
				addChannel.RetainUnlockEpisodesNum = v.RetainUnlockEpisodesNum
				addChannel.RetainDayViewsNum = v.RetainDayViewsNum
				addChannel.RetainWindowSeconds = v.RetainWindowSeconds
				addChannel.ContinuousIaaRetain = v.ContinuousIaaRetain
				addChannel.ContinuousUnlockInfo = v.ContinuousUnlockInfo
				// 设置渠道的模板id
				if v.TemplateId > 0 {
					addChannel.TemplateId = v.TemplateId
				} else {
					addChannel.TemplateId = defaultRechargeTemplate.Id
				}
				addChannelList = append(addChannelList, addChannel)
				// 有回传模板查询回传模板，没有用自定义的，没有自定义的用默认模板
				var targetAdSetting *model.AdSettingListRes
				var adSettingAddReq = &model.AdSettingAddReq{}
				var adRewardSettingAddReq = &model.AdRewardSettingAddReq{}
				if v.AdSettingTemplateId != nil && *v.AdSettingTemplateId > 0 {
					addChannel.AdSettingTemplateId = *v.AdSettingTemplateId
					adSettingTemplate, _ := service2.AdSettingTemplate().GetById(ctx, *v.AdSettingTemplateId)
					if adSettingTemplate == nil {
						liberr.ErrIsNil(ctx, errors.New("回传模板不存在"))
					}
					targetAdSetting = adSettingTemplate.AdSetting
				} else if v.AdSetting != nil && v.AdSetting.Id > 0 {
					targetAdSetting = v.AdSetting
					targetAdSetting.Ipu = targetAdSetting.Pv
					targetAdSetting.IpuFlag = targetAdSetting.PvFlag
				} else {
					targetAdSetting = sourceAdSetting
				}
				err = gconv.Struct(targetAdSetting, adSettingAddReq)
				liberr.ErrIsNil(ctx, err)
				err = gconv.Struct(targetAdSetting, adRewardSettingAddReq)
				liberr.ErrIsNil(ctx, err)
				adRewardSettingAddReq.Dimension = targetAdSetting.IaaDimension
				adRewardSettingAddReq.ProtectionSetting = targetAdSetting.IaaProtectionSetting
				adRewardSettingAddReq.DailyNonCallbackQuantity = targetAdSetting.IaaDailyNonCallbackQuantity
				adRewardSettingAddReq.SingleNonCallbackQuantity = targetAdSetting.IaaSingleNonCallbackQuantity
				adSettingAddReq.SubChannel = v.ChannelCode
				adSettingAddReq.IsDefault = 0
				adSettingAddReq.TemplateId = 0
				// 批量添加 可以设置广告主
				if len(v.AdvertiserIds) > 0 && sourceChannelCode == commonConsts.DefaultChannel {
					adSettingAddReq.AdvertiserIds = strings.Join(v.AdvertiserIds, ",")
				} else {
					// 复制渠道，不复制广告主 或者 批量添加不设置广告主
					adSettingAddReq.AdvertiserIds = ""
				}
				// 新增激励广告配置信息
				rewardId, err3 := service2.AdRewardSetting().Add(ctx, adRewardSettingAddReq)
				liberr.ErrIsNil(ctx, err3, "新增激励广告配置失败")
				adSettingAddReq.RewardId = gconv.Int(rewardId)
				adSettingList = append(adSettingList, adSettingAddReq)
				if v.AdLinkConfig != nil {
					v.AdLinkConfig.Account = v.ChannelCode
					adDiversionLinkList = append(adDiversionLinkList, v.AdLinkConfig)
				}
			}
			//添加渠道
			s.BatchAdd(ctx, addChannelList)
			service2.AdSetting().BatchAdd(ctx, adSettingList) //添加adsetting
			// 添加新的导流链接
			service2.AdDiversionLink().BatchAdd(ctx, adDiversionLinkList)
			// 同步面板到抖音
			go func() {
				innerCtx, cancel := context.WithCancel(context.Background())
				defer cancel()
				for _, adLink := range adDiversionLinkList {
					if !strings.Contains(adLink.AppId, commonConsts.DyAppIdPrefix) {
						continue
					}
					err3 := service2.PProduct().SyncPanelInfoToDy(innerCtx, adLink.Account)
					if err3 != nil {
						g.Log().Errorf(innerCtx, "渠道: %s, 同步面板信息到抖音失败: %v", adLink.Account, err3)
					}
				}
			}()
		}

	})
	return
}

// BatchCopyAddAndPush 新增渠道并且推送
func (s *sSChannel) BatchCopyAddAndPush(ctx context.Context, req *model.SChannelBatchAddAndPushReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.PushReq == nil || len(req.PushReq.PushItems) == 0 {
			liberr.ErrIsNil(ctx, errors.New("推送信息不能为空"))
		}
		// 合并重复的逻辑为一个函数
		assignAdvertiserIds := func(channelCode string, pushItems []*model.PushItem, pushMethod int) []string {
			for index, pushItem := range pushItems {
				if index > 0 && pushMethod == commonConsts.PushMethodUnified {
					continue
				}
				if channelCode == pushItem.ChannelCode {
					return pushItem.AdvertiserIds
				}
			}
			return nil
		}
		// 应用到渠道列表
		for _, v := range req.List {
			v.AdvertiserIds = assignAdvertiserIds(v.ChannelCode, req.PushReq.PushItems, req.PushReq.PushMethod)
		}
		// 应用到番茄渠道列表
		for _, v := range req.FqList {
			v.AdvertiserIds = assignAdvertiserIds(v.ChannelCode, req.PushReq.PushItems, req.PushReq.PushMethod)
		}
		// 应用到点众渠道列表
		for _, v := range req.DzList {
			v.AdvertiserIds = assignAdvertiserIds(v.ChannelCode, req.PushReq.PushItems, req.PushReq.PushMethod)
		}
		if req.OnlyPush {
			var channelCodes = make([]string, 0)
			for _, v := range req.PushReq.PushItems {
				channelCodes = append(channelCodes, v.ChannelCode)
			}
			adSettings, _ := service2.AdSetting().GetByAccountList(ctx, channelCodes)
			editAdvertiserIds := make([]*model.SChannelBatchEditAdvertiserId, 0)
			for index, v := range req.PushReq.PushItems {
				if index > 0 && req.PushReq.PushMethod == commonConsts.PushMethodUnified {
					continue
				}
				advertiserIds := gset.NewStrSetFrom(v.AdvertiserIds)
				editAdvertiserId := &model.SChannelBatchEditAdvertiserId{}
				for _, adSetting := range adSettings {
					if adSetting.SubChannel == v.ChannelCode {
						if len(adSetting.AdvertiserIds) > 0 {
							advertiserIds.Add(strings.Split(adSetting.AdvertiserIds, ",")...)
						}
						editAdvertiserId.AdvertiserIds = advertiserIds.Slice()
						editAdvertiserId.AdSettingId = adSetting.Id
						break
					}
				}
				editAdvertiserIds = append(editAdvertiserIds, editAdvertiserId)
			}
			err = s.BatchEditAdvertiserIds(ctx, &model.SChannelBatchEditAdvertiserIdsReq{
				List: editAdvertiserIds,
			})
			liberr.ErrIsNil(ctx, err)
		} else {
			if len(req.List) > 0 {
				err = s.BatchCopyAdd(ctx, &model.SChannelBatchAddReq{
					List: req.List,
				})
				liberr.ErrIsNil(ctx, err)
			} else if len(req.FqList) > 0 {
				err = s.BatchCopyAddFq(ctx, &model.SChannelBatchAddFqReq{
					List: req.FqList,
				})
				liberr.ErrIsNil(ctx, err)
				// 番茄渠道获取小程序ID
				channelCodes := make([]string, 0)
				for _, v := range req.FqList {
					channelCodes = append(channelCodes, v.ChannelCode)
				}
				adDiversionLink, _ := service2.AdDiversionLink().GetByAccountList(ctx, channelCodes)
				for _, pushItem := range req.PushReq.PushItems {
					for _, adLink := range adDiversionLink {
						if adLink.Account == pushItem.ChannelCode {
							appId, _ := libUtils.ExtractAppID(adLink.Link)
							pushItem.AppId = appId
							break
						}
					}
				}
			} else if len(req.DzList) > 0 {
				err = s.BatchCopyAddDz(ctx, &model.SChannelBatchAddDzReq{
					List: req.DzList,
				})
				liberr.ErrIsNil(ctx, err)
				// 点众渠道获取小程序ID
				dzChannelIds := make([]int64, 0)
				for _, v := range req.DzList {
					dzChannelIds = append(dzChannelIds, gconv.Int64(v.DzChannelId))
				}
				dzChannelList, _ := adService.DzAdAccountChannel().GetByChannelIds(ctx, dzChannelIds)
				for _, pushItem := range req.PushReq.PushItems {
					for _, v := range req.DzList {
						if v.ChannelCode == pushItem.ChannelCode {
							var dzChannelId = v.DzChannelId
							for _, dzChannel := range dzChannelList {
								if dzChannelId == gconv.String(dzChannel.ChannelId) {
									pushItem.AppId = dzChannel.OfAppId
									break
								}
							}
							break
						}
					}
				}
			}
		}
		// 任务名称
		var taskName string
		var mediaType = commonConsts.MediaTypeMapping[req.PushReq.MediaType]
		var pushType = commonConsts.PushTypeMapping[req.PushReq.PushType]
		var pushMethod = commonConsts.PushMethodMapping[req.PushReq.PushMethod]
		var dateStr = gtime.Now().Format("ymd")
		var timeStr = gtime.Now().Format("His")
		if req.PushReq.PushType == commonConsts.PushTypeExistApplet {
			taskName = fmt.Sprintf("%s-%s-%s-%s", mediaType, pushType, dateStr, timeStr)
		} else {
			taskName = fmt.Sprintf("%s-%s-%s-%s-%s", mediaType, pushType, pushMethod, dateStr, timeStr)
		}
		// 推送数量
		var pushNum int
		for _, pushItem := range req.PushReq.PushItems {
			if len(pushItem.AdvertiserIds) == 0 {
				pushNum += 1
			} else {
				pushNum += len(pushItem.AdvertiserIds)
			}
		}
		userId := sysService.Context().GetUserId(ctx)
		// 添加任务记录
		var taskId = libUtils.GenerateID()
		err = service2.SChannelPushTask().Add(ctx, &model.SChannelPushTaskAddReq{
			TaskId:     taskId,
			TaskName:   taskName,
			MediaType:  req.PushReq.MediaType,
			PushMethod: req.PushReq.PushMethod,
			PushNum:    pushNum,
			OptStatus:  commonConsts.OptStatusExecuting,
			SuccessNum: 0,
			FailNum:    0,
			UserId:     int(userId),
		})
		liberr.ErrIsNil(ctx, err, "添加任务失败")
		// 获取分类ID
		if req.PushReq.CategoryId == "" {
			categoryId, _ := libUtils.GetCategoryIdByName(ctx, req.PushReq.CategoryName, req.PushReq.PushItems[0].AppId)
			req.PushReq.CategoryId = gconv.String(categoryId)
		}
		var serialNumber int
		for index, pushItem := range req.PushReq.PushItems {
			if req.PushReq.PushType == commonConsts.PushTypeExistApplet {
				serialNumber++
				s.PushTask(ctx, taskId, pushItem, req.PushReq.AdvertiserId, req.PushReq, serialNumber)
			} else {
				if len(pushItem.AdvertiserIds) == 0 {
					serialNumber++
					s.PushTask(ctx, taskId, pushItem, "", req.PushReq, serialNumber)
				} else {
					if index > 0 && req.PushReq.PushMethod == commonConsts.PushMethodUnified {
						pushItem.AdvertiserIds = req.PushReq.PushItems[0].AdvertiserIds
					}
					for _, advertiserId := range pushItem.AdvertiserIds {
						serialNumber++
						s.PushTask(ctx, taskId, pushItem, advertiserId, req.PushReq, serialNumber)
					}
				}
			}
		}
	})
	return
}

func (s *sSChannel) PushTask(ctx context.Context,
	taskId string,
	pushItem *model.PushItem,
	advertiserId string,
	pushReq *model.PushReq,
	serialNumber int) {
	task := model.PushTask{
		TaskId:       taskId,
		PushType:     pushReq.PushType,
		ChannelCode:  pushItem.ChannelCode,
		AdvertiserId: advertiserId,
		AppId:        pushItem.AppId,
		CategoryId:   pushReq.CategoryId,
		SerialNumber: serialNumber,
		InstanceId:   pushReq.InstanceId,
		LinkRemark:   pushItem.LinkRemark,
	}
	taskInfo, _ := json.Marshal(task)
	err := commonService.GetGoRedis().XAdd(ctx, &redis.XAddArgs{
		Stream: commonConsts.ChannelPushStream,
		Values: map[string]interface{}{"push_task_info": taskInfo},
	}).Err()
	liberr.ErrIsNil(ctx, err, "写入任务失败")
}

// BatchCopyAddFq 批量新增渠道和复制番茄渠道号
func (s *sSChannel) BatchCopyAddFq(ctx context.Context, req *model.SChannelBatchAddFqReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		sourceChannelCode := commonConsts.DefaultChannel
		// 渠道复制，查询目标渠道的回传数据
		if req.SourceChannelCode != "" {
			sourceChannelCode = req.SourceChannelCode
		}
		sourceAdSetting, err := service2.AdSetting().GetByChannel(ctx, sourceChannelCode)
		liberr.ErrIsNil(ctx, err)
		//判断渠道是否重复
		if len(req.List) > 0 {
			var channelCodes []string
			for _, v := range req.List {
				channelCodes = append(channelCodes, v.ChannelCode)
			}
			s.VerifyChannelCodes(ctx, channelCodes)
			// 批量新增校验广告主ID
			if sourceChannelCode == commonConsts.DefaultChannel {
				var advertiserList = make([]string, 0)
				for _, v := range req.List {
					if len(v.AdvertiserIds) > 0 {
						advertiserList = append(advertiserList, v.AdvertiserIds...)
					}
				}
				err = s.VerifyAdvertiserId(ctx, advertiserList)
				liberr.ErrIsNil(ctx, err)
			}
			fqAdAccount, _ := adService.FqAdAccountChannel().GetSecretByChannelDistributorId(ctx, gconv.Int64(req.List[0].ChannelDistributorId))
			if fqAdAccount == nil {
				liberr.ErrIsNil(ctx, errors.New("番茄账号不存在"))
			}
			var adSettingList []*model.AdSettingAddReq
			var addChannelList []*model.SChannelAddFqReq
			var adDiversionLinkList []*model.AdDiversionLinkAddFqReq
			for _, v := range req.List {
				// 创建番茄推广链接
				var index int64 = 9
				if v.AdLinkConfig.Num-1 < 9 {
					index = v.AdLinkConfig.Num
				}
				if v.LockNum == 0 {
					v.LockNum = 10
				}
				if v.BuyPriceGold == 0 {
					v.BuyPriceGold = 1.5
				}
				var price = gconv.Int64(v.BuyPriceGold * 100)
				promotionCreateReq := fqModel.PromotionCreateReq{
					FqCommonReq: &fqModel.FqCommonReq{
						DistributorId: gconv.Int64(v.ChannelDistributorId),
					},
					BookId:        v.AdLinkConfig.FqBookId,
					Index:         index,
					PromotionName: &v.ChannelCode,
					MediaSource:   commonConsts.GetFqMediaSource(v.AdLinkConfig.Platform),
					StartChapter:  &v.LockNum,
					Price:         &price,
				}
				if v.FqCallbackConfigId != nil {
					var adCallbackConfigId = gconv.Int64(v.FqCallbackConfigId)
					promotionCreateReq.AdCallbackConfigId = &adCallbackConfigId
				}
				if v.FqRechargeTemplateId != nil {
					var fqRechargeTemplateId = gconv.Int64(v.FqRechargeTemplateId)
					promotionCreateReq.RechargeTemplateId = &fqRechargeTemplateId
				}
				if v.FqPackStrategyStatus != nil {
					promotionCreateReq.PackStrategyStatus = v.FqPackStrategyStatus
				}
				if v.FqIncentiveAdStatus != nil {
					promotionCreateReq.StrategyConfig = &fqModel.StrategyConfig{
						PromotionStrategySwitch: map[int64]int64{
							1: gconv.Int64(*v.FqIncentiveAdStatus),
						},
					}
				}
				if v.AdLinkConfig.FqWxVideoId != "" {
					promotionCreateReq.WxVideoID = &v.AdLinkConfig.FqWxVideoId
				}
				promotionCreateRes, err := fqApi.GetAdFQIClient().PromotionCreateV1ApiService.
					SetSecert(fqAdAccount.SecretKey).
					Request(promotionCreateReq).Do()
				liberr.ErrIsNil(ctx, err)
				addChannel := new(model.SChannelAddFqReq)
				err = gconv.Struct(v, addChannel)
				liberr.ErrIsNil(ctx, err)
				addChannel.UserId = user.Id
				addChannel.FqPromotionId = promotionCreateRes.PromotionId
				addChannelList = append(addChannelList, addChannel)
				var adSettingAddReq = &model.AdSettingAddReq{}
				err = gconv.Struct(sourceAdSetting, adSettingAddReq)
				liberr.ErrIsNil(ctx, err)
				adSettingAddReq.SubChannel = v.ChannelCode
				adSettingAddReq.IsDefault = 0
				adSettingAddReq.TemplateId = 0
				adSettingAddReq.RewardId = 0
				// 批量添加 可以设置广告主
				if len(v.AdvertiserIds) > 0 && sourceChannelCode == commonConsts.DefaultChannel {
					adSettingAddReq.AdvertiserIds = strings.Join(v.AdvertiserIds, ",")
				} else {
					// 复制渠道，不复制广告主 或者 批量添加不设置广告主
					adSettingAddReq.AdvertiserIds = ""
				}
				adSettingList = append(adSettingList, adSettingAddReq)
				if v.AdLinkConfig != nil {
					v.AdLinkConfig.Account = v.ChannelCode
					v.AdLinkConfig.FqPromotionUrl = promotionCreateRes.PromotionUrl
					v.AdLinkConfig.FqPromotionHttpUrl = promotionCreateRes.PromotionHttpUrl
					adDiversionLinkList = append(adDiversionLinkList, v.AdLinkConfig)
				}
			}
			// 添加渠道
			_ = s.BatchAddFq(ctx, addChannelList)
			// 添加adsetting
			_ = service2.AdSetting().BatchAdd(ctx, adSettingList)
			// 添加导流链接
			_ = service2.AdDiversionLink().BatchAddFq(ctx, adDiversionLinkList)
		}
	})
	return
}

// BatchCopyAddFqAndPush 新增番茄渠道并且推送
func (s *sSChannel) BatchCopyAddFqAndPush(ctx context.Context, req *model.SChannelBatchAddFqAndPushReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = s.BatchCopyAddAndPush(ctx, &model.SChannelBatchAddAndPushReq{
			FqList:   req.List,
			PushReq:  req.PushReq,
			OnlyPush: req.OnlyPush,
		})
		liberr.ErrIsNil(ctx, err)
	})
	return
}

// DistributionFqChannel 分配番茄/点众渠道
func (s *sSChannel) DistributionFqChannel(ctx context.Context, req *model.DistributionFqChannelReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		channelInfos, _ := s.GetByIds(ctx, gconv.Int64s(req.Ids))
		channelIds := make([]int64, 0)
		for _, v := range channelInfos {
			if v.ChannelPlatform == commonConsts.ManSenPlatform {
				continue
			}
			channelIds = append(channelIds, v.Id)
		}
		updateChannelInfo := &do.SChannel{
			Updatetime: gtime.Now(),
			UserId:     req.UserId,
		}
		if len(channelIds) > 0 {
			_, err = dao2.SChannel.Ctx(ctx).WhereIn(dao2.SChannel.Columns().Id, channelIds).Update(updateChannelInfo)
			liberr.ErrIsNil(ctx, err, "修改失败")
		}
	})
	return
}

// BatchCopyAddDz 批量新增渠道和复制点众渠道号
func (s *sSChannel) BatchCopyAddDz(ctx context.Context, req *model.SChannelBatchAddDzReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		sourceChannelCode := commonConsts.DefaultChannel
		// 渠道复制，查询目标渠道的回传数据
		if req.SourceChannelCode != "" {
			sourceChannelCode = req.SourceChannelCode
		}
		sourceAdSetting, err := service2.AdSetting().GetByChannel(ctx, sourceChannelCode)
		liberr.ErrIsNil(ctx, err)
		//判断渠道是否重复
		if len(req.List) > 0 {
			var channelCodes []string
			for _, v := range req.List {
				channelCodes = append(channelCodes, v.ChannelCode)
			}
			s.VerifyChannelCodes(ctx, channelCodes)
			// 批量新增校验广告主ID
			if sourceChannelCode == commonConsts.DefaultChannel {
				var advertiserList = make([]string, 0)
				for _, v := range req.List {
					if len(v.AdvertiserIds) > 0 {
						advertiserList = append(advertiserList, v.AdvertiserIds...)
					}
				}
				err = s.VerifyAdvertiserId(ctx, advertiserList)
				liberr.ErrIsNil(ctx, err)
			}
			// 获取点众账号
			dzAdAccount, _ := adService.DzAdAccount().GetByAccountId(ctx, req.List[0].DzAccountId)
			if dzAdAccount == nil {
				liberr.ErrIsNil(ctx, errors.New("点众账号不存在"))
			}
			var adSettingList []*model.AdSettingAddReq
			var addChannelList []*model.SChannelAddDzReq
			var adDiversionLinkList []*model.AdDiversionLinkAddDzReq
			for _, v := range req.List {
				// 创建点众推广链接
				referralSaveReq := dzApi.ReferralSaveRequest{
					ClientId:           dzAdAccount.AccountId,
					BookId:             v.AdLinkConfig.DzBookId,
					ChapterIdx:         gconv.String(v.AdLinkConfig.Num),
					Name:               v.ChannelCode,
					MediaSource:        commonConsts.GetDzMediaSource(v.AdLinkConfig.Platform),
					ChannelId:          v.DzChannelId,
					BatchId:            v.DzRechargeTemplateId,
					CallbackConfigId:   v.DzCallbackConfigId,
					FromDrId:           v.DzFromDrId,
					ChapterChargeStart: v.LockNum,
					ChapterKandian:     v.BuyPriceGold,
				}
				referralSaveRes, err := dzApi.GetAdDZIClient().ReferralSaveService.
					SetToken(dzAdAccount.Token).
					SetReq(referralSaveReq).Do()
				liberr.ErrIsNil(ctx, err)
				addChannel := new(model.SChannelAddDzReq)
				err = gconv.Struct(v, addChannel)
				liberr.ErrIsNil(ctx, err)
				addChannel.UserId = user.Id
				addChannel.DzReferralId = gconv.String(referralSaveRes.Data.ReferralId)
				addChannelList = append(addChannelList, addChannel)
				var adSettingAddReq = &model.AdSettingAddReq{}
				err = gconv.Struct(sourceAdSetting, adSettingAddReq)
				liberr.ErrIsNil(ctx, err)
				adSettingAddReq.SubChannel = v.ChannelCode
				adSettingAddReq.IsDefault = 0
				adSettingAddReq.TemplateId = 0
				adSettingAddReq.RewardId = 0
				// 批量添加 可以设置广告主
				if len(v.AdvertiserIds) > 0 && sourceChannelCode == commonConsts.DefaultChannel {
					adSettingAddReq.AdvertiserIds = strings.Join(v.AdvertiserIds, ",")
				} else {
					// 复制渠道，不复制广告主 或者 批量添加不设置广告主
					adSettingAddReq.AdvertiserIds = ""
				}
				adSettingList = append(adSettingList, adSettingAddReq)
				if v.AdLinkConfig != nil {
					v.AdLinkConfig.Account = v.ChannelCode
					v.AdLinkConfig.DzPath = referralSaveRes.Data.Path
					v.AdLinkConfig.DzMonitor = referralSaveRes.Data.Monitor
					v.AdLinkConfig.DzMonitor1 = referralSaveRes.Data.Monitor1
					v.AdLinkConfig.DzMonitor2 = referralSaveRes.Data.Monitor2
					adDiversionLinkList = append(adDiversionLinkList, v.AdLinkConfig)
				}
			}
			// 添加渠道
			_ = s.BatchAddDz(ctx, addChannelList)
			// 添加adsetting
			_ = service2.AdSetting().BatchAdd(ctx, adSettingList)
			// 添加导流链接
			_ = service2.AdDiversionLink().BatchAddDz(ctx, adDiversionLinkList)
		}
	})
	return
}

// BatchCopyAddDzAndPush 新增点众渠道并且推送
func (s *sSChannel) BatchCopyAddDzAndPush(ctx context.Context, req *model.SChannelBatchAddDzAndPushReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = s.BatchCopyAddAndPush(ctx, &model.SChannelBatchAddAndPushReq{
			DzList:   req.List,
			PushReq:  req.PushReq,
			OnlyPush: req.OnlyPush,
		})
		liberr.ErrIsNil(ctx, err)
	})
	return
}

func (s *sSChannel) GetFqAppList(ctx context.Context, req *model.GetFqAppListReq) (res *model.GetFqAppListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.PageNum > 10 {
			liberr.ErrIsNil(ctx, errors.New("页数不能大于10"))
		}
		fqAdAccount, _ := adService.FqAdAccount().GetByDistributorId(ctx, gconv.Int64(req.DistributorId))
		var pageIndex = req.PageNum - 1
		promotionCreateRes, err1 := fqApi.GetAdFQIClient().GetPackageListV2ApiService.
			SetSecert(fqAdAccount.SecretKey).
			Request(fqModel.GetPackageListReq{
				FqCommonReq: &fqModel.FqCommonReq{
					DistributorId: gconv.Int64(req.DistributorId),
					PageIndex:     &pageIndex,
					PageSize:      &req.PageSize,
				},
				AppType: req.AppType,
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetFqAppListRes{
			ListRes: comModel.ListRes{
				CurrentPage: req.PageNum,
				Total:       promotionCreateRes.Total,
			},
			List: promotionCreateRes.PackageInfoOpenList,
		}
	})
	return
}

func (s *sSChannel) GetFqRechargeTemplateList(ctx context.Context, req *model.GetFqRechargeTemplateListReq) (res *model.GetFqRechargeTemplateListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		fqAdAccount, _ := adService.FqAdAccountChannel().GetSecretByChannelDistributorId(ctx, gconv.Int64(req.ChannelDistributorId))
		var pageIndex = req.PageNum - 1
		getRechargeTemplateRes, err1 := fqApi.GetAdFQIClient().GetRechargeTemplateV1ApiService.
			SetSecert(fqAdAccount.SecretKey).
			Request(fqModel.GetRechargeTemplateReq{
				FqCommonReq: &fqModel.FqCommonReq{
					DistributorId: gconv.Int64(req.ChannelDistributorId),
					PageIndex:     &pageIndex,
					PageSize:      &req.PageSize,
				},
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetFqRechargeTemplateListRes{
			ListRes: comModel.ListRes{
				CurrentPage: req.PageNum,
				Total:       getRechargeTemplateRes.Total,
			},
			List: getRechargeTemplateRes.Data,
		}
	})
	return
}

func (s *sSChannel) GetFqCallbackConfigList(ctx context.Context, req *model.GetFqCallbackConfigListReq) (res *model.GetFqCallbackConfigListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		fqAdAccount, _ := adService.FqAdAccountChannel().GetSecretByChannelDistributorId(ctx, gconv.Int64(req.ChannelDistributorId))
		var pageIndex = req.PageNum - 1
		getCallbackConfigRes, err1 := fqApi.GetAdFQIClient().GetAdCallbackConfigV1ApiService.
			SetSecert(fqAdAccount.SecretKey).
			Request(fqModel.GetAdCallbackConfigReq{
				FqCommonReq: &fqModel.FqCommonReq{
					DistributorId: gconv.Int64(req.ChannelDistributorId),
					PageIndex:     &pageIndex,
					PageSize:      &req.PageSize,
				},
				MediaSource: fqModel.MediaSource(commonConsts.GetFqMediaSource(req.Platform)),
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetFqCallbackConfigListRes{
			ListRes: comModel.ListRes{
				CurrentPage: req.PageNum,
				Total:       getCallbackConfigRes.Total,
			},
			List: getCallbackConfigRes.ConfigList,
		}
	})
	return
}

func (s *sSChannel) GetFqBookInfo(ctx context.Context, req *model.GetFqBookInfoReq) (res *model.GetFqBookInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = &model.GetFqBookInfoRes{}
		// 从redis缓存中获取
		key := fmt.Sprintf(commonConsts.FQAdBookInfo+"%s:%s", req.BookId, req.DistributorId)
		val, err2 := commonService.RedisCache().Get(ctx, key)

		if val.IsNil() || err2 != nil {
			if len(req.SecretKey) > 0 {
				getBookMetaRes, err1 := fqApi.GetAdFQIClient().GetBookMetaV1ApiService.
					SetSecert(req.SecretKey).
					Request(fqModel.GetBookMetaReq{
						FqCommonReq: &fqModel.FqCommonReq{
							DistributorId: gconv.Int64(req.DistributorId),
						},
						BookId: gconv.Int64(req.BookId),
					}).Do()
				liberr.ErrIsNil(ctx, err1)
				if len(getBookMetaRes.Result) > 0 {
					res.GetBookMetaResult = getBookMetaRes.Result[0]
					dataStr, _ := json.Marshal(res.GetBookMetaResult)
					commonService.RedisCache().Set(ctx, key, gconv.String(dataStr))
					commonService.RedisCache().Expire(ctx, key, 24*3600)
				}
			} else {
				fqAdAccount, _ := adService.FqAdAccount().GetByDistributorId(ctx, gconv.Int64(req.DistributorId))
				getBookMetaRes, err1 := fqApi.GetAdFQIClient().GetBookMetaV1ApiService.
					SetSecert(fqAdAccount.SecretKey).
					Request(fqModel.GetBookMetaReq{
						FqCommonReq: &fqModel.FqCommonReq{
							DistributorId: gconv.Int64(req.DistributorId),
						},
						BookId: gconv.Int64(req.BookId),
					}).Do()
				liberr.ErrIsNil(ctx, err1)
				if len(getBookMetaRes.Result) > 0 {
					res.GetBookMetaResult = getBookMetaRes.Result[0]
					dataStr, _ := json.Marshal(res.GetBookMetaResult)
					commonService.RedisCache().Set(ctx, key, gconv.String(dataStr))
					commonService.RedisCache().Expire(ctx, key, 24*3600)
				}
			}
			return
		}
		if v, ok := val.Val().(string); ok {
			//dataStr, _ := v
			bookInfo := &fqModel.GetBookMetaResult{}
			err = json.Unmarshal([]byte(v), &bookInfo)
			liberr.ErrIsNil(ctx, err, "Unmarshal bookInfo failed")
			res.GetBookMetaResult = bookInfo
			return
		}
	})
	return
}

func (s *sSChannel) GetDzChannelList(ctx context.Context, req *model.GetDzChannelListReq) (res *model.GetDzChannelListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dzAdAccount, _ := adService.DzAdAccount().GetByAccountId(ctx, req.ClientId)
		if dzAdAccount == nil {
			liberr.ErrIsNil(ctx, errors.New("点众账号不存在"))
		}
		channelInfoListRes, err1 := dzApi.GetAdDZIClient().ChannelInfoListService.
			SetToken(dzAdAccount.Token).
			SetReq(dzApi.ChannelInfoListRequest{
				ClientId: req.ClientId,
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetDzChannelListRes{
			List: channelInfoListRes.Data.ChannelInfoList,
		}
	})
	return
}

func (s *sSChannel) GetDzRechargeTemplateList(ctx context.Context, req *model.GetDzRechargeTemplateListReq) (res *model.GetDzRechargeTemplateListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dzAdAccount, _ := adService.DzAdAccount().GetByAccountId(ctx, req.ClientId)
		if dzAdAccount == nil {
			liberr.ErrIsNil(ctx, errors.New("点众账号不存在"))
		}
		batchListRes, err1 := dzApi.GetAdDZIClient().BatchListService.
			SetToken(dzAdAccount.Token).
			SetReq(dzApi.BatchListRequest{
				ClientId:  req.ClientId,
				ChannelId: req.ChannelId,
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetDzRechargeTemplateListRes{
			List: batchListRes.Data,
		}
	})
	return
}

func (s *sSChannel) GetDzCallbackConfigList(ctx context.Context, req *model.GetDzCallbackConfigListReq) (res *model.GetDzCallbackConfigListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dzAdAccount, _ := adService.DzAdAccount().GetByAccountId(ctx, req.ClientId)
		if dzAdAccount == nil {
			liberr.ErrIsNil(ctx, errors.New("点众账号不存在"))
		}
		callbackListRes, err1 := dzApi.GetAdDZIClient().CallbackListService.
			SetToken(dzAdAccount.Token).
			SetReq(dzApi.CallbackListRequest{
				ClientId:  req.ClientId,
				ChannelId: req.ChannelId,
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetDzCallbackConfigListRes{
			List: callbackListRes.Data,
		}
	})
	return
}

func (s *sSChannel) GetDzBookInfo(ctx context.Context, req *model.GetDzBookInfoReq) (res *model.GetDzBookInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		dzAdAccount, _ := adService.DzAdAccount().GetByAccountId(ctx, req.ClientId)
		if dzAdAccount == nil {
			liberr.ErrIsNil(ctx, errors.New("点众账号不存在"))
		}
		bookInfoRes, err1 := dzApi.GetAdDZIClient().BookInfoService.
			SetToken(dzAdAccount.Token).
			SetReq(dzApi.BookInfoRequest{
				ClientId:  req.ClientId,
				ChannelId: req.ChannelId,
				BookId:    req.BookId,
			}).Do()
		liberr.ErrIsNil(ctx, err1)
		res = &model.GetDzBookInfoRes{}
		if len(bookInfoRes.Data) > 0 {
			res.BookItem = bookInfoRes.Data[0]
		}
	})
	return
}

func (s *sSChannel) RunSyncFqPromotionUrl(ctx context.Context, req *model.SyncFqPromotionUrlReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		if startTime == "" {
			err1 := s.SyncFqPromotionUrl(innerContext, "", req.PromotionId)
			liberr.ErrIsNil(innerContext, err1)
		} else {
			for {
				if startTime > endTime {
					break
				}
				err1 := s.SyncFqPromotionUrl(innerContext, startTime, req.PromotionId)
				if err1 != nil {
					g.Log().Error(innerContext, err1)
				}
				startTime = libUtils.PlusDays(startTime, 1)
			}
		}
	})
	return
}

func (s *sSChannel) SyncFqPromotionUrlTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatSyncFqPromotionUrlTaskLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatSyncFqPromotionUrlTaskLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		today := gtime.Now().Format("Y-m-d")
		err = s.SyncFqPromotionUrl(ctx, today, "")
		liberr.ErrIsNil(ctx, err, "同步番茄推广链接失败")
		err = adService.FqAdAccountChannel().SyncFqAdAccountChannel(ctx)
		liberr.ErrIsNil(ctx, err, "同步番茄渠道数据失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncFqPromotionUrlTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步番茄推广链接，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// SyncFqPromotionUrl 同步番茄推广链接
func (s *sSChannel) SyncFqPromotionUrl(ctx context.Context, statTime string, promotionId string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTimestamps, endTimestamps, _ := libUtils.GetDayTimestamps(statTime)
		var pageNo = 1
		var pageSize = 100
		targetAdSetting, _ := service2.AdSetting().GetByChannel(ctx, commonConsts.DefaultChannel)
		for {
			fqAdAccountChannels, _ := adService.FqAdAccountChannel().GetChannelList(ctx, &adModel.FqAdAccountChannelSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})
			if len(fqAdAccountChannels) == 0 {
				break
			}
			for _, fqAdAccountChannel := range fqAdAccountChannels {
				// 获取回传规则列表
				mediaSources := []fqModel.MediaSource{
					fqModel.MediaSourceByte,
					fqModel.MediaSourceKs,
					fqModel.MediaSourceTencent,
					fqModel.MediaSourceBaidu,
				}
				adCallbackConfigs := make([]*fqModel.ConfigInfo, 0)
				for _, mediaSource := range mediaSources {
					var callbackPageIndex = 0
					var callbackPageSize = 100
					for {
						adCallbackConfigRes, err1 := fqApi.GetAdFQIClient().GetAdCallbackConfigV1ApiService.
							SetSecert(fqAdAccountChannel.SecretKey).
							Request(fqModel.GetAdCallbackConfigReq{
								FqCommonReq: &fqModel.FqCommonReq{
									DistributorId: fqAdAccountChannel.ChannelDistributorId,
									PageIndex:     &callbackPageIndex,
									PageSize:      &callbackPageSize,
								},
								MediaSource: mediaSource,
							}).Do()
						if err1 != nil {
							g.Log().Error(ctx, fmt.Sprintf("GetAdFQIClient GetAdCallbackConfigV1ApiService err：%v", err1))
							break
						}
						if len(adCallbackConfigRes.ConfigList) == 0 {
							break
						}
						if len(adCallbackConfigRes.ConfigList) > 0 {
							adCallbackConfigs = append(adCallbackConfigs, adCallbackConfigRes.ConfigList...)
						}
						if adCallbackConfigRes.Total <= int64((callbackPageIndex+1)*callbackPageSize) {
							break
						}
						callbackPageIndex++
					}
				}
				var offset int64 = 0
				var limit int64 = 100
				for {
					getPromotionListReq := fqModel.GetPromotionListReq{
						FqCommonReq: &fqModel.FqCommonReq{
							DistributorId: fqAdAccountChannel.ChannelDistributorId,
						},
						Offset: offset,
						Limit:  limit,
					}
					if startTimestamps > 0 && endTimestamps > 0 {
						getPromotionListReq.Begin = startTimestamps / 1000
						getPromotionListReq.End = endTimestamps / 1000
					}
					if promotionId != "" {
						getPromotionListReq.PromotionId = promotionId
					}
					promotionListRes, err1 := fqApi.GetAdFQIClient().GetPromotionListV1ApiService.
						SetSecert(fqAdAccountChannel.SecretKey).
						Request(getPromotionListReq).Do()
					if err1 != nil {
						g.Log().Error(ctx, err1)
						break
					}
					if len(promotionListRes.Result) == 0 {
						break
					}
					var promotionIds = make([]string, 0)
					var channelList = make([]*model.SChannelAddFqReq, 0)
					var adDiversionLinkList = make([]*model.AdDiversionLinkAddFqReq, 0)
					for _, promotion := range promotionListRes.Result {
						var channelCode = fmt.Sprintf("%s(%v)", promotion.PromotionName, promotion.PromotionId)
						var fqCallbackConfigId = gconv.String(promotion.AdCallbackConfigId)
						var fqPromotionId = gconv.String(promotion.PromotionId)
						promotionIds = append(promotionIds, fqPromotionId)
						var fqCallbackConfigName string
						for _, config := range adCallbackConfigs {
							if config.ConfigId == promotion.AdCallbackConfigId {
								fqCallbackConfigName = config.ConfigName
								break
							}
						}
						channelList = append(channelList, &model.SChannelAddFqReq{
							ChannelCode:          channelCode,
							ChannelDistributorId: gconv.String(fqAdAccountChannel.ChannelDistributorId),
							FqCallbackConfigId:   &fqCallbackConfigId,
							FqCallbackConfigName: &fqCallbackConfigName,
							FqPromotionId:        fqPromotionId,
							CreateTime:           gtime.ParseTimeFromContent(promotion.CreateTime, time.DateTime),
						})
						adDiversionLinkList = append(adDiversionLinkList, &model.AdDiversionLinkAddFqReq{
							Account:            channelCode,
							Platform:           commonConsts.GetPlatformByMediaSource(promotion.MediaSource),
							Num:                promotion.ChapterOrder,
							FqBookId:           gconv.String(promotion.BookId),
							FqBookName:         promotion.BookName,
							FqWxVideoId:        promotion.WxVideoId,
							FqPromotionUrl:     promotion.PromotionUrl,
							FqPromotionHttpUrl: promotion.PromotionHttpUrl,
							FqPromotionId:      fqPromotionId,
						})
					}
					// 查询推广链接是否已存在
					existChannelList, _ := s.GetChannelByPromotionIds(ctx, promotionIds)
					existPromotionIds := make([]string, 0)
					for _, v := range existChannelList {
						existPromotionIds = append(existPromotionIds, v.FqPromotionId)
					}
					var addChannelList = make([]*model.SChannelAddFqReq, 0)
					var adSettingList = make([]*model.AdSettingAddReq, 0)
					for _, addChannel := range channelList {
						if libUtils.FindTargetStr(existPromotionIds, addChannel.FqPromotionId) {
							continue
						}
						addChannelList = append(addChannelList, addChannel)
						var adSettingAddReq = &model.AdSettingAddReq{}
						err = gconv.Struct(targetAdSetting, adSettingAddReq)
						adSettingAddReq.SubChannel = addChannel.ChannelCode
						adSettingAddReq.IsDefault = 0
						adSettingAddReq.TemplateId = 0
						adSettingAddReq.RewardId = 0
						adSettingList = append(adSettingList, adSettingAddReq)
					}
					var addAdDiversionLinkList = make([]*model.AdDiversionLinkAddFqReq, 0)
					for _, addAdDiversionLink := range adDiversionLinkList {
						if libUtils.FindTargetStr(existPromotionIds, addAdDiversionLink.FqPromotionId) {
							continue
						}
						addAdDiversionLinkList = append(addAdDiversionLinkList, addAdDiversionLink)
					}
					if len(addChannelList) > 0 {
						err = s.BatchAddFq(ctx, addChannelList)
						if err != nil {
							g.Log().Errorf(ctx, "批量新增番茄渠道失败: %v", err)
						} else {
							if len(adSettingList) > 0 {
								err = service2.AdSetting().BatchAdd(ctx, adSettingList)
								if err != nil {
									g.Log().Errorf(ctx, "批量新增番茄渠道广告主失败: %v", err)
								}
							}
							if len(addAdDiversionLinkList) > 0 {
								err = service2.AdDiversionLink().BatchAddFq(ctx, addAdDiversionLinkList)
								if err != nil {
									g.Log().Errorf(ctx, "批量新增番茄渠道推广链接失败: %v", err)
								}
							}
						}
					}
					if promotionListRes.NextOffset == 0 {
						break
					}
					offset = promotionListRes.NextOffset
				}
			}
			pageNo++
		}

	})
	return
}

func (s *sSChannel) RunSyncDzPromotionUrl(ctx context.Context, req *model.SyncFqPromotionUrlReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		if startTime == "" {
			err1 := s.SyncDzPromotionUrl(innerContext, "", "")
			liberr.ErrIsNil(innerContext, err1)
		} else {
			for {
				if startTime > endTime {
					break
				}
				err1 := s.SyncDzPromotionUrl(innerContext, startTime, "")
				if err1 != nil {
					g.Log().Error(innerContext, err1)
				}
				startTime = libUtils.PlusDays(startTime, 1)
			}
		}
	})
	return
}

func (s *sSChannel) SyncDzPromotionUrlTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatSyncDzPromotionUrlTaskLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatSyncDzPromotionUrlTaskLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		today := gtime.Now().Format("Y-m-d")
		err = s.SyncDzPromotionUrl(ctx, today, "")
		liberr.ErrIsNil(ctx, err, "同步点众推广链接失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncDzPromotionUrlTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步点众推广链接，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// SyncDzPromotionUrl 同步点众推广链接
func (s *sSChannel) SyncDzPromotionUrl(ctx context.Context, statTime string, accountId string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var startTimestamps, endTimestamps int64
		if statTime != "" {
			startTimestamps, endTimestamps, _ = libUtils.GetDayTimestamps(statTime)
			startTimestamps, endTimestamps = startTimestamps/1000, endTimestamps/1000
		}
		var pageNo = 1
		var pageSize = 100
		targetAdSetting, _ := service2.AdSetting().GetByChannel(ctx, commonConsts.DefaultChannel)
		for {
			accountChannelReq := &adModel.DzAdAccountChannelSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			}
			if accountId != "" {
				accountChannelReq.AccountId = accountId
			}
			dzAdAccountChannels, _ := adService.DzAdAccountChannel().GetChannelList(ctx, accountChannelReq)
			if len(dzAdAccountChannels) == 0 {
				break
			}
			for _, dzAdAccountChannel := range dzAdAccountChannels {
				var channelId = dzAdAccountChannel.ChannelId
				var clientId = dzAdAccountChannel.AccountId
				// 获取回传规则列表
				adCallbackConfigs := make([]dzApi.CallbackItem, 0)
				adCallbackConfigRes, err1 := dzApi.GetAdDZIClient().CallbackListService.
					SetToken(dzAdAccountChannel.Token).
					SetReq(dzApi.CallbackListRequest{
						ChannelId: gconv.Int(channelId),
						ClientId:  gconv.String(clientId),
					}).Do()
				if err1 != nil {
					g.Log().Error(ctx, fmt.Sprintf("GetAdDZIClient CallbackListService err：%v", err1))
					break
				}
				if len(adCallbackConfigRes.Data) > 0 {
					adCallbackConfigs = append(adCallbackConfigs, adCallbackConfigRes.Data...)
				}
				// 获取充值模板列表
				rechargeTemplates := make([]dzApi.BatchItem, 0)
				adBatchListRes, err1 := dzApi.GetAdDZIClient().BatchListService.
					SetToken(dzAdAccountChannel.Token).
					SetReq(dzApi.BatchListRequest{
						ChannelId: gconv.Int(channelId),
						ClientId:  gconv.String(clientId),
					}).Do()
				if err1 != nil {
					g.Log().Error(ctx, fmt.Sprintf("GetAdDZIClient BatchListService err：%v", err1))
					break
				}
				if len(adBatchListRes.Data) > 0 {
					rechargeTemplates = append(rechargeTemplates, adBatchListRes.Data...)
				}
				var referralPage = 1
				var referralSize = 200
				for {
					referralListReq := dzApi.ReferralListRequest{
						ChannelId: gconv.String(channelId),
						Size:      referralSize,
						Page:      referralPage,
						ClientId:  gconv.String(clientId),
					}
					if startTimestamps > 0 && endTimestamps > 0 {
						var startTime = gconv.String(startTimestamps)
						var endTime = gconv.String(endTimestamps)
						referralListReq.StartTime = &startTime
						referralListReq.EndTime = &endTime
					}
					referralListRes, err2 := dzApi.GetAdDZIClient().ReferralListService.
						SetToken(dzAdAccountChannel.Token).
						SetReq(referralListReq).Do()
					if err2 != nil {
						g.Log().Error(ctx, err2)
						break
					}
					if len(referralListRes.Data.List) == 0 {
						break
					}
					var referralIds = make([]string, 0)
					var channelList = make([]*model.SChannelAddDzReq, 0)
					var adDiversionLinkList = make([]*model.AdDiversionLinkAddDzReq, 0)
					for _, promotion := range referralListRes.Data.List {
						var channelCode = fmt.Sprintf("%s(%v)", promotion.Name, promotion.ReferralId)
						var dzReferralId = gconv.String(promotion.ReferralId)
						referralIds = append(referralIds, dzReferralId)
						var dzCallbackConfigName string
						var dzRechargeTemplateName string
						for _, config := range adCallbackConfigs {
							if config.Id == promotion.CallbackConfigId {
								dzCallbackConfigName = config.Name
								break
							}
						}
						for _, template := range rechargeTemplates {
							if template.Id == promotion.ReferralGoodsBatchId {
								dzRechargeTemplateName = template.Name
								break
							}
						}
						channelList = append(channelList, &model.SChannelAddDzReq{
							ChannelCode:            channelCode,
							DzAccountId:            gconv.String(clientId),
							DzRechargeTemplateId:   gconv.String(promotion.ReferralGoodsBatchId),
							DzRechargeTemplateName: dzRechargeTemplateName,
							DzCallbackConfigId:     gconv.String(promotion.CallbackConfigId),
							DzCallbackConfigName:   dzCallbackConfigName,
							DzChannelId:            gconv.String(channelId),
							DzReferralId:           gconv.String(promotion.ReferralId),
						})
						adDiversionLinkList = append(adDiversionLinkList, &model.AdDiversionLinkAddDzReq{
							Account:      channelCode,
							Platform:     commonConsts.GetPlatformByDzMediaSource(promotion.MediaSource),
							DzBookId:     gconv.String(promotion.BookId),
							DzBookName:   promotion.BookName,
							DzPath:       promotion.Path,
							DzMonitor:    promotion.Monitor,
							DzMonitor1:   promotion.Monitor1,
							DzMonitor2:   promotion.Monitor2,
							DzReferralId: gconv.String(promotion.ReferralId),
						})
					}
					// 查询推广链接是否已存在
					existChannelList, _ := s.GetChannelByReferralIds(ctx, referralIds)
					existReferralIds := make([]string, 0)
					for _, v := range existChannelList {
						existReferralIds = append(existReferralIds, v.DzReferralId)
					}
					var addChannelList = make([]*model.SChannelAddDzReq, 0)
					var adSettingList = make([]*model.AdSettingAddReq, 0)
					for _, addChannel := range channelList {
						if libUtils.FindTargetStr(existReferralIds, addChannel.DzReferralId) {
							continue
						}
						addChannelList = append(addChannelList, addChannel)
						var adSettingAddReq = &model.AdSettingAddReq{}
						err = gconv.Struct(targetAdSetting, adSettingAddReq)
						adSettingAddReq.SubChannel = addChannel.ChannelCode
						adSettingAddReq.IsDefault = 0
						adSettingAddReq.TemplateId = 0
						adSettingAddReq.RewardId = 0
						adSettingList = append(adSettingList, adSettingAddReq)
					}
					var addAdDiversionLinkList = make([]*model.AdDiversionLinkAddDzReq, 0)
					for _, addAdDiversionLink := range adDiversionLinkList {
						if libUtils.FindTargetStr(existReferralIds, addAdDiversionLink.DzReferralId) {
							continue
						}
						addAdDiversionLinkList = append(addAdDiversionLinkList, addAdDiversionLink)
					}
					if len(addChannelList) > 0 {
						err = s.BatchAddDz(ctx, addChannelList)
						if err != nil {
							g.Log().Errorf(ctx, "批量新增点众渠道失败: %v", err)
						} else {
							if len(adSettingList) > 0 {
								err = service2.AdSetting().BatchAdd(ctx, adSettingList)
								if err != nil {
									g.Log().Errorf(ctx, "批量新增点众渠道广告主失败: %v", err)
								}
							}
							if len(addAdDiversionLinkList) > 0 {
								err = service2.AdDiversionLink().BatchAddDz(ctx, addAdDiversionLinkList)
								if err != nil {
									g.Log().Errorf(ctx, "批量新增点众渠道推广链接失败: %v", err)
								}
							}
						}
					}
					if referralPage*referralSize >= referralListRes.Data.Total {
						break
					}
					referralPage++
				}
			}
			pageNo++
		}
	})
	return
}

func (s *sSChannel) VerifyChannelCodes(ctx context.Context, channelCodes []string) {
	//先判断是否重复渠道
	channelList, err := s.GetChannelByChannelCodes(ctx, channelCodes)
	liberr.ErrIsNil(ctx, err, "查询渠道数据失败")
	if len(channelList) > 0 {
		//存在重复渠道数据
		var repeatChannel []string
		for _, v := range channelList {
			repeatChannel = append(repeatChannel, v.ChannelCode)
		}
		liberr.ErrIsNil(ctx, errors.New(fmt.Sprintf("渠道名：%s已存在", strings.Join(repeatChannel, ","))))
	}
}

// VerifyAdvertiserId 校验广告主id是否已绑定
func (s *sSChannel) VerifyAdvertiserId(ctx context.Context, advertiserList []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		errMsg := service2.AdSetting().CheckAdvertiser(advertiserList, 0, ctx)
		// 校验不通过
		if len(errMsg) > 0 {
			err = errors.New(strings.Join(errMsg, ";"))
			liberr.ErrIsNil(ctx, err)
		}
	})
	return
}

// GenerateChannelCodeList 生成渠道号名称列表
func (s *sSChannel) GenerateChannelCodeList(ctx context.Context, channelCode string, num int) (res *model.GenerateChannelCodeListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.GenerateChannelCodeListRes)
		var dailyNum int
		data, _ := commonService.RedisCache().Get(context.Background(), fmt.Sprintf("%s%s", commonConsts.ChannelCodeDailyNumKey, channelCode))
		if !data.IsNil() {
			if v, ok := data.Val().(string); ok {
				dailyNum = gconv.Int(v)
			}
		}
		channelCodeList := make([]string, 0)
		var dateStr = gtime.Now().Format("ymd")
		for i := 0; i < num; i++ {
			dailyNumStr := libUtils.GetDailyNum(&dailyNum)
			channelCodeList = append(channelCodeList, fmt.Sprintf("%s%s%v", channelCode, dateStr, dailyNumStr))
		}
		existChannelList, _ := s.GetChannelByChannelCodes(ctx, channelCodeList)
		remDupChanelCodeList := make([]string, 0)
		if len(existChannelList) > 0 {
			// 移除已存在的渠道号
			existChannelCodeList := make([]string, 0)
			for _, v := range existChannelList {
				existChannelCodeList = append(existChannelCodeList, v.ChannelCode)
			}
			remDupChanelCodeList = libUtils.RemoveSliceStr(channelCodeList, existChannelCodeList)
			// 补充新的渠道号
			for i := 0; i < len(existChannelList); i++ {
				dailyNumStr := libUtils.GetDailyNum(&dailyNum)
				remDupChanelCodeList = append(remDupChanelCodeList, fmt.Sprintf("%s%s%v", channelCode, dateStr, dailyNumStr))
			}
			res.List = remDupChanelCodeList
		} else {
			res.List = channelCodeList
		}
	})
	return
}

func (s *sSChannel) BatchAdd(ctx context.Context, list []*model.SChannelAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if list != nil {
			var data = make([]do.SChannel, len(list))
			for k, v := range list {
				sChannel := do.SChannel{
					Id:                      libUtils.GenerateIDInt64(),
					ChannelCode:             v.ChannelCode,
					Createtime:              gtime.Now(),
					Updatetime:              gtime.Now(),
					Remark:                  v.Remark,
					BuyPriceGold:            v.BuyPriceGold,
					IsDefault:               v.IsDefault,
					DelFlag:                 v.DelFlag,
					UserId:                  v.UserId,
					AppId:                   v.AppId,
					LockNum:                 v.LockNum,
					OpenRetainWindow:        v.OpenRetainWindow,
					RetainTitle:             v.RetainTitle,
					RetainUnlockEpisodesNum: v.RetainUnlockEpisodesNum,
					RetainDayViewsNum:       v.RetainDayViewsNum,
					RetainWindowSeconds:     v.RetainWindowSeconds,
					ContinuousIaaRetain:     v.ContinuousIaaRetain,
					ContinuousUnlockInfo:    v.ContinuousUnlockInfo,
				}
				if v.TemplateId > 0 {
					sChannel.TemplateId = v.TemplateId
				}
				if v.AdSettingTemplateId > 0 {
					sChannel.AdSettingTemplateId = v.AdSettingTemplateId
				}
				data[k] = sChannel
			}
			//批量插入数据
			err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
				_, err := dao2.SChannel.Ctx(ctx).TX(tx).Batch(100).Insert(data)
				return err
			})
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannel) BatchAddFq(ctx context.Context, list []*model.SChannelAddFqReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if list != nil {
			var data = make([]do.SChannel, len(list))
			for k, v := range list {
				sChannel := do.SChannel{
					Id:              libUtils.GenerateIDInt64(),
					ChannelCode:     v.ChannelCode,
					Createtime:      v.CreateTime,
					Updatetime:      gtime.Now(),
					Remark:          v.Remark,
					BuyPriceGold:    v.BuyPriceGold,
					IsDefault:       0,
					DelFlag:         0,
					UserId:          v.UserId,
					LockNum:         v.LockNum,
					ChannelPlatform: commonConsts.FanQiePlatform,
					FqDistributorId: v.ChannelDistributorId,
					FqAppType:       v.FqAppType,
				}
				if v.FqRechargeTemplateId != nil {
					sChannel.FqRechargeTemplateId = v.FqRechargeTemplateId
				}
				if v.FqRechargeTemplateName != nil {
					sChannel.FqRechargeTemplateName = v.FqRechargeTemplateName
				}
				if v.FqCallbackConfigId != nil {
					sChannel.FqCallbackConfigId = v.FqCallbackConfigId
				}
				if v.FqCallbackConfigName != nil {
					sChannel.FqCallbackConfigName = v.FqCallbackConfigName
				}
				if v.FqPackStrategyStatus != nil {
					sChannel.FqPackStrategyStatus = v.FqPackStrategyStatus
				}
				if v.FqIncentiveAdStatus != nil {
					sChannel.FqIncentiveAdStatus = v.FqIncentiveAdStatus
				}
				if v.FqPromotionId != "" {
					sChannel.FqPromotionId = v.FqPromotionId
				}
				data[k] = sChannel
			}
			//批量插入数据
			err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
				_, err := dao2.SChannel.Ctx(ctx).TX(tx).Batch(100).Insert(data)
				return err
			})
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannel) BatchAddDz(ctx context.Context, list []*model.SChannelAddDzReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if list != nil {
			var data = make([]do.SChannel, len(list))
			for k, v := range list {
				sChannel := do.SChannel{
					Id:                     libUtils.GenerateIDInt64(),
					ChannelCode:            v.ChannelCode,
					Createtime:             gtime.Now(),
					Updatetime:             gtime.Now(),
					Remark:                 v.Remark,
					BuyPriceGold:           v.BuyPriceGold,
					IsDefault:              0,
					DelFlag:                0,
					UserId:                 v.UserId,
					LockNum:                v.LockNum,
					ChannelPlatform:        commonConsts.DianZhongPlatform,
					DzAccountId:            v.DzAccountId,
					DzRechargeTemplateId:   v.DzRechargeTemplateId,
					DzRechargeTemplateName: v.DzRechargeTemplateName,
					DzCallbackConfigId:     v.DzCallbackConfigId,
					DzCallbackConfigName:   v.DzCallbackConfigName,
					DzChannelId:            v.DzChannelId,
					DzFromDrId:             v.DzFromDrId,
					DzReferralId:           v.DzReferralId,
				}
				data[k] = sChannel
			}
			//批量插入数据
			err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
				_, err := dao2.SChannel.Ctx(ctx).TX(tx).Batch(100).Insert(data)
				return err
			})
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannel) GetChannelListTemplateId(ctx context.Context, templateId int) (listRes []*model.SChannelInfoRes, err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		m := dao2.SChannel.Ctx(ctx).WithAll()
		m = m.Where(dao2.SChannel.Columns().TemplateId+" = ?", gconv.Int(templateId))
		err = m.Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannel) List(ctx context.Context, req *model.SChannelSearchReq) (listRes *model.SChannelSearchRes, err error) {
	listRes = new(model.SChannelSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao2.SChannel.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao2.SChannel.Columns().Id+" = ?", req.Id)
		}
		if req.ChannelCode != "" {
			m = m.Where(dao2.SChannel.Columns().ChannelCode+" = ?", req.ChannelCode)
		}
		if req.Createtime != "" {
			m = m.Where(dao2.SChannel.Columns().Createtime+" = ?", gconv.Time(req.Createtime))
		}
		if req.Updatetime != "" {
			m = m.Where(dao2.SChannel.Columns().Updatetime+" = ?", gconv.Time(req.Updatetime))
		}
		if req.IsDefault != "" {
			m = m.Where(dao2.SChannel.Columns().IsDefault+" = ?", gconv.Int(req.IsDefault))
		}
		if req.DelFlag != "" {
			m = m.Where(dao2.SChannel.Columns().DelFlag+" = ?", gconv.Int(req.DelFlag))
		}
		if req.UserId != "" {
			m = m.Where(dao2.SChannel.Columns().UserId+" = ?", gconv.Int(req.UserId))
		}
		if req.AppId != "" {
			m = m.Where(dao2.SChannel.Columns().AppId+" = ?", req.AppId)
		}
		if req.LockNum != "" {
			m = m.Where(dao2.SChannel.Columns().LockNum+" = ?", gconv.Int(req.LockNum))
		}
		if req.TemplateId != "" {
			m = m.Where(dao2.SChannel.Columns().TemplateId+" = ?", gconv.Int(req.TemplateId))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SChannelListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SChannelListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.SChannelListRes{
				Id:           v.Id,
				ChannelCode:  v.ChannelCode,
				Createtime:   v.Createtime,
				Updatetime:   v.Updatetime,
				Remark:       v.Remark,
				BuyPriceGold: v.BuyPriceGold,
				IsDefault:    v.IsDefault,
				DelFlag:      v.DelFlag,
				UserId:       v.UserId,
				AppId:        v.AppId,
				LockNum:      v.LockNum,
				TemplateId:   v.TemplateId,
			}
		}
	})
	return
}

func (s *sSChannel) GetChannelList(ctx context.Context, req *comModel.PageReq, keyWard, remark string, channelList []string, channelPlatform int) (listRes *model.GetChannelListRes, err error) {
	listRes = new(model.GetChannelListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao2.SChannel.Ctx(ctx).WithAll().
			As("demo").
			LeftJoin("ad_setting ads", fmt.Sprintf("demo.%s=ads.%s", dao2.SChannel.Columns().ChannelCode, dao2.AdSetting.Columns().SubChannel)).
			LeftJoin("sys_user user", "demo.user_id=user.id").
			Where("demo.del_flag = 0")
		if keyWard != "" {
			keyWords := "%" + keyWard + "%"
			m = m.Where("demo.channel_code like ? ", keyWords)
		}
		if len(remark) > 0 {
			m = m.Where(" demo.remark like ?", "%"+remark+"%")
		}
		if channelList != nil && len(channelList) > 0 {
			m = m.WhereIn("demo.channel_code", channelList)
		}
		if channelPlatform == 0 {
			m = m.Where("demo.channel_platform = ?", commonConsts.ManSenPlatform)
		} else if channelPlatform == commonConsts.FanQiePlatform {
			m = m.Where("demo.channel_platform = ?", commonConsts.FanQiePlatform)
		}
		var (
			where g.Map
			ent   *entity.SChannel
		)
		where, err = service.SysUser().GetManSenDataWhere(ctx,
			service.Context().GetLoginUser(ctx),
			ent, consts.SearchUserId)
		liberr.ErrIsNil(ctx, err)
		m = m.Where(where) //添加条件
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.ChannelRes
		err = m.Fields("demo.id, demo.remark , demo.channel_code as account , ads.channel_from as channel ").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = res
	})
	return
}

func (s *sSChannel) GetChannelListRemark(ctx context.Context, req *comModel.PageReq, remark string) (listRes *model.GetChannelListRemarkRes, err error) {
	listRes = new(model.GetChannelListRemarkRes)
	err = g.Try(ctx, func(ctx context.Context) {
		ids, isAdmin, _ := sysService.SysUser().GetContainUser(ctx, sysService.Context().GetLoginUser(ctx))
		m := dao2.SChannel.Ctx(ctx).WithAll().Fields(" DISTINCT remark ").
			Where("del_flag = 0").Where("remark is not null")
		if len(remark) > 0 {
			m = m.Where(" remark like ?", "%"+remark+"%")
		}
		if !isAdmin {
			//非admin查询当前用户下的数据
			if len(ids) > 0 {
				m = m.WhereIn("user_id", ids)
			}
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "remark asc"
		var res []*model.ChannelRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		for _, re := range res {
			listRes.List = append(listRes.List, re.Remark)
		}

	})
	return
}
func (s *sSChannel) GetExportData(ctx context.Context, req *model.SChannelSearchReq) (listRes []*model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao2.SChannel.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao2.SChannel.Columns().Id+" = ?", req.Id)
		}
		if req.ChannelCode != "" {
			m = m.Where(dao2.SChannel.Columns().ChannelCode+" = ?", req.ChannelCode)
		}
		if req.Createtime != "" {
			m = m.Where(dao2.SChannel.Columns().Createtime+" = ?", gconv.Time(req.Createtime))
		}
		if req.Updatetime != "" {
			m = m.Where(dao2.SChannel.Columns().Updatetime+" = ?", gconv.Time(req.Updatetime))
		}
		if req.IsDefault != "" {
			m = m.Where(dao2.SChannel.Columns().IsDefault+" = ?", gconv.Int(req.IsDefault))
		}
		if req.DelFlag != "" {
			m = m.Where(dao2.SChannel.Columns().DelFlag+" = ?", gconv.Int(req.DelFlag))
		}
		if req.UserId != "" {
			m = m.Where(dao2.SChannel.Columns().UserId+" = ?", gconv.Int(req.UserId))
		}
		if req.AppId != "" {
			m = m.Where(dao2.SChannel.Columns().AppId+" = ?", req.AppId)
		}
		if req.LockNum != "" {
			m = m.Where(dao2.SChannel.Columns().LockNum+" = ?", gconv.Int(req.LockNum))
		}
		if req.TemplateId != "" {
			m = m.Where(dao2.SChannel.Columns().TemplateId+" = ?", gconv.Int(req.TemplateId))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannel) Import(ctx context.Context, file *ghttp.UploadFile) (err error) {
	if file == nil {
		err = errors.New("请上传数据文件")
		return
	}
	var data []do.SChannel
	err = g.Try(ctx, func(ctx context.Context) {
		f, err := file.Open()
		liberr.ErrIsNil(ctx, err)
		defer f.Close()
		exFile, err := excelize.OpenReader(f)
		liberr.ErrIsNil(ctx, err)
		defer exFile.Close()
		rows, err := exFile.GetRows("Sheet1")
		liberr.ErrIsNil(ctx, err)
		if len(rows) == 0 {
			liberr.ErrIsNil(ctx, errors.New("表格内容不能为空"))
		}
		d := make([]interface{}, len(rows[0]))
		data = make([]do.SChannel, len(rows)-1)
		for k, v := range rows {
			if k == 0 {
				continue
			}
			for kv, vv := range v {
				d[kv] = vv
			}
			data[k-1] = do.SChannel{
				ChannelCode:  d[0],
				Createtime:   gconv.GTime(d[1]),
				Updatetime:   gconv.GTime(d[2]),
				Remark:       d[3],
				BuyPriceGold: gconv.Float64(d[4]),
				IsDefault:    gconv.Int64(d[5]),
				DelFlag:      gconv.Int64(d[6]),
				UserId:       gconv.Int64(d[7]),
				AppId:        d[8],
				LockNum:      gconv.Int64(d[9]),
				TemplateId:   gconv.Int64(d[10]),
			}
		}
		if len(data) > 0 {
			err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
				_, err = dao2.SChannel.Ctx(ctx).Batch(500).Insert(data)
				return err
			})
			liberr.ErrIsNil(ctx, err)
		}
	})
	return
}

func (s *sSChannel) GetById(ctx context.Context, id int64) (res *model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().Where(dao2.SChannel.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannel) GetByIds(ctx context.Context, ids []int64) (res []*model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().WhereIn(dao2.SChannel.Columns().Id, ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannel) GetByCode(ctx context.Context, channelCode string) (res *model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().
			Where(dao2.SChannel.Columns().ChannelCode, channelCode).
			Where(dao2.SChannel.Columns().DelFlag, 0).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannel) Add(ctx context.Context, req *model.SChannelAddReq) (err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		searchReq := new(model.SChannelSearchReq)
		searchReq.ChannelCode = req.ChannelCode
		currChannel, err := service2.SChannel().List(ctx, searchReq)
		if err != nil {
			liberr.ErrIsNil(ctx, err, "查询渠道数据失败")
		}
		if currChannel != nil && len(currChannel.List) > 0 {
			err := gerror.NewCode(
				gcode.CodeInvalidOperation,
				`该渠道名已存在`,
			)
			liberr.ErrIsNil(ctx, err, "该渠道已存在")
		}
		//查询默认的单集价格
		searchReq.ChannelCode = commonConsts.DefaultChannel
		defaultChannel, err := service2.SChannel().List(ctx, searchReq)
		if defaultChannel != nil && len(defaultChannel.List) > 0 {
			req.BuyPriceGold = int(defaultChannel.List[0].BuyPriceGold)
		}
		_, err = dao2.SChannel.Ctx(ctx).Insert(do.SChannel{
			ChannelCode:  req.ChannelCode,
			Createtime:   gtime.Now(),
			Updatetime:   gtime.Now(),
			Remark:       req.Remark,
			BuyPriceGold: req.BuyPriceGold,
			IsDefault:    req.IsDefault,
			DelFlag:      req.DelFlag,
			UserId:       user.Id,
			AppId:        req.AppId,
			LockNum:      req.LockNum,
			TemplateId:   req.TemplateId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")

		//把默认的ad_setting填入
		adReq := new(model.AdSettingSearchReq)
		adReq.IsDefault = "1"
		defaultList := new(model.AdSettingSearchRes)
		defaultList, err = service2.AdSetting().List(ctx, adReq)
		liberr.ErrIsNil(ctx, err, "查询当前渠道的adSetting失败")
		if defaultList != nil {
			//不存在，查询默认的设置过去
			liberr.ErrIsNil(ctx, err, "查询默认的adSetting失败")
			for _, v := range defaultList.List {
				_, err = dao2.AdSetting.Ctx(ctx).Insert(do.AdSetting{
					UserActionSetAppName:       v.UserActionSetAppName,
					BaiduToken:                 v.BaiduToken,
					SubChannel:                 req.ChannelCode,
					OrderReturnRateType:        v.OrderReturnRateType,
					AdActive:                   v.AdActive,
					ReturnRate:                 v.ReturnRate,
					MinReturnAmount:            v.MinReturnAmount,
					MaxReturnAmount:            v.MaxReturnAmount,
					ChannelFrom:                v.ChannelFrom,
					OriginalAccount:            v.OriginalAccount,
					IsDefault:                  0,
					CreateTime:                 gtime.Now(),
					UpdateTime:                 gtime.Now(),
					CustomReportFlag:           v.CustomReportFlag,
					AdAction:                   v.AdAction,
					OpenFlag:                   v.OpenFlag,
					AdvertiserId:               v.AdvertiserId,
					SourceId:                   v.SourceId,
					LowOrderReturnRateTrue:     v.LowOrderReturnRateTrue,
					AppId:                      req.AppId,
					LowOrderReturnRateFalse:    v.LowOrderReturnRateFalse,
					MiddleOrderReturnRateTrue:  v.MiddleOrderReturnRateTrue,
					MiddleOrderReturnRateFalse: v.MiddleOrderReturnRateFalse,
					HighOrderReturnRateTrue:    v.HighOrderReturnRateTrue,
					HighOrderReturnRateFalse:   v.HighOrderReturnRateFalse,
					LowMinReturnAmount:         v.LowMinReturnAmount,
					LowMaxReturnAmount:         v.LowMaxReturnAmount,
					MiddleMinReturnAmount:      v.MiddleMinReturnAmount,
					MiddleMaxReturnAmount:      v.MiddleMaxReturnAmount,
					HighMinReturnAmount:        v.HighMinReturnAmount,
					HighMaxReturnAmount:        v.HighMaxReturnAmount,
					LowOpenFlag:                v.LowOpenFlag,
					MiddleOpenFlag:             v.MiddleOpenFlag,
					HighOpenFlag:               v.HighOpenFlag,
					TopMinReturnAmount:         v.TopMinReturnAmount,
					TopMaxReturnAmount:         v.TopMaxReturnAmount,
					ReturnType:                 v.ReturnType,
					TopOrderReturnRateTrue:     v.TopOrderReturnRateTrue,
					TopOrderReturnRateFalse:    v.TopOrderReturnRateFalse,
					AdvertiserIds:              v.AdvertiserIds,
					OldCustomReportFlag:        v.OldCustomReportFlag,
				})
			}
		}
		//将模板id的数据加进去 添加到product
		proSearchReq := new(model.PProductSearchReq)
		if req.TemplateId == 0 {
			//模板为空，查询默认模板
			proSearchReq.ChannelCode = commonConsts.DefaultChannel
		} else {
			proSearchReq.TemplateId = req.TemplateId
		}

		record, err := service2.PProduct().GetRecharge(ctx, proSearchReq)
		liberr.ErrIsNil(ctx, err, "查询当前充值模板的产品出错")
		if record != nil {
			if record.FirstList != nil && len(record.FirstList) > 0 {
				for _, v := range record.FirstList {
					//查到数据插入
					v.ChannelCode = req.ChannelCode
					v.CreateTime = gtime.Now()
					v.UserId = int(user.Id)
					_, err = dao2.PProduct.Ctx(ctx).Insert(do.PProduct{
						SysCode:          v.SysCode,
						Labels:           v.Labels,
						Price:            v.Price,
						ExchangeGoldCoin: v.ExchangeGoldCoin,
						Sort:             v.Sort,
						TopFlag:          v.TopFlag,
						Status:           1,
						UpdateTime:       v.UpdateTime,
						DelFlag:          req.DelFlag,
						ProductName:      v.ProductName,
						IsFirst:          v.IsFirst,
						Vip:              v.Vip,
						IsDefault:        1,
						ChannelCode:      v.ChannelCode,
						AppId:            v.AppId,
						Type:             v.Type,
						IsHigh:           v.IsHigh,
						Remakes:          v.Remakes,
						GiveGold:         v.GiveGold,
						VipType:          v.VipType,
						GuideRecharge:    v.GuideRecharge,
						Color:            v.Color,
						FixedPosition:    v.FixedPosition,
						Retain:           v.Retain,
						UserId:           v.UserId,
						PlayletId:        v.PlayletId,
					})
					liberr.ErrIsNil(ctx, err, "添加失败")

				}
			}
			if record.RepeatList != nil && len(record.RepeatList) > 0 {
				for _, v := range record.RepeatList {
					//查到数据插入
					v.ChannelCode = req.ChannelCode
					v.CreateTime = gtime.Now()
					v.UserId = int(user.Id)
					_, err = dao2.PProduct.Ctx(ctx).Insert(do.PProduct{
						SysCode:          v.SysCode,
						Labels:           v.Labels,
						Price:            v.Price,
						ExchangeGoldCoin: v.ExchangeGoldCoin,
						Sort:             v.Sort,
						TopFlag:          v.TopFlag,
						Status:           1,
						UpdateTime:       v.UpdateTime,
						DelFlag:          req.DelFlag,
						ProductName:      v.ProductName,
						IsFirst:          v.IsFirst,
						Vip:              v.Vip,
						IsDefault:        1,
						ChannelCode:      v.ChannelCode,
						AppId:            v.AppId,
						Type:             v.Type,
						IsHigh:           v.IsHigh,
						Remakes:          v.Remakes,
						GiveGold:         v.GiveGold,
						VipType:          v.VipType,
						GuideRecharge:    v.GuideRecharge,
						Color:            v.Color,
						FixedPosition:    v.FixedPosition,
						Retain:           v.Retain,
						UserId:           v.UserId,
						PlayletId:        v.PlayletId,
					})
					liberr.ErrIsNil(ctx, err, "添加失败")

				}
			}
		}

	})
	return

}

func (s *sSChannel) Edit(ctx context.Context, req *model.SChannelEditReq) (err error) {
	currChannel, err := service2.SChannel().GetById(ctx, gconv.Int64(req.Id))
	if err != nil {
		liberr.ErrIsNil(ctx, err, "查询渠道数据失败")
	}
	if currChannel == nil {
		err := gerror.NewCode(
			gcode.CodeInvalidOperation,
			`该渠道不存在`,
		)
		liberr.ErrIsNil(ctx, err, "该渠道不存在")
	}
	//更改当前渠道
	err = g.Try(ctx, func(ctx context.Context) {

		if req.BatchEdit {
			//批量修改，为空的数据，不做修改
			var updSql string = "update s_channel set updatetime=" + "'" + gtime.Now().String() + "'"
			if req.BuyPriceGold != "" {
				updSql = updSql + ",buy_price_gold=" + req.BuyPriceGold
			}
			if req.LockNum != "" {
				updSql = updSql + ",lock_num=" + req.LockNum
			}
			if req.TemplateId > 0 {
				updSql = updSql + ",template_id=" + strconv.Itoa(req.TemplateId)
			}
			if req.AdSettingTemplateId != nil {
				updSql = updSql + ",ad_setting_template_id=" + gconv.String(*req.AdSettingTemplateId)
			}
			if req.Remark != "" {
				updSql = updSql + ",remark=" + "'" + req.Remark + "'"
			}
			if req.OpenRetainWindow != nil {
				updSql = updSql + ",open_retain_window=" + gconv.String(*req.OpenRetainWindow)
			}
			if req.RetainTitle != "" {
				updSql = updSql + ",retain_title=" + "'" + req.RetainTitle + "'"
			}
			if req.RetainUnlockEpisodesNum > 0 {
				updSql = updSql + ",retain_unlock_episodes_num=" + gconv.String(req.RetainUnlockEpisodesNum)
			}
			if req.RetainDayViewsNum > 0 {
				updSql = updSql + ",retain_day_views_num=" + gconv.String(req.RetainDayViewsNum)
			}
			if req.RetainWindowSeconds > 0 {
				updSql = updSql + ",retain_window_seconds=" + gconv.String(req.RetainWindowSeconds)
			}
			if req.ContinuousIaaRetain != nil {
				updSql = updSql + ",continuous_iaa_retain=" + gconv.String(*req.ContinuousIaaRetain)
			}
			if req.ContinuousUnlockInfo != nil {
				updSql = updSql + ",continuous_unlock_info=" + "'" + gconv.String(req.ContinuousUnlockInfo) + "'"
			}
			updSql = updSql + " where id=" + req.Id
			_, err = dao2.SChannel.DB().GetAll(ctx, updSql)
		} else {
			_, err = dao2.SChannel.Ctx(ctx).WherePri(req.Id).Update(do.SChannel{
				Createtime:          req.Createtime,
				Updatetime:          gtime.Now(),
				Remark:              req.Remark,
				BuyPriceGold:        req.BuyPriceGold,
				IsDefault:           req.IsDefault,
				DelFlag:             req.DelFlag,
				AppId:               req.AppId,
				LockNum:             req.LockNum,
				TemplateId:          req.TemplateId,
				AdSettingTemplateId: req.AdSettingTemplateId,
			})
		}

		liberr.ErrIsNil(ctx, err, "修改失败")

		//if !(req.BatchEdit && req.TemplateId == 0) {
		//	//当  批量修改，且模板id为空时，不修改模板数据(批量修改不能把模板修改为空）
		//
		//	if req.TemplateId != currChannel.TemplateId {
		//		var channelCode = currChannel.ChannelCode
		//		//若修改了模板 删除当前渠道使用的模板
		//		err = service2.PProduct().DeleteByChannel(ctx, channelCode)
		//		liberr.ErrIsNil(ctx, err, "删除旧充值模板的产品出错")
		//		//把当前模板的充值数据（或默认模板） 添加到product
		//		searchReq := new(model.PProductSearchReq)
		//		if req.TemplateId == 0 {
		//			searchReq.ChannelCode = commonConsts.DefaultChannel
		//		} else {
		//			searchReq.TemplateId = req.TemplateId
		//		}
		//
		//		record, err := service2.PProduct().GetRecharge(ctx, searchReq)
		//		liberr.ErrIsNil(ctx, err, "查询当前充值模板的产品出错")
		//		var addProductArr []*model.PProductAddReq
		//		if record != nil {
		//			if record.FirstList != nil && len(record.FirstList) > 0 {
		//				for _, v := range record.FirstList {
		//					//查到数据插入
		//					productAddReq := model.PProductAddReq{
		//						SysCode:          v.SysCode,
		//						Labels:           v.Labels,
		//						Price:            v.Price,
		//						ExchangeGoldCoin: v.ExchangeGoldCoin,
		//						Sort:             v.Sort,
		//						TopFlag:          v.TopFlag,
		//						Status:           1,
		//						UpdateTime:       v.UpdateTime,
		//						DelFlag:          req.DelFlag,
		//						ProductName:      v.ProductName,
		//						IsFirst:          v.IsFirst,
		//						Vip:              v.Vip,
		//						IsDefault:        1,
		//						ChannelCode:      currChannel.ChannelCode,
		//						AppId:            v.AppId,
		//						Type:             v.Type,
		//						IsHigh:           v.IsHigh,
		//						Remakes:          v.Remakes,
		//						GiveGold:         v.GiveGold,
		//						VipType:          v.VipType,
		//						GuideRecharge:    v.GuideRecharge,
		//						Color:            v.Color,
		//						FixedPosition:    v.FixedPosition,
		//						Retain:           v.Retain,
		//						UserId:           v.UserId,
		//						PlayletId:        v.PlayletId,
		//						OriginalPrice:    v.OriginalPrice,
		//						CountdownSeconds: v.CountdownSeconds,
		//						TriggersNum:      v.TriggersNum,
		//					}
		//					addProductArr = append(addProductArr, &productAddReq)
		//				}
		//			}
		//			if record.RepeatList != nil && len(record.RepeatList) > 0 {
		//				for _, v := range record.RepeatList {
		//					//查到数据插入
		//					productAddReq := model.PProductAddReq{
		//						SysCode:          v.SysCode,
		//						Labels:           v.Labels,
		//						Price:            v.Price,
		//						ExchangeGoldCoin: v.ExchangeGoldCoin,
		//						Sort:             v.Sort,
		//						TopFlag:          v.TopFlag,
		//						Status:           1,
		//						UpdateTime:       v.UpdateTime,
		//						DelFlag:          req.DelFlag,
		//						ProductName:      v.ProductName,
		//						IsFirst:          v.IsFirst,
		//						Vip:              v.Vip,
		//						IsDefault:        1,
		//						ChannelCode:      channelCode,
		//						AppId:            v.AppId,
		//						Type:             v.Type,
		//						IsHigh:           v.IsHigh,
		//						Remakes:          v.Remakes,
		//						GiveGold:         v.GiveGold,
		//						VipType:          v.VipType,
		//						GuideRecharge:    v.GuideRecharge,
		//						Color:            v.Color,
		//						FixedPosition:    v.FixedPosition,
		//						Retain:           v.Retain,
		//						UserId:           v.UserId,
		//						PlayletId:        v.PlayletId,
		//						OriginalPrice:    v.OriginalPrice,
		//						CountdownSeconds: v.CountdownSeconds,
		//						TriggersNum:      v.TriggersNum,
		//					}
		//					addProductArr = append(addProductArr, &productAddReq)
		//
		//				}
		//			}
		//			//批量插入
		//			service2.PProduct().BatchAddPro(ctx, addProductArr)
		//		}
		//		// 同步面板到抖音
		//		go func() {
		//			innerCtx, cancel := context.WithCancel(context.Background())
		//			defer cancel()
		//			err3 := service2.PProduct().SyncPanelInfoToDy(innerCtx, currChannel.ChannelCode)
		//			if err3 != nil {
		//				g.Log().Errorf(innerCtx, "渠道: %s, 同步面板信息到抖音失败: %v", currChannel.ChannelCode, err3)
		//			}
		//		}()
		//	}
		//}

	})
	return
}

func (s *sSChannel) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao2.SChannel.Ctx(ctx).Delete(dao2.SChannel.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetChannelAdSettingList 小程序渠道列表
func (s *sSChannel) GetChannelAdSettingList(ctx context.Context, req *model.SChannelAdSettingSearchReq) (listRes *model.SChannelAdSettingSearchRes, err error) {
	listRes = new(model.SChannelAdSettingSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询权限
		user := sysService.Context().GetLoginUser(ctx)
		ids, isAdmin, _ := sysService.SysUser().GetContainUser(ctx, user)
		var m *gdb.Model
		if isAdmin {
			m = dao2.SChannelAnalytic.Ctx(ctx).As("sc")
		} else {
			m = dao2.SChannel.Ctx(ctx).As("sc")
		}
		m = m.WithAll().
			LeftJoin("ad_setting ad", "sc.channel_code = ad.sub_channel").
			LeftJoin("sys_user su", "sc.user_id = su.id ")
		var tempTable = "(SELECT account, MAX(id) AS max_id FROM ad_diversion_link GROUP BY account)"
		m = m.LeftJoin(tempTable+" temp", "sc.channel_code = temp.account").
			LeftJoin("ad_diversion_link adl", "adl.id = temp.max_id")
		m = m.Where("sc.del_flag = ?", 0)
		if req.UserId > 0 {
			m = m.Where("sc.user_id = ?", req.UserId)
		}
		if req.ChannelCode != "" {
			m = m.Where("sc.channel_code = ?", req.ChannelCode)
		}
		if req.ChannelCodes != nil && len(req.ChannelCodes) > 0 {
			m = m.Where("sc.channel_code in (?)", req.ChannelCodes)
		}
		if req.Remarks != nil && len(req.Remarks) > 0 {
			m = m.Where("sc.remark in (?)", req.Remarks)
		}
		if len(req.UserIds) > 0 {
			m = m.WhereIn("sc.user_id", req.UserIds)
		}
		if !isAdmin {
			//非admin查询当前用户下的数据
			if len(ids) > 0 {
				m = m.WhereIn("sc.user_id", ids)
			}
		}
		// 分销商ID查询
		if len(req.DistributorIds) > 0 {
			var userIds []int
			users, _ := sysService.SysUser().GetUserByIds(ctx, req.DistributorIds)
			for _, v := range users {
				deptUserIds, _, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
					LoginUserRes: &systemModel.LoginUserRes{
						Id:     v.Id,
						DeptId: v.DeptId,
					},
				})
				for _, k := range deptUserIds {
					userIds = append(userIds, k)
				}
			}
			if len(userIds) > 0 {
				m = m.WhereIn("sc.user_id", userIds)
			}
		}
		// 广告账户ID查询
		if req.AdvertiserId != "" {
			m = m.Where(fmt.Sprintf("FIND_IN_SET('%s', ad.advertiser_ids) > 0", req.AdvertiserId))
		}
		// 部门ID查询
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("su.dept_id", req.DeptIds)
		}
		// 短剧ID查询
		if len(req.VideoIds) > 0 {
			m = m.WhereIn("adl.video_id", req.VideoIds)
		}
		if req.ChannelPlatform > 0 {
			m = m.Where("sc.channel_platform = ?", req.ChannelPlatform)
		}
		if len(req.FqDistributorIds) > 0 {
			fqAdAccountChannels, _ := adService.FqAdAccountChannel().GetByDistributorIds(ctx, gconv.Int64s(req.FqDistributorIds))
			fqChannelDistributorIds := make([]string, 0)
			for _, v := range fqAdAccountChannels {
				fqChannelDistributorIds = append(fqChannelDistributorIds, gconv.String(v.ChannelDistributorId))
			}
			if len(fqChannelDistributorIds) > 0 {
				m = m.WhereIn("sc.fq_distributor_id", fqChannelDistributorIds)
			}
		}
		if len(req.FqChannelDistributorIds) > 0 {
			m = m.WhereIn("sc.fq_distributor_id", req.FqChannelDistributorIds)
		}
		if len(req.DzAccountIds) > 0 {
			m = m.WhereIn("sc.dz_account_id", req.DzAccountIds)
		}
		if len(req.DzChannelIds) > 0 {
			m = m.WhereIn("sc.dz_channel_id", req.DzChannelIds)
		}
		if req.Keyword != "" {
			m = m.Where("sc.channel_code like ?", "%"+req.Keyword+"%")
		}
		if req.RemarkKeyword != "" {
			m = m.Where("sc.remark like ?", "%"+req.RemarkKeyword+"%")
		}
		if req.StartTime != "" && req.EndTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.WhereGTE("sc.createtime", dayStartTime)
			m = m.WhereLTE("sc.createtime", dayEndTime)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "sc.createtime desc, sc.id desc"
		var res []*model.SChannelAdSettingListRes
		err = m.Fields("sc.id as id").
			Fields("sc.channel_code as channelCode").
			Fields("sc.createtime as createTime").
			Fields("sc.updatetime as updateTime").
			Fields("sc.remark as remark").
			Fields("sc.lock_num as lockNum").
			Fields("sc.buy_price_gold as buyPriceGold").
			Fields("sc.is_default as isDefault").
			Fields("sc.user_id as userId").
			Fields("sc.template_id as templateId").
			Fields("sc.ad_setting_template_id as adSettingTemplateId").
			Fields("ad.advertiser_ids as advertiserIds").
			Fields("su.user_name as userName").
			Fields("ad.id as adSettingId").
			Fields("ad.app_id as appId").
			Fields("ad.channel_from as adPlatform").
			Fields("ad.reward_id as rewardId").
			Fields("adl.app_id as appId").
			Fields("adl.video_id as parentId").
			Fields("adl.num as num").
			Fields("adl.platform as platform").
			Fields("adl.unlock_switch as unlockSwitch").
			Fields("adl.unlock_episodes as unlockEpisodes").
			Fields("adl.link as link").
			Fields("adl.id as adDiversionLinkId").
			Fields("adl.video_account_id as videoAccountId").
			Fields("sc.channel_platform as channelPlatform").
			Fields("sc.fq_app_type as fqAppType").
			Fields("sc.fq_distributor_id as fqChannelDistributorId").
			Fields("sc.fq_recharge_template_id as fqRechargeTemplateId").
			Fields("sc.fq_recharge_template_name as fqRechargeTemplateName").
			Fields("sc.fq_callback_config_id as fqCallbackConfigId").
			Fields("sc.fq_callback_config_name as fqCallbackConfigName").
			Fields("sc.fq_pack_strategy_status as fqPackStrategyStatus").
			Fields("sc.fq_incentive_ad_status as fqIncentiveAdStatus").
			Fields("sc.fq_promotion_id as fqPromotionId").
			Fields("sc.open_retain_window as openRetainWindow").
			Fields("sc.retain_title as retainTitle").
			Fields("sc.retain_unlock_episodes_num as retainUnlockEpisodesNum").
			Fields("sc.retain_day_views_num as retainDayViewsNum").
			Fields("sc.retain_window_seconds as retainWindowSeconds").
			Fields("sc.continuous_iaa_retain as continuousIaaRetain").
			Fields("sc.continuous_unlock_info as continuousUnlockInfo").
			Fields("adl.fq_app_id as fqAppId").
			Fields("adl.fq_app_name as fqAppName").
			Fields("adl.fq_book_id as fqBookId").
			Fields("adl.fq_book_name as fqBookName").
			Fields("adl.fq_wx_video_id as fqWxVideoId").
			Fields("sc.dz_account_id as dzAccountId").
			Fields("sc.dz_recharge_template_id as dzRechargeTemplateId").
			Fields("sc.dz_recharge_template_name as dzRechargeTemplateName").
			Fields("sc.dz_callback_config_id as dzCallbackConfigId").
			Fields("sc.dz_callback_config_name as dzCallbackConfigName").
			Fields("sc.dz_channel_id as dzChannelId").
			Fields("sc.dz_from_dr_id as dzFromDrId").
			Fields("sc.dz_referral_id as dzReferralId").
			Fields("adl.dz_book_id as dzBookId").
			Fields("adl.dz_book_name as dzBookName").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 查询充值模板
		templateIds := make([]int, 0)
		// 查询回传模板
		adSettingTemplateIds := make([]int64, 0)
		// 查询小程序名称
		appIds := make([]string, 0)
		// 查询短剧名称
		videoIds := make([]string, 0)
		distributorIds := make([]string, 0)
		dzChannelIds := make([]string, 0)
		for _, v := range res {
			if v.TemplateId > 0 {
				templateIds = append(templateIds, v.TemplateId)
			}
			if v.AdSettingTemplateId > 0 {
				adSettingTemplateIds = append(adSettingTemplateIds, v.AdSettingTemplateId)
			}
			if v.AppId != "" {
				appIds = append(appIds, v.AppId)
			}
			if v.ParentId > 0 {
				videoIds = append(videoIds, gconv.String(v.ParentId))
			}
			if v.FqChannelDistributorId != "" {
				if !libUtils.FindTargetStr(distributorIds, v.FqChannelDistributorId) {
					distributorIds = append(distributorIds, v.FqChannelDistributorId)
				}
			}
			if v.DzChannelId != "" {
				if !libUtils.FindTargetStr(dzChannelIds, v.DzChannelId) {
					dzChannelIds = append(dzChannelIds, v.DzChannelId)
				}
			}
		}
		adSettingTemplates, _ := service2.AdSettingTemplate().GetByIds(ctx, adSettingTemplateIds)
		rechargeTemplates, _ := service2.RechargeTemplate().GetByIds(ctx, templateIds)
		platRules, _ := service.SPlatRules().GetMiniInfoByAppIds(ctx, appIds)
		theaterInfos, _ := theaterService.TheaterInfo().GetVideoByIds(ctx, videoIds)
		fqAdAccountChannels, _ := adService.FqAdAccountChannel().GetByChannelDistributorIds(ctx, gconv.Int64s(distributorIds))
		dzChannels, _ := adService.DzAdAccountChannel().GetByChannelIds(ctx, gconv.Int64s(dzChannelIds))
		listRes.List = make([]*model.SChannelAdSettingListRes, len(res))
		for k, v := range res {
			channelAdSettingListRes := v
			// 微信视频号特殊处理
			if v.Platform == commonConsts.WXShiPingHao {
				channelAdSettingListRes.Link = fmt.Sprintf("视频号ID:%s", v.VideoAccountId)
			}
			// 获取充值模板名称
			if v.TemplateId == 0 {
				channelAdSettingListRes.TemplateName = commonConsts.DefaultChannelName
			} else {
				for _, rechargeTemplate := range rechargeTemplates {
					if gconv.Int(rechargeTemplate.Id) == v.TemplateId {
						var templateName = rechargeTemplate.Name
						if templateName == commonConsts.DefaultChannel {
							templateName = commonConsts.DefaultChannelName
						}
						channelAdSettingListRes.TemplateName = templateName
						break
					}
				}
			}
			// 获取回传模板名称
			if v.AdSettingTemplateId == 0 {
				channelAdSettingListRes.AdSettingTemplateName = commonConsts.CustomAdSettingTemplateName
			} else {
				for _, adSettingTemplate := range adSettingTemplates {
					if adSettingTemplate.Id == v.AdSettingTemplateId {
						var adSettingTemplateName = adSettingTemplate.Name
						if adSettingTemplateName == commonConsts.DefaultChannel {
							adSettingTemplateName = commonConsts.DefaultChannelName
						}
						channelAdSettingListRes.AdSettingTemplateName = adSettingTemplateName
						break
					}
				}
			}
			// 获取小程序名称
			for _, platRule := range platRules {
				if v.AppId == platRule.AppId {
					channelAdSettingListRes.AppName = platRule.AppName
					break
				}
			}
			// 获取短剧名称
			for _, theaterInfo := range theaterInfos {
				if gconv.Int(theaterInfo.Id) == v.ParentId {
					channelAdSettingListRes.TheaterTitle = theaterInfo.Title
					break
				}
			}
			// 番茄渠道
			if channelAdSettingListRes.ChannelPlatform == commonConsts.FanQiePlatform {
				channelAdSettingListRes.AppId, _ = libUtils.ExtractAppID(channelAdSettingListRes.Link)
				channelAdSettingListRes.AppName = channelAdSettingListRes.FqAppName
				channelAdSettingListRes.TheaterTitle = channelAdSettingListRes.FqBookName
				if channelAdSettingListRes.Platform == commonConsts.WXShiPingHao && channelAdSettingListRes.FqWxVideoId != nil {
					channelAdSettingListRes.Link = fmt.Sprintf("视频ID:%s", *channelAdSettingListRes.FqWxVideoId)
				}
				if channelAdSettingListRes.FqRechargeTemplateName != nil && *channelAdSettingListRes.FqRechargeTemplateName != "" {
					channelAdSettingListRes.TemplateName = *channelAdSettingListRes.FqRechargeTemplateName
				}
				if channelAdSettingListRes.FqCallbackConfigName != nil && *channelAdSettingListRes.FqCallbackConfigName != "" {
					channelAdSettingListRes.AdSettingTemplateName = *channelAdSettingListRes.FqCallbackConfigName
				}
				for _, fqAdAccountChannel := range fqAdAccountChannels {
					if gconv.String(fqAdAccountChannel.ChannelDistributorId) == channelAdSettingListRes.FqChannelDistributorId {
						channelAdSettingListRes.FqDistributorName = fqAdAccountChannel.NickName
						break
					}
				}
			}
			if channelAdSettingListRes.ChannelPlatform == commonConsts.DianZhongPlatform {
				channelAdSettingListRes.TheaterTitle = channelAdSettingListRes.DzBookName
				if channelAdSettingListRes.DzRechargeTemplateName != "" {
					channelAdSettingListRes.TemplateName = channelAdSettingListRes.DzRechargeTemplateName
				}
				if channelAdSettingListRes.DzCallbackConfigName != "" {
					channelAdSettingListRes.AdSettingTemplateName = channelAdSettingListRes.DzCallbackConfigName
				}
				for _, dzChannel := range dzChannels {
					if gconv.String(dzChannel.ChannelId) == channelAdSettingListRes.DzChannelId {
						channelAdSettingListRes.DzAccountName = dzChannel.NickName
						channelAdSettingListRes.AppId = dzChannel.OfAppId
						break
					}
				}
			}
			listRes.List[k] = channelAdSettingListRes
		}
	})
	return
}

func (s *sSChannel) RemoveChannelCache(ctx context.Context, userIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		ctx = context.Background()
		var pageNum = 1
		var pageSize = 10
		for {
			var channelList []*model.SChannelInfoRes
			err = dao2.SChannel.Ctx(ctx).WithAll().
				Where(dao2.SChannel.Columns().UserId, userIds).
				Page(pageNum, pageSize).
				Scan(&channelList)
			liberr.ErrIsNil(ctx, err, "获取渠道列表失败")
			if channelList == nil || len(channelList) == 0 {
				break
			}
			for _, channel := range channelList {
				var key = fmt.Sprintf("%s%s", commonConsts.PlatUserChannelInfo, channel.ChannelCode)
				data, _ := commonService.PersistenceRedisCache().Get(ctx, key)
				if !data.IsNil() {
					_, err = commonService.PersistenceRedisCache().Del(ctx, key)
					liberr.ErrIsNil(ctx, err, "PersistenceRedisCache del error key :"+key)
				}
				var newKey = fmt.Sprintf("%s%s", commonConsts.PlatUserNewChannelInfo, channel.ChannelCode)
				newData, _ := commonService.PersistenceRedisCache().Get(ctx, newKey)
				if !newData.IsNil() {
					_, err = commonService.PersistenceRedisCache().Del(ctx, newKey)
					liberr.ErrIsNil(ctx, err, "PersistenceRedisCache del error key :"+newKey)
				}
			}
			pageNum++
		}
	})
	return
}

func (s *sSChannel) GetChannelByCode(ctx context.Context, channelCode string) (response *model.GetChannelByCode, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var entityStr = ""
		response = new(model.GetChannelByCode)
		var key = fmt.Sprintf("%s%s", commonConsts.PlatUserChannelInfo, channelCode)
		data, err2 := commonService.PersistenceRedisCache().Get(ctx, key)
		liberr.ErrIsNil(ctx, err2)
		if !data.IsNil() {
			if v, ok := data.Val().(string); ok {
				entityStr, _ = strconv.Unquote(v)
			}
		}
		if len(entityStr) > 0 {
			err = json.Unmarshal([]byte(entityStr), response)
			liberr.ErrIsNil(ctx, err)
			return
		} else { //  redis 中不存在 則查詢到之後去緩存
			var record gdb.Record
			record, err = dao2.SChannel.Ctx(ctx).WithAll().
				As("c").
				LeftJoin("s_user u", "c.user_id=u.id").
				Where("c.channel_code = ? and  u.id > 0 and c.del_flag = 0", channelCode).
				Fields("u.id as id, u.pitcher as pitcher, u.parent_id as parentId").One()
			liberr.ErrIsNil(ctx, err)
			if record != nil {
				if record["id"].Int() != 1 && record["pitcher"].Int() == 1 {
					response.DistributorId = record["parentId"].Int()
					response.UserId = record["id"].Int()
				} else {
					response.DistributorId = record["id"].Int()
				}
			}
			str, innerError := libUtils.SerializeQuoteStruct(response)
			if innerError != nil {
				liberr.ErrIsNil(ctx, innerError, "SerializeQuoteStruct SCustomerQrCodeConfig失败")
			} else {
				_, err = commonService.PersistenceRedisCache().Set(ctx, key, str)
				liberr.ErrIsNil(ctx, err, " RedisCache Set Error key :"+key)
			}
		}
	})
	return
}

func (s *sSChannel) GetAllChannelByCode(ctx context.Context) (response []*model.GetAllChannelByCode, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []model.ChannelByCode
		//  redis 中不存在 則查詢到之後去緩存
		//var record gdb.Record
		err = dao2.SChannel.Ctx(ctx).WithAll().
			As("c").
			LeftJoin("s_user u", "c.user_id=u.id").
			Where("u.id > 0 and c.del_flag = 0").
			Fields("u.id as id, u.pitcher as pitcher, u.parent_id as parentId ,channel_code").Scan(&list)
		liberr.ErrIsNil(ctx, err)
		for _, record := range list {
			channelCode := new(model.GetAllChannelByCode)
			channelCode.ChannelCode = record.ChannelCode
			if record.Id != 1 && record.Pitcher == 1 {
				channelCode.DistributorId = record.ParentId
				channelCode.UserId = record.Id
			} else {
				channelCode.DistributorId = record.Id
			}
			response = append(response, channelCode)
		}
	})
	return
}

func (s *sSChannel) UpdatePanelId(ctx context.Context, req *model.SChannelUpdatePanelIdReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		channel := do.SChannel{
			Id:         req.Id,
			Updatetime: gtime.Now(),
		}
		if req.PanelId != "" {
			channel.PanelId = req.PanelId
		}
		_, err1 := dao2.SChannel.Ctx(ctx).WherePri(req.Id).Update(channel)
		liberr.ErrIsNil(ctx, err1, "修改渠道panelId异常")
	})
	return
}

func (s *sSChannel) UpdateTemplateId(ctx context.Context, channelCode string, templateId int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		channel := do.SChannel{
			TemplateId: templateId,
			Updatetime: gtime.Now(),
		}
		_, err1 := dao2.SChannel.Ctx(ctx).Where(dao2.SChannel.Columns().ChannelCode, channelCode).Update(channel)
		liberr.ErrIsNil(ctx, err1, "修改渠道充值模板ID异常")
	})
	return
}

func (s *sSChannel) UpdateProductByTemplateId(ctx context.Context, channelCode string, curTemplateId int, templateId int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if curTemplateId == templateId {
			return
		}
		err1 := s.UpdateTemplateId(ctx, channelCode, templateId)
		liberr.ErrIsNil(ctx, err1, "修改渠道充值模板ID异常")
		////把当前模板的充值数据（或默认模板） 添加到product
		//searchReq := &model.PProductSearchReq{
		//	TemplateId: templateId,
		//}
		//record, err2 := service2.PProduct().GetRecharge(ctx, searchReq)
		//liberr.ErrIsNil(ctx, err2, "查询当前充值模板的产品出错")
		//var addProductArr []*model.PProductAddReq
		//if record == nil {
		//	return
		//}
		//// 删除当前渠道使用的充值面板
		//err1 := service2.PProduct().DeleteByChannel(ctx, channelCode)
		//liberr.ErrIsNil(ctx, err1, "删除充值面板失败")
		//if record.FirstList != nil && len(record.FirstList) > 0 {
		//	for _, v := range record.FirstList {
		//		var productAddReq = &model.PProductAddReq{}
		//		err = gconv.Struct(v, productAddReq)
		//		liberr.ErrIsNil(ctx, err)
		//		productAddReq.ChannelCode = channelCode
		//		productAddReq.TemplateId = 0
		//		productAddReq.IsDefault = 1
		//		addProductArr = append(addProductArr, productAddReq)
		//	}
		//}
		//if record.RepeatList != nil && len(record.RepeatList) > 0 {
		//	for _, v := range record.RepeatList {
		//		var productAddReq = &model.PProductAddReq{}
		//		err = gconv.Struct(v, productAddReq)
		//		liberr.ErrIsNil(ctx, err)
		//		productAddReq.ChannelCode = channelCode
		//		productAddReq.TemplateId = 0
		//		productAddReq.IsDefault = 1
		//		addProductArr = append(addProductArr, productAddReq)
		//	}
		//}
		////批量插入
		//err3 := service2.PProduct().BatchAddPro(ctx, addProductArr)
		//liberr.ErrIsNil(ctx, err3, "批量添加面板失败")
	})
	return
}

func (s *sSChannel) UpdateChannel(ctx context.Context, req *model.SChannelEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao2.SChannel.Ctx(ctx).WherePri(req.Id).Update(do.SChannel{
			Updatetime:   gtime.Now(),
			BuyPriceGold: req.BuyPriceGold,
			LockNum:      req.LockNum,
			TemplateId:   req.TemplateId,
		})
	})
	return
}

func (s *sSChannel) ChannelList(ctx context.Context, req model.AdAssetWechatAppletChannelReq) (listRes *model.AdAssetWechatAppletChannelRes, err error) {
	listRes = new(model.AdAssetWechatAppletChannelRes)
	if req.Channels == nil || len(req.Channels) == 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		channelList, err1 := s.GetChannelByChannelCodes(ctx, req.Channels)
		liberr.ErrIsNil(ctx, err1, "获取渠道列表失败")
		adSettings, err1 := service2.AdSetting().GetByAccountList(ctx, req.Channels)
		liberr.ErrIsNil(ctx, err1, "获取渠道回传配置列表失败")
		adLinks, err1 := service2.AdDiversionLink().GetByAccounts(ctx, req.Channels, req.AppType)
		liberr.ErrIsNil(ctx, err1, "获取渠道导流链接失败")
		list := make([]*model.AdAssetWechatAppletChannelListRes, 0)
		for _, channel := range channelList {
			innerList := make([]*model.AdAssetWechatAppletChannelListRes, 0)
			for _, adLink := range adLinks {
				if channel.ChannelCode == adLink.Account {
					innerList = append(innerList, &model.AdAssetWechatAppletChannelListRes{
						Channel: channel.ChannelCode,
						Remark:  channel.Remark,
						UserId:  channel.UserId,
						AppId:   adLink.AppId,
						VideoId: adLink.VideoId,
						Link:    adLink.Link,
					})
				}
			}
			var advertiserIds string
			for _, adSetting := range adSettings {
				if channel.ChannelCode == adSetting.SubChannel {
					advertiserIds = adSetting.AdvertiserIds
					break
				}
			}
			advertiserIdList := strings.Split(advertiserIds, ",")
			if len(advertiserIdList) == 0 {
				list = append(list, innerList...)
				continue
			}
			if len(innerList) == 0 {
				for _, advertiserId := range advertiserIdList {
					list = append(list, &model.AdAssetWechatAppletChannelListRes{
						Channel:      channel.ChannelCode,
						Remark:       channel.Remark,
						AdvertiserId: advertiserId,
						UserId:       channel.UserId,
					})
				}
				continue
			}
			for _, advertiserId := range advertiserIdList {
				for _, inner := range innerList {
					list = append(list, &model.AdAssetWechatAppletChannelListRes{
						Channel:      channel.ChannelCode,
						Remark:       channel.Remark,
						AdvertiserId: advertiserId,
						AppId:        inner.AppId,
						VideoId:      inner.VideoId,
						Link:         inner.Link,
						UserId:       channel.UserId,
					})
				}
			}
		}
		var channelInfoList []*model.AdAssetWechatAppletChannelListRes
		// 只显示可选
		if req.ShowOnlyOptional == 1 {
			for _, v := range list {
				if v.AdvertiserId != "" && v.Link != "" {
					channelInfoList = append(channelInfoList, v)
				}
			}
		} else {
			channelInfoList = list
		}
		// 分页
		listRes.CurrentPage = req.PageNum
		listRes.Total = len(channelInfoList)
		listRes.List = channelInfoList[req.PageSize*(req.PageNum-1) : gconv.Int(math.Min(float64(req.PageSize*req.PageNum), float64(len(channelInfoList))))]
		appIdSet := gset.NewStrSet()
		videoIdSet := gset.NewStrSet()
		userIdSet := gset.NewIntSet()
		for _, res := range listRes.List {
			userIdSet.Add(res.UserId)
			if res.AppId != "" {
				appIdSet.Add(res.AppId)
			}
			if res.VideoId != 0 {
				videoIdSet.Add(gconv.String(res.VideoId))
			}
		}
		miniInfos, _ := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIdSet.Slice())
		theaterInfos, _ := theaterService.TheaterInfo().GetVideoByIds(ctx, videoIdSet.Slice())
		userInfos, _ := sysService.SysUser().GetUserByIds(ctx, gconv.Uint64s(userIdSet.Slice()))
		for _, res := range listRes.List {
			for _, miniInfo := range miniInfos {
				if res.AppId == miniInfo.AppId {
					res.AppName = miniInfo.AppName
					break
				}
			}
			for _, theaterInfo := range theaterInfos {
				if gconv.Uint(res.VideoId) == theaterInfo.Id {
					res.Title = theaterInfo.Title
					break
				}
			}
			for _, userInfo := range userInfos {
				if gconv.Uint64(res.UserId) == userInfo.Id {
					res.UserName = userInfo.UserName
					break
				}
			}
		}
	})
	return
}

func (s *sSChannel) GetByAdSettingTemplateIds(ctx context.Context, templateIds []int64) (res []*model.SChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao2.SChannel.Ctx(ctx).WithAll().
			WhereIn(dao2.SChannel.Columns().AdSettingTemplateId, templateIds).
			Where(dao2.SChannel.Columns().DelFlag, 0).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannel) GetChannelOwnerInfo(ctx context.Context, account string) (channelInfo *model.SChannelInfo, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		channelKey := "PLAT:USER:NEW:CHANNEL:INFO:" + account
		data, _ := commonService.PersistenceRedisCache().Get(ctx, channelKey)
		if !data.IsNil() {
			if v, ok := data.Val().(string); ok {
				dataStr, _ := strconv.Unquote(v)
				err = json.Unmarshal([]byte(dataStr), &channelInfo)
			}
		} else {
			err = dao2.SChannel.Ctx(ctx).Raw(`
			 SELECT su.id as distributorId,c.user_id as userId 
			 FROM s_channel as c 
			     LEFT JOIN sys_user as u ON c.user_id = u.id 
			     LEFT JOIN sys_dept as d ON d.dept_id = u.dept_id 
			     LEFT JOIN sys_user as su ON su.user_name = d.leader 
			 WHERE c.channel_code = ? AND c.del_flag = 0
		`, account).Scan(&channelInfo)
			liberr.ErrIsNil(ctx, err, "获取信息失败")
		}
		if channelInfo == nil {
			return
		}
		if channelInfo.DistributorId == channelInfo.UserId {
			channelInfo.UserId = 0
		}
	})
	return
}
