// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/model/entity/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzNativeLink is the golang structure for table dz_native_link.
type DzNativeLink struct {
	gmeta.Meta        `orm:"table:dz_native_link"`
	Id                uint64      `orm:"id,primary" json:"id"`                         // 主键ID
	ProjectId         string      `orm:"project_id" json:"projectId"`                  // 项目ID
	BookId            string      `orm:"book_id" json:"bookId"`                        // 书籍ID
	BookName          string      `orm:"book_name" json:"bookName"`                    // 书籍名称
	DyBookId          string      `orm:"dy_book_id" json:"dyBookId"`                   // 抖音书籍ID
	AccountUserName   string      `orm:"account_user_name" json:"accountUserName"`     // 账户用户名
	DistributorId     string      `orm:"distributor_id" json:"distributorId"`          // 分销商ID
	DistributorName   string      `orm:"distributor_name" json:"distributorName"`      // 分销商名称
	ChannelId         int64       `orm:"channel_id" json:"channelId"`                  // 渠道ID
	PurchasePanelId   string      `orm:"purchase_panel_id" json:"purchasePanelId"`     // 购买面板ID
	PurchasePanelName string      `orm:"purchase_panel_name" json:"purchasePanelName"` // 购买面板名称
	PurchasePanelJson string      `orm:"purchase_panel_json" json:"purchasePanelJson"` // 购买面板JSON配置
	AdvertiseLink     string      `orm:"advertise_link" json:"advertiseLink"`          // 广告推广链接
	TaskStatus        int         `orm:"task_status" json:"taskStatus"`                // 任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败
	Response          string      `orm:"response" json:"response"`                     // 接口响应信息
	CreateTime        *gtime.Time `orm:"create_time" json:"createTime"`                // 创建时间
	UpdateTime        *gtime.Time `orm:"update_time" json:"updateTime"`                // 更新时间
	BookStatus        int         `orm:"book_status" json:"bookStatus"`                // 书籍状态：1-未上架，2-已上架，3-已下架
	BookType          int         `orm:"book_type" json:"bookType"`                    // 书籍类型：1-短剧，2-小说
	Connect           int         `orm:"connect" json:"connect"`                       // 连接状态：0-未连接，1-已连接
	PutTimeText       string      `orm:"put_time_text" json:"putTimeText"`             // 投放时间文本
	Type              int         `orm:"type" json:"type"`                             // 类型 1-付费 2-免费
}
