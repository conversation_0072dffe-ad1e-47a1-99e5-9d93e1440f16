package api

import (
	"context"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/gogf/gf/v2/frame/g"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"net/http"
	"strings"
	"sync"
	"time"
)

type AdKSService struct {
	AppId  int64  `json:"appId"`
	Secret string `json:"secret"`
}

var ()

var (
	instance    *AdKSIClient
	once        sync.Once
	ksAdService *AdKSService
)

func init() {
	ksAdService = new(AdKSService)
	g.Cfg().MustGet(context.Background(), "advertiser.ks").Scan(&ksAdService)
}

type Configuration struct {
	BasePath   string `json:"basePath,omitempty"`
	HTTPClient *resty.Client
}

func NewConfiguration() *Configuration {
	cfg := &Configuration{
		BasePath:   "https://ad.e.kuaishou.com",
		HTTPClient: resty.New(),
	}
	return cfg
}

func NewAPIClient(cfg *Configuration) *AdKSIClient {
	if cfg.HTTPClient == nil {
		cfg.HTTPClient = resty.New()
	}
	c := &AdKSIClient{}
	c.Cfg = cfg
	c.GetTokenService = new(GetTokenService)
	c.GetTokenService.SetCfg(c.Cfg)

	c.GetAdListService = new(GetAdListService)
	c.GetTokenService.SetCfg(c.Cfg)

	//*GetReTokenService
	c.GetReTokenService = new(GetReTokenService)
	c.GetReTokenService.SetCfg(c.Cfg)
	//*QueryCoreDataService
	c.QueryCoreDataService = new(QueryCoreDataService)
	c.QueryCoreDataService.SetCfg(c.Cfg)
	//*QueryAdDataService
	c.QueryAdDataService = new(QueryAdDataService)
	c.QueryAdDataService.SetCfg(c.Cfg)
	//*QuerySalerCopyRightDataService
	c.QuerySalerCopyRightDataService = new(QuerySalerCopyRightDataService)
	c.QuerySalerCopyRightDataService.SetCfg(c.Cfg)
	//*QuerySettleDataService
	c.QuerySettleDataService = new(QuerySettleDataService)
	c.QuerySettleDataService.SetCfg(c.Cfg)
	//*QueryAccountInfoService
	c.QueryAccountInfoService = new(QueryAccountInfoService)
	c.QueryAccountInfoService.SetCfg(c.Cfg)
	//QueryAdDetailService
	c.QueryAdDetailService = new(QueryAdDetailService)
	c.QueryAdDetailService.SetCfg(c.Cfg)
	// QuerySeriesInfoService
	c.QuerySeriesInfoService = new(QuerySeriesInfoService)
	c.QuerySeriesInfoService.SetCfg(c.Cfg)
	// QueryOrderDetailService
	c.QueryOrderDetailService = new(QueryOrderDetailService)
	c.QueryOrderDetailService.SetCfg(c.Cfg)

	return c
}

// GetKSApiClient 获取快手的API客户端
func GetKSApiClient() *AdKSIClient {
	once.Do(func() {
		configuration := NewConfiguration()
		client := resty.New()
		client.SetTimeout(10 * time.Second) //设置全局超时时间
		client.SetTransport(&http.Transport{
			MaxIdleConnsPerHost:   10,               // 对于每个主机，保持最大空闲连接数为 10
			IdleConnTimeout:       30 * time.Second, // 空闲连接超时时间为 30 秒
			TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时时间为 10 秒
			ResponseHeaderTimeout: 20 * time.Second, // 等待响应头的超时时间为 20 秒
		})
		configuration.HTTPClient = client
		instance = NewAPIClient(configuration)
	})
	return instance
}

// KsBaseResp 泛型结构体
type KsBaseResp[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    *T     `json:"data"`
}

const (
	KsRefreshToken   = "KS:REFRESH:TOKEN:"
	KsAccessToken    = "KS:ACCESS:TOKEN:"
	KsAdvertiserList = "KS:ADVERTISER:List:"
)

func GetAccessTokenByAppIdCache(aId int64) (token string) {
	//查询广告主所属商务账号
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	token = commonService.GetGoRedis().Get(ctx, fmt.Sprintf("%s%v", KsAccessToken, aId)).Val()
	if len(token) == 0 {
		// 刷新token 并更新缓存
		reToken := commonService.GetGoRedis().Get(ctx, fmt.Sprintf("%s%v", KsRefreshToken, aId)).Val()
		reToken = strings.ReplaceAll(reToken, `"`, "")
		reTokenRes, err := GetKSApiClient().GetReTokenService.SetReq(GetReTokenReq{
			AppId:        ksAdService.AppId,
			Secret:       ksAdService.Secret,
			RefreshToken: reToken,
		}).Do()
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
		if reTokenRes.Code != 0 {
			err = fmt.Errorf("GetKSAccessTokenByAppIdCache失败: %s", reTokenRes.Message)
			return
		}
		token = reTokenRes.Data.AccessToken
		// 设置token
		var accessToken = reTokenRes.Data.AccessToken
		var expiresIn = reTokenRes.Data.AccessTokenExpiresIn
		var refreshToken = reTokenRes.Data.RefreshToken
		var refreshTokenExpiresIn = reTokenRes.Data.RefreshTokenExpiresIn
		// 给渠道获取消耗的appId
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", KsRefreshToken, aId), fmt.Sprintf(`"%s"`, refreshToken), time.Duration(refreshTokenExpiresIn)*time.Second)
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf("%s%v", KsAccessToken, aId), fmt.Sprintf(`"%s"`, accessToken), time.Duration(expiresIn)*time.Second)
	}
	token = strings.ReplaceAll(token, `"`, "")
	return
}
