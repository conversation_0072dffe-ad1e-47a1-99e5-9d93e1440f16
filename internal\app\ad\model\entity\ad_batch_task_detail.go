// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/model/entity/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdBatchTaskDetail is the golang structure for table ad_batch_task_detail.
type AdBatchTaskDetail struct {
	gmeta.Meta               `orm:"table:ad_batch_task_detail"`
	Id                       int64       `orm:"id,primary" json:"id"`                                       //
	TaskId                   string      `orm:"task_id" json:"taskId"`                                      // 任务ID
	SerialNumber             int         `orm:"serial_number" json:"serialNumber"`                          // 序号 任务中的执行顺序
	AdvertiserId             string      `orm:"advertiser_id" json:"advertiserId"`                          // 媒体账户ID
	OriginalAdvertiserName   string      `orm:"original_advertiser_name" json:"originalAdvertiserName"`     // 原账户名称
	NewAdvertiserName        string      `orm:"new_advertiser_name" json:"newAdvertiserName"`               // 新账户名称
	OriginalAdvertiserRemark string      `orm:"original_advertiser_remark" json:"originalAdvertiserRemark"` // 原账户备注
	NewAdvertiserRemark      string      `orm:"new_advertiser_remark" json:"newAdvertiserRemark"`           // 新账户备注
	OptResult                string      `orm:"opt_result" json:"optResult"`                                // 执行结果：SUCCESS：成功  FAIL：失败
	ErrMsg                   string      `orm:"err_msg" json:"errMsg"`                                      // 失败原因
	CreatedAt                *gtime.Time `orm:"created_at" json:"createdAt"`                                // 创建时间
	UpdatedAt                *gtime.Time `orm:"updated_at" json:"updatedAt"`                                // 更新时间
	DeletedAt                *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                // 删除时间
}
