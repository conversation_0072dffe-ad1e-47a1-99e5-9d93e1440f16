// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-09 17:30:45
// 生成路径: internal/app/channel/service/s_channel_push_task.go
// 生成人：cq
// desc:渠道推送任务
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

type ISChannelPushTask interface {
	List(ctx context.Context, req *model.SChannelPushTaskSearchReq) (res *model.SChannelPushTaskSearchRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.SChannelPushTaskInfoRes, err error)
	GetByTaskId(ctx context.Context, taskId string) (res *model.SChannelPushTaskInfoRes, err error)
	Add(ctx context.Context, req *model.SChannelPushTaskAddReq) (err error)
	Edit(ctx context.Context, req *model.SChannelPushTaskEditReq) (err error)
	EditOptStatus(ctx context.Context, req *model.SChannelPushTaskEditReq) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
}

var localSChannelPushTask ISChannelPushTask

func SChannelPushTask() ISChannelPushTask {
	if localSChannelPushTask == nil {
		panic("implement not found for interface ISChannelPushTask, forgot register?")
	}
	return localSChannelPushTask
}

func RegisterSChannelPushTask(i ISChannelPushTask) {
	localSChannelPushTask = i
}
