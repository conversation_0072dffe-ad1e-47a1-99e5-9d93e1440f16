// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/logic/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"net/url"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAppConfig(New())
}

func New() service.IAdAppConfig {
	return &sAdAppConfig{}
}

type sAdAppConfig struct{}

func (s *sAdAppConfig) List(ctx context.Context, req *model.AdAppConfigSearchReq) (listRes *model.AdAppConfigSearchRes, err error) {
	listRes = new(model.AdAppConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAppConfig.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdAppConfig.Columns().Id+" = ?", req.Id)
		}
		if req.AppId != "" {
			m = m.Where(dao.AdAppConfig.Columns().AppId+" = ?", req.AppId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAppConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAppConfigListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdAppConfigListRes{
				Id:           v.Id,
				AppId:        v.AppId,
				Secret:       v.Secret,
				Type:         v.Type,
				AuthNums:     v.AuthNums,
				AuthUserType: v.AuthUserType,
				CreatedAt:    v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdAppConfig) GetById(ctx context.Context, id int) (res *model.AdAppConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAppConfig.Ctx(ctx).WithAll().Where(dao.AdAppConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAppConfig) Add(ctx context.Context, req *model.AdAppConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAppConfig.Ctx(ctx).Insert(do.AdAppConfig{
			AppId:        req.AppId,
			Secret:       req.Secret,
			Type:         req.Type,
			AuthNums:     req.AuthNums,
			AuthUserType: req.AuthUserType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAppConfig) Edit(ctx context.Context, req *model.AdAppConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		appConfig := do.AdAppConfig{}
		if req.AppId != "" {
			appConfig.AppId = req.AppId
		}
		if req.Secret != "" {
			appConfig.Secret = req.Secret
		}
		if req.Type != 0 {
			appConfig.Type = req.Type
		}
		if req.AuthNums != 0 {
			appConfig.AuthNums = req.AuthNums
		}
		if req.AuthUserType != 0 {
			appConfig.AuthUserType = req.AuthUserType
		}
		_, err = dao.AdAppConfig.Ctx(ctx).WherePri(req.Id).Update(appConfig)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAppConfig) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAppConfig.Ctx(ctx).Delete(dao.AdAppConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdAppConfig) GetAuthUrl(ctx context.Context, envType int32, appType int32, authUserType *int32, authUserId *string) (authUrl string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var callbackUrl string
		if appType == 1 {
			callbackUrl = g.Cfg().MustGet(ctx, "advertiser.toutiao.oauth2CallbackUrl").String()
		} else if appType == 2 {
			callbackUrl = g.Cfg().MustGet(ctx, "advertiser.tencent.oauth2CallbackUrl").String()
		} else {
			liberr.ErrIsNil(ctx, errors.New("广告平台类型错误"))
		}
		// 查询用户授权来源类型
		if authUserId != nil && *authUserId != "" {
			existAdMajordomo, _ := oceanengineService.AdMajordomoAdvertiserAccount().GetByUserName(ctx, *authUserId, "")
			if existAdMajordomo != nil {
				var userType int32
				if existAdMajordomo.MajordomoType == commonConsts.AGENT {
					userType = 2
				} else if existAdMajordomo.MajordomoType == commonConsts.CUSTOMER_ADMIN ||
					existAdMajordomo.MajordomoType == commonConsts.CUSTOMER_OPERATOR {
					userType = 1
				}
				authUserType = &userType
			}
		}
		var res *model.AdAppConfigInfoRes
		m := dao.AdAppConfig.Ctx(ctx).
			Where(dao.AdAppConfig.Columns().Type, appType).
			WhereLT(dao.AdAppConfig.Columns().AuthNums, 50)
		if authUserType != nil && *authUserType > 0 {
			m = m.Where(dao.AdAppConfig.Columns().AuthUserType, *authUserType)
		}
		err = m.OrderDesc(dao.AdAppConfig.Columns().AuthNums).Limit(1).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取应用配置失败")
		if res == nil {
			liberr.ErrIsNil(ctx, errors.New("暂无可授权的应用，请联系管理员"))
		}
		userId := sysService.Context().GetUserId(ctx)
		//
		if envType == 2 {
			authUrl = fmt.Sprintf(
				"https://open.oceanengine.com/audit/oauth.html?app_id=%s&state=%v&material_auth=1&redirect_uri=%s",
				res.AppId, fmt.Sprintf("%v_2", userId), url.QueryEscape(callbackUrl))
		} else {
			authUrl = fmt.Sprintf(
				"https://open.oceanengine.com/audit/oauth.html?app_id=%s&state=%v&material_auth=1&redirect_uri=%s",
				res.AppId, userId, url.QueryEscape(callbackUrl))
		}

	})
	return
}

func (s *sAdAppConfig) GetByAppId(ctx context.Context, appId string) (res *model.AdAppConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAppConfig.Ctx(ctx).WithAll().Where(dao.AdAppConfig.Columns().AppId, appId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}
