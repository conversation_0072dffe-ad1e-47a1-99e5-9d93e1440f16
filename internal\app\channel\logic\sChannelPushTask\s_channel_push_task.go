// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-09 17:30:45
// 生成路径: internal/app/channel/logic/s_channel_push_task.go
// 生成人：cq
// desc:渠道推送任务
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/redis/go-redis/v9"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	oceanengineModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/channel/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterSChannelPushTask(New())
	go StartChannelPushTaskConsumer()
	InitStreamCleanupTask()
}

func New() service.ISChannelPushTask {
	return &sSChannelPushTask{}
}

type sSChannelPushTask struct{}

func (s *sSChannelPushTask) List(ctx context.Context, req *model.SChannelPushTaskSearchReq) (listRes *model.SChannelPushTaskSearchRes, err error) {
	listRes = new(model.SChannelPushTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.SChannelPushTask.Ctx(ctx).WithAll().As("t").
			LeftJoin("sys_user u", "t.user_id = u.id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("t."+dao.SChannelPushTask.Columns().UserId, userIds)
		}
		if req.TaskId != "" {
			m = m.Where("t."+dao.SChannelPushTask.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.TaskName != "" {
			m = m.Where("t."+dao.SChannelPushTask.Columns().TaskName+" like ?", "%"+req.TaskName+"%")
		}
		if req.MediaType != "" {
			m = m.Where("t."+dao.SChannelPushTask.Columns().MediaType+" = ?", gconv.Int(req.MediaType))
		}
		if req.PushMethod != "" {
			m = m.Where("t."+dao.SChannelPushTask.Columns().PushMethod+" = ?", gconv.Int(req.PushMethod))
		}
		if req.OptStatus != "" {
			m = m.Where("t."+dao.SChannelPushTask.Columns().OptStatus+" = ?", req.OptStatus)
		}
		if req.EndTime != "" && req.StartTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.WhereGTE("t."+dao.SChannelPushTask.Columns().CreatedAt, dayStartTime)
			m = m.WhereLTE("t."+dao.SChannelPushTask.Columns().CreatedAt, dayEndTime)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "t.id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SChannelPushTaskListRes
		err = m.Fields("t.id as id").
			Fields("t.task_id as taskId").
			Fields("t.task_name as taskName").
			Fields("t.media_type as mediaType").
			Fields("t.push_method as pushMethod").
			Fields("t.push_num as pushNum").
			Fields("t.opt_status as optStatus").
			Fields("t.success_num as successNum").
			Fields("t.fail_num as failNum").
			Fields("t.user_id as userId").
			Fields("u.user_name as userName").
			Fields("t.created_at as createdAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SChannelPushTaskListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.SChannelPushTaskListRes{
				Id:             v.Id,
				TaskId:         v.TaskId,
				TaskName:       v.TaskName,
				MediaType:      v.MediaType,
				MediaTypeName:  commonConsts.MediaTypeMapping[v.MediaType],
				PushMethod:     v.PushMethod,
				PushMethodName: commonConsts.PushMethodMapping[v.PushMethod],
				PushNum:        v.PushNum,
				OptStatus:      v.OptStatus,
				OptStatusName:  commonConsts.OptStatusMapping[v.OptStatus],
				SuccessNum:     v.SuccessNum,
				FailNum:        v.FailNum,
				UserId:         v.UserId,
				UserName:       v.UserName,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sSChannelPushTask) GetById(ctx context.Context, id int64) (res *model.SChannelPushTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SChannelPushTask.Ctx(ctx).WithAll().Where(dao.SChannelPushTask.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelPushTask) GetByTaskId(ctx context.Context, taskId string) (res *model.SChannelPushTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SChannelPushTask.Ctx(ctx).WithAll().Where(dao.SChannelPushTask.Columns().TaskId, taskId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelPushTask) Add(ctx context.Context, req *model.SChannelPushTaskAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SChannelPushTask.Ctx(ctx).Insert(do.SChannelPushTask{
			TaskId:     req.TaskId,
			TaskName:   req.TaskName,
			MediaType:  req.MediaType,
			PushMethod: req.PushMethod,
			PushNum:    req.PushNum,
			OptStatus:  req.OptStatus,
			SuccessNum: req.SuccessNum,
			FailNum:    req.FailNum,
			UserId:     req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSChannelPushTask) Edit(ctx context.Context, req *model.SChannelPushTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SChannelPushTask.Ctx(ctx).WherePri(req.Id).Update(do.SChannelPushTask{
			TaskId:     req.TaskId,
			TaskName:   req.TaskName,
			MediaType:  req.MediaType,
			PushMethod: req.PushMethod,
			PushNum:    req.PushNum,
			OptStatus:  req.OptStatus,
			SuccessNum: req.SuccessNum,
			FailNum:    req.FailNum,
			UserId:     req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSChannelPushTask) EditOptStatus(ctx context.Context, req *model.SChannelPushTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 使用单个原子SQL操作完成所有更新
		_, err = dao.SChannelPushTask.Ctx(ctx).
			Where(dao.SChannelPushTask.Columns().TaskId, req.TaskId).
			Data(g.Map{
				dao.SChannelPushTask.Columns().SuccessNum: gdb.Raw(fmt.Sprintf("%s + %d",
					dao.SChannelPushTask.Columns().SuccessNum, req.SuccessNum)),
				dao.SChannelPushTask.Columns().FailNum: gdb.Raw(fmt.Sprintf("%s + %d",
					dao.SChannelPushTask.Columns().FailNum, req.FailNum)),
				dao.SChannelPushTask.Columns().OptStatus: gdb.Raw(fmt.Sprintf("CASE WHEN ((%s + %d) + (%s + %d)) = %s THEN '%s' ELSE %s END",
					dao.SChannelPushTask.Columns().SuccessNum, req.SuccessNum,
					dao.SChannelPushTask.Columns().FailNum, req.FailNum,
					dao.SChannelPushTask.Columns().PushNum,
					commonConsts.OptStatusCompleted, // 字符串值需要加单引号
					dao.SChannelPushTask.Columns().OptStatus)),
			}).Update()
		liberr.ErrIsNil(ctx, err, "更新任务状态失败")
	})
	return
}

func (s *sSChannelPushTask) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SChannelPushTask.Ctx(ctx).Delete(dao.SChannelPushTask.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// StartChannelPushTaskConsumer 消费者服务
func StartChannelPushTaskConsumer() {
	var ctx = context.Background()
	// 1. 首先检查消费者组是否已存在
	_, err := commonService.GetGoRedis().XInfoGroups(ctx, commonConsts.ChannelPushStream).Result()
	if err != nil {
		// 如果返回的错误是"ERR no such key"，说明消费者组不存在，需要创建
		if strings.Contains(err.Error(), "ERR no such key") {
			// 创建消费者组
			err = commonService.GetGoRedis().XGroupCreateMkStream(ctx,
				commonConsts.ChannelPushStream,
				commonConsts.ChannelPushGroup1,
				"0-0").Err()
			if err != nil {
				g.Log().Errorf(ctx, "创建消费者组失败：%v", err.Error())
				return
			}
		} else {
			g.Log().Errorf(ctx, "检查消费者组是否已存在失败：%v", err.Error())
			return
		}
	}
	for {
		// 2. 处理Pending消息
		processPendingMessages()
		// 3. 然后读取新消息
		xStreams, err1 := commonService.GetGoRedis().XReadGroup(context.Background(), &redis.XReadGroupArgs{
			Group:    commonConsts.ChannelPushGroup1,
			Consumer: commonConsts.ChannelPushConsumer1,
			Streams:  []string{commonConsts.ChannelPushStream, ">"},
			Count:    1,
			Block:    5 * time.Second,
		}).Result()
		if err1 != nil && err1.Error() != "redis: nil" {
			g.Log().Errorf(context.Background(), "读取待处理任务失败：%v", err1)
			continue
		}
		if len(xStreams) == 0 {
			continue
		}
		for _, msg := range xStreams[0].Messages {
			lockKey := fmt.Sprintf("channel_push_task_lock:%s", msg.ID)
			ok, _ := commonService.GetGoRedis().SetNX(ctx, lockKey, 1, 10*time.Second).Result()
			if !ok {
				continue
			}
			var task model.PushTask
			err2 := json.Unmarshal([]byte(msg.Values["push_task_info"].(string)), &task)
			if err2 != nil {
				g.Log().Errorf(context.Background(), "反序列化任务失败：%v", err2)
				commonService.GetGoRedis().Del(ctx, lockKey)
				continue
			}
			// 处理任务
			optResult, err3 := processTask(task)
			if err3 != nil {
				g.Log().Errorf(context.Background(), "处理任务失败：%v", err3)
				commonService.GetGoRedis().Del(ctx, lockKey)
				continue
			}
			// 更新任务状态
			taskEditReq := &model.SChannelPushTaskEditReq{
				TaskId: task.TaskId,
			}
			if optResult == commonConsts.OptResultSuccess {
				taskEditReq.SuccessNum = 1
			} else {
				taskEditReq.FailNum = 1
			}
			_ = service.SChannelPushTask().EditOptStatus(ctx, taskEditReq)
			commonService.GetGoRedis().XAck(ctx,
				commonConsts.ChannelPushStream,
				commonConsts.ChannelPushGroup1,
				msg.ID)
			commonService.GetGoRedis().Del(ctx, lockKey)
		}
	}
}

func processPendingMessages() {
	ctx := context.Background()
	// 1. 首先检查Pending消息数量
	pending, err := commonService.GetGoRedis().XPending(ctx,
		commonConsts.ChannelPushStream,
		commonConsts.ChannelPushGroup1).Result()
	if err != nil {
		g.Log().Errorf(ctx, "检查Pending消息失败: %v", err)
		return
	}
	// 2. 如果有Pending消息
	if pending.Count > 0 {
		// 获取Pending消息详情
		pendingMsgs, err := commonService.GetGoRedis().XPendingExt(ctx, &redis.XPendingExtArgs{
			Stream:   commonConsts.ChannelPushStream,
			Group:    commonConsts.ChannelPushGroup1,
			Start:    "-", // 从最早的消息开始
			End:      "+", // 到最新的消息
			Count:    10,  // 每次取10条
			Consumer: "",  // 不限定消费者
		}).Result()
		if err != nil {
			g.Log().Errorf(ctx, "获取Pending消息详情失败: %v", err)
			return
		}
		// 3. 处理每条Pending消息
		for _, msg := range pendingMsgs {
			lockKey := fmt.Sprintf("channel_push_task_lock:%s", msg.ID)
			ok, _ := commonService.GetGoRedis().SetNX(ctx, lockKey, 1, 10*time.Second).Result()
			if !ok {
				continue
			}
			// 认领消息(Claim)
			claimedMsgs, err := commonService.GetGoRedis().XClaim(ctx, &redis.XClaimArgs{
				Stream:   commonConsts.ChannelPushStream,
				Group:    commonConsts.ChannelPushGroup1,
				Consumer: commonConsts.ChannelPushConsumer1,
				MinIdle:  10 * time.Second, // 消息至少闲置10秒才认领
				Messages: []string{msg.ID},
			}).Result()
			if err != nil {
				g.Log().Errorf(ctx, "认领消息失败(ID:%s): %v", msg.ID, err)
				commonService.GetGoRedis().Del(ctx, lockKey)
				continue
			}
			// 4. 处理认领到的消息
			for _, claimedMsg := range claimedMsgs {
				var task model.PushTask
				if err := json.Unmarshal([]byte(claimedMsg.Values["push_task_info"].(string)), &task); err != nil {
					g.Log().Errorf(ctx, "反序列化任务失败: %v", err)
					continue
				}
				if optResult, err := processTask(task); err == nil {
					taskEditReq := &model.SChannelPushTaskEditReq{
						TaskId: task.TaskId,
					}
					if optResult == commonConsts.OptResultSuccess {
						taskEditReq.SuccessNum = 1
					} else {
						taskEditReq.FailNum = 1
					}
					_ = service.SChannelPushTask().EditOptStatus(ctx, taskEditReq)
					commonService.GetGoRedis().XAck(ctx,
						commonConsts.ChannelPushStream,
						commonConsts.ChannelPushGroup1,
						claimedMsg.ID)
				}
			}
			commonService.GetGoRedis().Del(ctx, lockKey)
		}
	}
}

// 任务处理逻辑
func processTask(task model.PushTask) (optResult string, err error) {
	var ctx = context.Background()
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取导流链接
		adLink, _ := service.AdDiversionLink().GetByAccount(ctx, task.ChannelCode)
		if adLink == nil {
			liberr.ErrIsNil(ctx, errors.New("导流链接不存在"))
		}
		editReq := &model.AdDiversionLinkEditReq{}
		_ = gconv.Struct(adLink, editReq)
		req := &oceanengineModel.AdAssetChannelImportReq{
			List: []*oceanengineModel.AdAssetChannelImport{
				{
					AppId:        task.AppId,
					AdvertiserId: task.AdvertiserId,
					Link:         adLink.Link,
				},
			},
			CategoryId: task.CategoryId,
		}
		var errMsg string
		if task.AdvertiserId == "" {
			errMsg = "媒体账户为空"
			optResult = commonConsts.OptResultFail
		} else {
			if strings.HasPrefix(task.AppId, commonConsts.DyAppIdPrefix) {
				if task.PushType == commonConsts.PushTypeExistApplet {
					splits := strings.Split(adLink.Link, "\n")
					err = oceanengineService.AdAssetByteAppletLink().Add(ctx,
						&oceanengineModel.AdAssetByteAppletLinkBatchAddReq{
							InstanceId: task.InstanceId,
							List: []*oceanengineModel.AdAssetByteAppletLinkBatchAdd{
								{
									LinkRemark: task.LinkRemark,
									StartPage:  strings.Split(splits[0], ":")[1],
									StartParam: strings.Split(splits[1], ":")[1],
								},
							},
						})
				} else {
					err = oceanengineService.AdAssetByteApplet().ChannelImport(ctx, req)
				}
			} else if strings.HasPrefix(task.AppId, commonConsts.WxAppIdPrefix) {
				// 查询微小原始ID和名称
				for _, v := range req.List {
					thirdMini, _ := adService.AdThirdMiniProgramConfig().GetByAppId(ctx, v.AppId)
					if thirdMini != nil {
						v.AppName = thirdMini.AppName
						v.OriginalId = thirdMini.OriginalId
					}
				}
				err = oceanengineService.AdAssetWechatApplet().ChannelImport(ctx, req)
			} else {
				err = errors.New("小程序ID不正确")
			}
			// 生成任务详情记录
			optResult = commonConsts.OptResultSuccess
			if err != nil {
				optResult = commonConsts.OptResultFail
				errMsg = err.Error()
			}
			// 请求太多，休眠一秒
			if strings.Contains(errMsg, commonConsts.TooManyRequestErrMsg) {
				time.Sleep(1 * time.Second)
				return
			}
		}
		detail := &model.SChannelPushTaskDetailAddReq{
			TaskId:       task.TaskId,
			SerialNumber: task.SerialNumber,
			ChannelCode:  task.ChannelCode,
			AdvertiserId: task.AdvertiserId,
			OptResult:    optResult,
			ErrMsg:       errMsg,
		}
		err = service.SChannelPushTaskDetail().Add(ctx, detail)
	})
	return
}

// InitStreamCleanupTask 初始化Stream清理任务
func InitStreamCleanupTask() {
	// 添加定时任务：每天凌晨1点执行，按数量清理Stream数据 0 0 1 * * ?
	_, _ = gcron.Add(gctx.New(), "0 0 1 * * ?", func(ctx context.Context) {
		// 创建分布式锁，避免多服务器实例同时执行清理任务
		lockKey := "cleanup_stream_task_lock"
		// 尝试获取锁
		ok, _ := commonService.GetGoRedis().SetNX(ctx, lockKey, 1, 10*time.Minute).Result()
		if !ok {
			return
		}
		// 无论任务是否成功执行，确保最终释放锁
		defer func() {
			_, err := commonService.GetGoRedis().Del(ctx, lockKey).Result()
			if err != nil {
				g.Log().Errorf(ctx, "释放分布式锁失败: %v", err)
			}
		}()
		// 执行实际的清理逻辑
		err := CleanupChannelPushStreamData(ctx, 1000)
		if err != nil {
			g.Log().Errorf(ctx, "执行Stream数据清理失败: %v", err)
			return
		}
	}, "CleanupChannelPushStreamData")
}

// CleanupChannelPushStreamData 清理Redis Stream中的数据
func CleanupChannelPushStreamData(ctx context.Context, maxStreamSize int64) error {
	if maxStreamSize <= 0 {
		maxStreamSize = 1000 // 默认保留1000条消息
	}

	// 获取消费者组信息，检查是否所有消息都已处理
	groups, err := commonService.GetGoRedis().XInfoGroups(ctx, commonConsts.ChannelPushStream).Result()
	if err != nil {
		g.Log().Error(ctx, fmt.Sprintf("获取消费者组信息失败: %v", err))
		return err
	}

	allProcessed := true
	for _, group := range groups {
		// 如果有未处理的消息，标记为未全部处理
		if group.Pending > 0 {
			allProcessed = false
			break
		}
	}

	if allProcessed {
		_, err = commonService.GetGoRedis().XTrimMaxLen(ctx, commonConsts.ChannelPushStream, maxStreamSize).Result()
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("清理Stream数据失败 (保留最近%d条): %v", maxStreamSize, err))
			return err
		}
		g.Log().Infof(ctx, "成功清理Stream数据，保留最近%d条消息", maxStreamSize)
	}

	return nil
}
