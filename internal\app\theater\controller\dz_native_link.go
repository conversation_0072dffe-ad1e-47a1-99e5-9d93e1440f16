// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/controller/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/theater"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/internal/app/theater/service"
)

type dzNativeLinkController struct {
	systemController.BaseController
}

var DzNativeLink = new(dzNativeLinkController)

// List 列表
func (c *dzNativeLinkController) List(ctx context.Context, req *theater.DzNativeLinkSearchReq) (res *theater.DzNativeLinkSearchRes, err error) {
	res = new(theater.DzNativeLinkSearchRes)
	res.DzNativeLinkSearchRes, err = service.DzNativeLink().List(ctx, &req.DzNativeLinkSearchReq)
	return
}

// Get 获取点众原生链接
func (c *dzNativeLinkController) Get(ctx context.Context, req *theater.DzNativeLinkGetReq) (res *theater.DzNativeLinkGetRes, err error) {
	res = new(theater.DzNativeLinkGetRes)
	res.DzNativeLinkInfoRes, err = service.DzNativeLink().GetById(ctx, req.Id)
	return
}

// Add 添加点众原生链接
func (c *dzNativeLinkController) Add(ctx context.Context, req *theater.DzNativeLinkAddReq) (res *theater.DzNativeLinkAddRes, err error) {
	err = service.DzNativeLink().Add(ctx, req.DzNativeLinkAddReq)
	return
}

// Edit 修改点众原生链接
func (c *dzNativeLinkController) Edit(ctx context.Context, req *theater.DzNativeLinkEditReq) (res *theater.DzNativeLinkEditRes, err error) {
	err = service.DzNativeLink().Edit(ctx, req.DzNativeLinkEditReq)
	return
}

// Delete 删除点众原生链接
func (c *dzNativeLinkController) Delete(ctx context.Context, req *theater.DzNativeLinkDeleteReq) (res *theater.DzNativeLinkDeleteRes, err error) {
	err = service.DzNativeLink().Delete(ctx, req.Ids)
	return
}

// DzNativeLinkCrawl 点众原生链接爬取
func (c *dzNativeLinkController) DzNativeLinkCrawl(ctx context.Context, req *theater.DzNativeLinkCrawlReq) (res *theater.DzNativeLinkCrawlRes, err error) {
	err = service.DzNativeLink().DzNativeLinkCrawl(ctx)
	return
}
