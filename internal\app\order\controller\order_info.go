// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-01-27 10:06:43
// 生成路径: internal/app/order/controller/order_info.go
// 生成人：gfast
// desc:订单主表信息
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
	"github.com/tiger1103/gfast/v3/api/v1/order"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	memberModel "github.com/tiger1103/gfast/v3/internal/app/member/model"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
	"github.com/tiger1103/gfast/v3/internal/app/order/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"sync"
)

type orderInfoController struct {
	systemController.BaseController
}

var OrderInfo = new(orderInfoController)

// List 列表
func (c *orderInfoController) List(ctx context.Context, req *order.OrderInfoSearchReq) (res *order.OrderInfoSearchRes, err error) {
	res = new(order.OrderInfoSearchRes)
	res.OrderInfoSearchRes, err = service.OrderInfo().List(ctx, &req.OrderInfoSearchReq)
	return
}

// RepurchaseRateList  分销用户统计
func (c *orderInfoController) RepurchaseRateList(ctx context.Context, req *order.RepurchaseRateReq) (res *order.RepurchaseRateRes, err error) {
	res = new(order.RepurchaseRateRes)
	res.RepurchaseRateSearchRes, err = service.OrderInfo().RepurchaseRateList(ctx, req.RepurchaseRateReq)
	return
}

// IncomeStatistics  汇总统计
func (c *orderInfoController) IncomeStatistics(ctx context.Context, req *order.IncomeStatisticsReq) (res *order.IncomeStatisticsRes, err error) {
	res = new(order.IncomeStatisticsRes)
	res.IncomeStatisticsRes, err = service.OrderInfo().IncomeStatistics(ctx)
	return
}

// AdvertisingChannelStatistics 广告渠道统计
func (c *orderInfoController) AdvertisingChannelStatistics(ctx context.Context, req *order.AdvertisingChannelStatisticsReq) (res *order.AdvertisingChannelStatisticsRes, err error) {
	res = new(order.AdvertisingChannelStatisticsRes)
	res.AdvertisingChannelStatisticsRes, err = service.OrderInfo().AdvertisingChannelStatistics(ctx, req)
	return
}

// RechargeStatList 小程序充值统计
func (c *orderInfoController) RechargeStatList(ctx context.Context, req *order.RechargeStatReq) (res *order.RechargeStatRes, err error) {
	res = new(order.RechargeStatRes)
	res.RechargeStatListRes, err = service.OrderInfo().RechargeStatList(ctx, req.RechargeStatReq)
	return
}

// RechargeStatExport 导出小程序充值统计excel
func (c *orderInfoController) RechargeStatExport(ctx context.Context, req *order.RechargeStatExportReq) (res *order.OrderInfoExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listRes  *model.RechargeStatListRes
		listData []*model.RechargeStatRes
		//表头
		tableHead = []interface{}{"日期", "小程序名称", "小程序类型", "当日新增用户充值", "总充值金额", "微小安卓总充值", "微小IOS总充值", "抖小总充值", "支小安卓总充值", "支小IOS总充值"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.OrderInfo().RechargeStatList(ctx, &req.RechargeStatReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.CreateTime,
				v.AppName,
				v.AppType,
				v.NewUserAmount,
				v.TotalAmount,
				v.WechatAndroidRechargeAmount,
				v.WechatIosRechargeAmount,
				v.DyRechargeAmount,
				v.AliAndroidRechargeAmount,
				v.AliIosRechargeAmount,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("小程序充值统计")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

func (c *orderInfoController) Export(ctx context.Context, req *order.OrderInfoExportReq) (res *order.OrderInfoExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.OrderInfoListRes
		//表头
		tableHead = []interface{}{"日期", "订单ID", "第三方单号", "充值时间", "订单号", "广告主ID", "所属分销", "投手", "渠道", "渠道备注", "媒体", "充值应用",
			"小程序名称", "用户昵称", "用户ID", "用户注册时间", "充值类型", "充值金额", "用户金币余额", "充值次数", "支付方式", "充值状态", "充值来源",
			"回传状态", "IP地址", "客户端类型", "广告信息"}
		headData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	// 每次查询2000条数据，防止in查询数量过大导致索引失效
	req.PageSize = 500
	//获取字典数据
	headData = append(headData, tableHead)
	req.Export = true
	total, err := service.OrderInfo().Count(ctx, &req.OrderInfoSearchReq)
	if err != nil {
		return
	}
	if total > libUtils.ExcelHelperMaxRowLimit {
		err = errors.New("已超出导出限制，最多导出20W条数据")
		return
	}

	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.AppendArrToExcel("Sheet1", "A1", headData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	cr, _ := excelize.JoinCellName(col, total+1)
	excel.SetCellBorder("Sheet1", "A1", cr)

	numWorkers := decimal.NewFromInt(gconv.Int64(total)).
		Div(decimal.NewFromInt(gconv.Int64(req.PageSize))).
		RoundCeil(0).IntPart()
	bytes, err := json.Marshal(req.OrderInfoSearchReq)
	if err != nil {
		return
	}

	// 创建goroutine池，避免goroutine数量过多同时加载数据到内存，占用内存过大
	pool := grpool.New(15)
	var wg sync.WaitGroup
	for i := 0; i < gconv.Int(numWorkers); i++ {
		j := i
		wg.Add(1)
		errs := pool.Add(ctx, func(ctx context.Context) {
			defer wg.Done()
			var copyReq *model.OrderInfoSearchReq
			err = json.Unmarshal(bytes, &copyReq)
			if err != nil {
				return
			}
			copyReq.PageNum = j + 1
			innerCtx, cancel := context.WithCancel(ctx)
			defer cancel()
			listData, err = service.OrderInfo().GetExportData(innerCtx, copyReq)
			if err != nil {
				return
			}
			if listData == nil || len(listData) == 0 {
				return
			}
			var nextRow = (copyReq.PageNum-1)*copyReq.PageSize + 2
			excelData := make([][]interface{}, 0)
			for _, v := range listData {
				var ()
				var status string
				if v.Status == 1 {
					status = "未支付"
				} else if v.Status == 2 {
					status = "已完成"
				}
				var deviceType string
				if v.DeviceType == 1 {
					deviceType = "安卓"
				} else if v.DeviceType == 2 {
					deviceType = "苹果"
				}
				dt := []interface{}{
					v.CreateDate,
					v.OrderId,
					v.ThirdTransSn,
					v.PayTime,
					v.OrderNum,
					v.AdvertiserId,
					v.Distributor,
					v.Pitcher,
					v.Channel,
					v.ChannelRemark,
					v.ChannelType,
					v.RechargePlatform,
					v.AppName,
					v.UserName,
					v.SaasId,
					v.UserRegisterTime,
					v.RechargeType,
					v.RechargeAmount,
					v.GoldBalance,
					v.RechargeCount,
					v.PayType,
					status,
					v.RechargeSource,
					v.CallbackStatus,
					v.Ip,
					deviceType,
					v.AdInfo,
				}
				excelData = append(excelData, dt)
			}
			excel.AppendArrToExcel("Sheet1", "A"+gconv.String(nextRow), excelData)
		})
		if errs != nil {
			g.Log().Errorf(context.Background(), "订单导出第%v页异常：%v", j+1, errs)
		}
	}
	wg.Wait()
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("订单主表信息")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

func (c *orderInfoController) ExportCvs(ctx context.Context, req *order.OrderInfoExportCvsReq) (res *order.OrderInfoExportCvsRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.OrderInfoListRes
		//表头
		tableHead = []interface{}{"IDFA原值", "OAID原值", "MD5摘要IMEI", "MD5摘要IDFA", "MD5摘要OAID", "SHA256摘要手机号",
			"MD5摘要手机号", "MD5摘要MAC地址", "触发时间", "渠道", "付费金额", "产品线", "自定义:客户类型"}
		headData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	// 每次查询2000条数据，防止in查询数量过大导致索引失效
	req.PageSize = 500
	//获取字典数据
	headData = append(headData, tableHead)
	req.Export = true
	total, err := service.OrderInfo().Count(ctx, &req.OrderInfoSearchReq)
	if err != nil {
		return
	}
	if total > libUtils.ExcelHelperMaxRowLimit {
		err = errors.New("已超出导出限制，最多导出20W条数据")
		return
	}

	//创建excel处理对象
	dir, err := filepath.Abs(filepath.Dir("."))
	filePath := filepath.Join(dir, "resource/template/excel/投后结案-转化分析数据模板.csv")
	// 打开CSV文件
	file, err := os.Open(filePath)
	if err != nil {
		g.Log().Error(ctx, "打开CSV模板文件失败:", err)
		return
	}
	defer file.Close()
	csvReader := csv.NewReader(file)

	// 读取现有数据（如果需要保留模板内容）
	existingRecords, err := csvReader.ReadAll()
	if err != nil {
		g.Log().Error(ctx, "读取CSV文件失败:", err)
		return
	}

	// 创建新的CSV内容
	var allRecords [][]string

	// 添加表头（如果模板没有的话）
	if len(existingRecords) == 0 {
		headerRecord := make([]string, len(tableHead))
		for i, header := range tableHead {
			headerRecord[i] = fmt.Sprintf("%v", header)
		}
		allRecords = append(allRecords, headerRecord)
	} else {
		// 保留模板中的现有内容
		allRecords = append(allRecords, existingRecords...)
	}

	numWorkers := decimal.NewFromInt(gconv.Int64(total)).
		Div(decimal.NewFromInt(gconv.Int64(req.PageSize))).
		RoundCeil(0).IntPart()
	bytesData, err := json.Marshal(req.OrderInfoSearchReq)
	if err != nil {
		return
	}

	// 创建goroutine池，避免goroutine数量过多同时加载数据到内存，占用内存过大
	pool := grpool.New(15)
	var wg sync.WaitGroup
	for i := 0; i < gconv.Int(numWorkers); i++ {
		j := i
		wg.Add(1)
		errs := pool.Add(ctx, func(ctx context.Context) {
			defer wg.Done()
			var copyReq *model.OrderInfoSearchReq
			err = json.Unmarshal(bytesData, &copyReq)
			if err != nil {
				return
			}
			copyReq.PageNum = j + 1
			innerCtx, cancel := context.WithCancel(ctx)
			defer cancel()
			listData, err = service.OrderInfo().GetExportData(innerCtx, copyReq)
			if err != nil {
				return
			}
			if listData == nil || len(listData) == 0 {
				return
			}
			//var nextRow = (copyReq.PageNum-1)*copyReq.PageSize + 2
			excelData := make([][]interface{}, 0)
			for _, v := range listData {
				var adInfo *memberModel.AdvertisingInfo
				_ = json.Unmarshal([]byte(v.AdInfo), &adInfo)
				if adInfo != nil {
					var customType string
					if v.RechargeCount == 1 {
						customType = "新客"
					} else {
						customType = "老客"
					}
					var idfa = adInfo.Idfa
					if idfa == "__IDFA__" {
						idfa = ""
					}
					var oaid = adInfo.Oaid
					if oaid == "__OAID__" {
						oaid = ""
					}
					var imei = adInfo.Imei
					if imei == "__IMEI__" {
						imei = ""
					}
					var mac = adInfo.Mac
					if mac == "__MAC__" {
						mac = ""
					}
					dt := []interface{}{
						idfa,
						oaid,
						imei,
						"",
						"",
						"",
						"",
						mac,
						v.PayTime,
						v.Channel,
						v.RechargeAmount,
						v.RechargeType,
						customType,
					}
					excelData = append(excelData, dt)
				}
			}
			//excel.AppendArrToExcel("Sheet1", "A"+gconv.String(nextRow), excelData)
			// 添加新数据
			for _, row := range excelData {
				record := make([]string, len(row))
				for i, cell := range row {
					record[i] = fmt.Sprintf("%v", cell)
				}
				allRecords = append(allRecords, record)
			}
		})
		if errs != nil {
			g.Log().Errorf(context.Background(), "订单导出第%v页异常：%v", j+1, errs)
		}
	}
	wg.Wait()
	// 创建输出缓冲区
	var buf bytes.Buffer
	csvWriter := csv.NewWriter(&buf)
	// 写入所有记录
	for _, record := range allRecords {
		if err1 := csvWriter.Write(record); err1 != nil {
			g.Log().Error(ctx, "写入CSV记录失败:", err1)
			return
		}
	}
	csvWriter.Flush()
	r.Response.Header().Set("Content-Type", "text/csv; charset=utf-8")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("投后结案-转化分析数据")+".csv")
	r.Response.Write([]byte(buf.String()))
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取订单主表信息
func (c *orderInfoController) Get(ctx context.Context, req *order.OrderInfoGetReq) (res *order.OrderInfoGetRes, err error) {
	res = new(order.OrderInfoGetRes)
	res.OrderInfoInfoRes, err = service.OrderInfo().GetById(ctx, req.Id)
	return
}

// Add 添加订单主表信息
func (c *orderInfoController) Add(ctx context.Context, req *order.OrderInfoAddReq) (res *order.OrderInfoAddRes, err error) {
	err = service.OrderInfo().Add(ctx, req.OrderInfoAddReq)
	return
}

// Edit 修改订单主表信息
func (c *orderInfoController) Edit(ctx context.Context, req *order.OrderInfoEditReq) (res *order.OrderInfoEditRes, err error) {
	err = service.OrderInfo().Edit(ctx, req.OrderInfoEditReq)
	return
}

// Delete 删除订单主表信息
func (c *orderInfoController) Delete(ctx context.Context, req *order.OrderInfoDeleteReq) (res *order.OrderInfoDeleteRes, err error) {
	err = service.OrderInfo().Delete(ctx, req.Ids)
	return
}

// GetMoneyFlow 获取金币流水
func (c *orderInfoController) GetMoneyFlow(ctx context.Context, req *order.MoneyFlowSearchReq) (res *order.MoneyFlowSearchRes, err error) {
	res = new(order.MoneyFlowSearchRes)
	res.MoneyFlowSearchRes, err = service.OrderInfo().GetMoneyFlowList(ctx, &req.MoneyFlowSearchReq)
	return
}

// ExportMoneyFlow 导出订单金币流水
func (c *orderInfoController) ExportMoneyFlow(ctx context.Context, req *order.MoneyFlowExportReq) (res *order.MoneyFlowExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.MoneyFlowListRes
		//表头
		tableHead = []interface{}{"渠道号", "用户id", "openId", "注册渠道", "充值金额", "充值金币", "赠送金币", "支付类型", "订单号", "订单状态", "创建时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.OrderInfo().GetMoneyFlowExportData(ctx, &req.MoneyFlowSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var statusStr string = "未支付"
			if v.Status == 2 {
				statusStr = "已完成"
			}
			var vipStr = "普通充值"
			if v.VipType == commonConsts.YearVip {
				vipStr = "年卡"
			} else if v.VipType == commonConsts.HalfYearVip {
				vipStr = "半年卡"
			} else if v.VipType == commonConsts.QuarterVip {
				vipStr = "季卡"
			} else if v.VipType == commonConsts.MonthVip {
				vipStr = "月卡"
			} else if v.VipType == commonConsts.WeekVip {
				vipStr = "周vip"
			} else if v.VipType == commonConsts.ThreeVip {
				vipStr = "三天vip"
			} else if v.VipType == commonConsts.OneVip {
				vipStr = "一天vip"
			} else if v.VipType == commonConsts.PlayVip {
				vipStr = "剧vip"
			} else if v.VipType == commonConsts.MonthSubscribe {
				vipStr = "连续包月"
			} else if v.VipType == commonConsts.FifteenVip {
				vipStr = "15天vip"
			}
			var channelTypeStr string = "微信"
			if v.ChannelType == commonConsts.DyChannelType {
				channelTypeStr = "抖音"
			}
			var ()
			dt := []interface{}{
				v.Account,
				v.SaasId,
				v.OpenId,
				channelTypeStr,
				v.PayMoney,
				v.ExchangeGoldCoin,
				v.GiveGold,
				vipStr,
				v.OrderSn,
				statusStr,
				v.CreateTime.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("资金流水统计")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// GetIpInfo 查询IP归属
func (c *orderInfoController) GetIpInfo(ctx context.Context, req *order.IpInfoSearchReq) (res *order.IpInfoSearchRes, err error) {
	res = new(order.IpInfoSearchRes)
	res.IpInfoSearchRes, err = service.OrderInfo().GetIpInfo(ctx, &req.IpInfoSearchReq)
	return
}

// AdCallback 订单补回传
func (c *orderInfoController) AdCallback(ctx context.Context, req *order.AdCallbackReq) (res *order.AdCallbackRes, err error) {
	res = new(order.AdCallbackRes)
	err = service.OrderInfo().AdCallback(ctx, &req.AdCallbackReq)
	return
}

// BatchAdCallback 批量订单补回传
func (c *orderInfoController) BatchAdCallback(ctx context.Context, req *order.AdCallbackBatchReq) (res *order.AdCallbackBatchRes, err error) {
	res = new(order.AdCallbackBatchRes)
	err = service.OrderInfo().BatchAdCallback(ctx, &req.AdCallbackBatchReq)
	return
}

// RefundCreate 发起退款
func (c *orderInfoController) RefundCreate(ctx context.Context, req *order.RefundCreateReq) (res *order.RefundCreateRes, err error) {
	res = new(order.RefundCreateRes)
	err = service.OrderInfo().RefundCreate(ctx, &req.RefundCreateReq)
	return
}

func (c *orderInfoController) GetPayMoneyGroupInfo(ctx context.Context, req *order.PayMoneyGroupSearchReq) (res *order.PayMoneyGroupSearchRes, err error) {
	res = new(order.PayMoneyGroupSearchRes)
	res.PayMoneyGroupSearchRes, err = service.OrderInfo().GetPayMoneyGroupInfo(ctx, &req.PayMoneyGroupSearchReq)
	return
}

// GetPromotionalList 渠道统计 / 推广数据 列表
func (c *orderInfoController) GetPromotionalList(ctx context.Context, req *order.PromotionalListReq) (res *order.PromotionalListRes, err error) {
	res = new(order.PromotionalListRes)
	res.PromotionalListRes, err = service.OrderInfo().GetPromotionalList(ctx, &req.PromotionalListReq)
	return
}

func (c *orderInfoController) PromotionalTheaterStat(ctx context.Context, req *order.PromotionalTheaterStatReq) (res *order.PromotionalTheaterStatRes, err error) {
	res = new(order.PromotionalTheaterStatRes)
	res.PromotionalTheaterStatRes, err = service.OrderInfo().PromotionalTheaterStat(ctx, &req.PromotionalTheaterStatReq)
	return
}

// PromotionalListExport 渠道统计 / 推广数据 列表导出
func (c *orderInfoController) PromotionalListExport(ctx context.Context, req *order.PromotionalListExportReq) (res *order.PromotionalListExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData *model.PromotionalListRes
		//表头
		tableHead = []interface{}{"日期", "渠道", "渠道账号", "调起次数", "激活人数", "当天注册新增的充值人数", "当前渠道注册当天充值总金额", "当天充值总金额", "1天充值总金额", "2天充值总金额", "3天充值总金额", "5天充值总金额", "7天充值总金额"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 1000
	//获取字典数据
	excelData = append(excelData, tableHead)
	hasFirst := true
	for {
		listData, err = service.OrderInfo().GetPromotionalList(ctx, &req.PromotionalListReq)
		if hasFirst {
			hasFirst = false
			excelData = append(excelData, []interface{}{
				"汇总",
				listData.SummaryStat.Channel,
				listData.SummaryStat.Account,
				listData.SummaryStat.UpNumber,
				listData.SummaryStat.ActivationNum,
				listData.SummaryStat.DayNewTopUp,
				listData.SummaryStat.TotalAmount,
				listData.SummaryStat.DayTotalAmount,
				listData.SummaryStat.OneDayTotalAmount,
				listData.SummaryStat.TwoDayTotalAmount,
				listData.SummaryStat.ThreeDayTotalAmount,
				listData.SummaryStat.FiveDayTotalAmount,
				listData.SummaryStat.SevenDayTotalAmount})
		}
		if err != nil {
			return
		}
		if listData == nil || len(listData.List) == 0 {
			break
		}
		for _, v := range listData.List {
			var ()
			dt := []interface{}{
				v.CreateTime,
				v.Channel,
				v.Account,
				v.UpNumber,
				v.ActivationNum,
				v.DayNewTopUp,
				v.TotalAmount,
				v.DayTotalAmount,
				v.OneDayTotalAmount,
				v.TwoDayTotalAmount,
				v.ThreeDayTotalAmount,
				v.FiveDayTotalAmount,
				v.SevenDayTotalAmount,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("推广数据")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}
func (c *orderInfoController) UserOrderList(ctx context.Context, req *order.UserOrderSearchReq) (res *order.UserOrderSearchRes, err error) {
	res = new(order.UserOrderSearchRes)
	res.UserOrderSearchRes, err = service.OrderInfo().UserOrderList(ctx, &req.UserOrderSearchReq)
	return
}
func (c *orderInfoController) GetPriceGroupCount(ctx context.Context, req *order.PriceGroupSearchReq) (res []*model.PriceGroupListRes, err error) {

	res, err = service.OrderInfo().PriceGroupCount(ctx, &req.PriceGroupSearchReq)
	return
}

func (c *orderInfoController) UpdateOrderRechargeType(ctx context.Context, req *order.UpdateOrderRechargeTypeReq) (res []*order.UpdateOrderRechargeTypeRes, err error) {
	err = service.OrderInfo().UpdateOrderRechargeType(ctx, req.StartTime, req.EndTime)
	return
}

func (c *orderInfoController) UpdateOrderAdInfoReq(ctx context.Context, req *order.UpdateOrderAdInfoReq) (res []*order.UpdateOrderAdInfoRes, err error) {
	err = service.OrderInfo().UpdateOrderAdInfoReq(ctx, req.StartTime, req.EndTime, req.OrderId, req.AppId)
	return
}

// RunCalcFqPitcherVideoRechargeStat 番茄短剧充值统计
func (c *orderInfoController) RunCalcFqPitcherVideoRechargeStat(ctx context.Context, req *order.FqVideoRechargeStatTaskReq) (res *order.FqVideoRechargeStatTaskRes, err error) {
	err = service.OrderInfo().RunCalcFqPitcherVideoRechargeStat(ctx, req.StartTime, req.EndTime)
	return
}

// RunCalcDzPitcherVideoRechargeStat 点众短剧充值统计
func (c *orderInfoController) RunCalcDzPitcherVideoRechargeStat(ctx context.Context, req *order.DzVideoRechargeStatTaskReq) (res *order.FqVideoRechargeStatTaskRes, err error) {
	err = service.OrderInfo().RunCalcDzPitcherVideoRechargeStat(ctx, req.StartTime, req.EndTime)
	return
}
