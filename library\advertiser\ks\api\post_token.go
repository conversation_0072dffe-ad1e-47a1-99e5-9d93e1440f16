package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// GetTokenService  Ks 获取token接口
type GetTokenService struct {
	ctx context.Context
	cfg *Configuration
	//token   string
	Request *GetTokenReq
}

type GetTokenReq struct {
	AppId    int64  `json:"app_id"`
	Secret   string `json:"secret"`
	AuthCode string `json:"auth_code"` // 授权时返回的 auth_code
}

type GetTokenResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    *struct {
		AccessToken           string `json:"access_token"`             // 用于验证权限的 token.
		RefreshTokenExpiresIn int64  `json:"refresh_token_expires_in"` // access_token 剩余有效时间，单位：秒。
		RefreshToken          string `json:"refresh_token"`            // 用于获取新的 access_token 和 refresh_token，并且刷新过期时间。
		AccessTokenExpiresIn  int64  `json:"access_token_expires_in"`  //refresh_token 剩余有效时间，单位：秒.
		AdvertiserId          int64  `json:"advertiser_id"`            // 短剧经营者的 账户id
	} `json:"data"`
}

func (r *GetTokenService) SetCfg(cfg *Configuration) *GetTokenService {
	r.cfg = cfg
	return r
}

func (r *GetTokenService) SetReq(req GetTokenReq) *GetTokenService {
	r.Request = &req
	return r
}

func (r *GetTokenService) Do() (data *GetTokenResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/oauth2/authorize/access_token"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(r.Request).
		SetResult(&GetTokenResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetTokenResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/oauth2/authorize/access_token解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
