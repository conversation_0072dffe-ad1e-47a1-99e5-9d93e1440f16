// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/logic/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"slices"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdOptimizerDataStat(New())
}

func New() service.IAdOptimizerDataStat {
	return &sAdOptimizerDataStat{}
}

type sAdOptimizerDataStat struct{}

// List 优化师数据统计列表
func (s *sAdOptimizerDataStat) List(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (listRes *model.AdOptimizerDataStatSearchRes, err error) {
	listRes = new(model.AdOptimizerDataStatSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m, summaryM := s.BuildSqlModel(ctx, req)
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var groupBy string
		var orderBy string
		fields := make([]string, 0)
		groupBy = "a.user_id, a.create_date"
		orderBy = "statPayAmount desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields = append(fields, "ANY_VALUE(a.create_date) as createDate")
		fields = append(fields, "ANY_VALUE(a.user_id) as userId")
		fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		fields = append(fields, "ANY_VALUE(d.leader) as supervisorName")
		fields = append(fields, "SUM(a.total_ad_nums) as totalAdNums")
		fields = append(fields, "SUM(a.has_cost_ad_nums) as hasCostAdNums")
		fields = append(fields, "SUM(a.learned_ad_nums) as learnedAdNums")
		fields = append(fields, "ROUND(SUM(a.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost),2) as payAmountRoi")
		fields = append(fields, "SUM(a.show_cnt) as showCnt")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.show_cnt)*1000,2) as cpmPlatform")
		fields = append(fields, "SUM(a.click_cnt) as clickCnt")
		fields = append(fields, "ROUND(SUM(a.click_cnt)/SUM(a.show_cnt)*100,2) as ctr")
		fields = append(fields, "SUM(a.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.convert_cnt),2) as conversionCost")
		fields = append(fields, "ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "SUM(a.active) as active")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.active),2) as activeCost")
		fields = append(fields, "ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost),2) as attributionGameInAppRoi1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost),2) as attributionMicroGame0DRoi")
		fields = append(fields, "SUM(a.active_pay) as activePay")
		fields = append(fields, "ROUND(SUM(a.active_pay)/SUM(a.active)*100,2) as activePayRate")
		fields = append(fields, "SUM(a.game_pay_count) as gamePayCount")
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		if listRes.List == nil || len(listRes.List) == 0 {
			return
		}
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
		// 计算汇总过学习期广告数
		err2 := summaryM.
			FieldCount("DISTINCT CASE WHEN learning_phase = 'LEARNED' THEN promotion_id END", "learnedAdNums").
			Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err2, "计算汇总过学习期广告数失败")
	})
	return
}

// OptimizerSupervisorList 优化师主管数据统计列表
func (s *sAdOptimizerDataStat) OptimizerSupervisorList(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (listRes *model.AdOptimizerDataStatSearchRes, err error) {
	listRes = new(model.AdOptimizerDataStatSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m, summaryM := s.BuildSqlModel(ctx, req)
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var groupBy string
		var orderBy string
		fields := make([]string, 0)
		groupBy = "d.leader, a.create_date"
		orderBy = "statPayAmount desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields = append(fields, "ANY_VALUE(a.create_date) as createDate")
		fields = append(fields, "ANY_VALUE(d.leader) as supervisorName")
		fields = append(fields, "SUM(a.total_ad_nums) as totalAdNums")
		fields = append(fields, "SUM(a.has_cost_ad_nums) as hasCostAdNums")
		fields = append(fields, "SUM(a.learned_ad_nums) as learnedAdNums")
		fields = append(fields, "ROUND(SUM(a.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost),2) as payAmountRoi")
		fields = append(fields, "SUM(a.show_cnt) as showCnt")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.show_cnt)*1000,2) as cpmPlatform")
		fields = append(fields, "SUM(a.click_cnt) as clickCnt")
		fields = append(fields, "ROUND(SUM(a.click_cnt)/SUM(a.show_cnt)*100,2) as ctr")
		fields = append(fields, "SUM(a.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.convert_cnt),2) as conversionCost")
		fields = append(fields, "ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "SUM(a.active) as active")
		fields = append(fields, "ROUND(SUM(a.stat_cost)/SUM(a.active),2) as activeCost")
		fields = append(fields, "ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost),2) as attributionGameInAppRoi1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost),2) as attributionMicroGame0DRoi")
		fields = append(fields, "SUM(a.active_pay) as activePay")
		fields = append(fields, "ROUND(SUM(a.active_pay)/SUM(a.active)*100,2) as activePayRate")
		fields = append(fields, "SUM(a.game_pay_count) as gamePayCount")
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		if listRes.List == nil || len(listRes.List) == 0 {
			return
		}
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
		// 计算汇总过学习期广告数
		err2 := summaryM.
			FieldCount("DISTINCT CASE WHEN learning_phase = 'LEARNED' THEN promotion_id END", "learnedAdNums").
			Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err2, "计算汇总过学习期广告数失败")
	})
	return
}

func (s *sAdOptimizerDataStat) BuildSqlModel(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (m *gdb.Model, summaryM *gdb.Model) {
	m = dao.AdOptimizerDataStat.Ctx(ctx).WithAll().As("a").
		LeftJoin("sys_user", "u", "a.user_id = u.id").
		LeftJoin("sys_dept", "d", "d.dept_id = u.dept_id")
	summaryM = dao.AdPromotionAnalytic.Ctx(ctx).As("a").
		LeftJoin("sys_user", "u", "a.user_id = u.id").
		LeftJoin("sys_dept", "d", "d.dept_id = u.dept_id")
	userInfo := sysService.Context().GetLoginUser(ctx)
	userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
		LoginUserRes: &systemModel.LoginUserRes{
			Id:     userInfo.Id,
			DeptId: userInfo.DeptId,
		},
	})
	if !admin && len(userIds) > 0 {
		m = m.WhereIn("a.user_id", userIds)
		summaryM = summaryM.WhereIn("a.user_id", userIds)
	}
	if len(req.UserIds) > 0 {
		m = m.WhereIn("a.user_id", req.UserIds)
		summaryM = summaryM.WhereIn("a.user_id", req.UserIds)
	}
	if len(req.SupervisorIds) > 0 {
		users, _ := sysService.SysUser().GetUserByIds(ctx, req.SupervisorIds)
		var deptUserIds = make([]int, 0)
		deptUserIds = append(deptUserIds, -1)
		for _, v := range users {
			ids, _, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     v.Id,
					DeptId: v.DeptId,
				},
			})
			for _, k := range ids {
				deptUserIds = append(deptUserIds, k)
			}
		}
		m = m.WhereIn("a.user_id", deptUserIds)
		summaryM = summaryM.WhereIn("a.user_id", deptUserIds)
	}
	if req.StartTime != "" {
		m = m.WhereGTE("a.create_date", req.StartTime)
	}
	if req.EndTime != "" {
		m = m.WhereLTE("a.create_date", req.EndTime)
		_, endTime := libUtils.GetDayStartAndEnd(req.EndTime, req.EndTime)
		summaryM = summaryM.WhereLTE("a.promotion_create_time", endTime)
	}
	if len(req.DeptIds) > 0 {
		m = m.WhereIn("u.dept_id", req.DeptIds)
		summaryM = summaryM.WhereIn("u.dept_id", req.DeptIds)
	}
	if len(req.AdvertiserIds) > 0 {
		m = m.WhereIn("a.advertiser_id", req.AdvertiserIds)
		summaryM = summaryM.WhereIn("a.advertiser_id", req.AdvertiserIds)
	}
	return
}

func (s *sAdOptimizerDataStat) Add(ctx context.Context, req *model.AdOptimizerDataStatAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdOptimizerDataStat.Ctx(ctx).Insert(do.AdOptimizerDataStat{
			CreateDate:                  req.CreateDate,
			AdvertiserId:                req.AdvertiserId,
			UserId:                      req.UserId,
			TotalAdNums:                 req.TotalAdNums,
			HasCostAdNums:               req.HasCostAdNums,
			LearnedAdNums:               req.LearnedAdNums,
			StatCost:                    req.StatCost,
			StatPayAmount:               req.StatPayAmount,
			ShowCnt:                     req.ShowCnt,
			ClickCnt:                    req.ClickCnt,
			ConvertCnt:                  req.ConvertCnt,
			Active:                      req.Active,
			AttributionGameInAppLtv1Day: req.AttributionGameInAppLtv1Day,
			AttributionMicroGame0DLtv:   req.AttributionMicroGame0DLtv,
			ActivePay:                   req.ActivePay,
			GamePayCount:                req.GamePayCount,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdOptimizerDataStat) RunAdOptimizerDataStat(ctx context.Context, req *model.AdOptimizerDataStatSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		for {
			if startTime > endTime {
				break
			}
			errors := s.CalcAdOptimizerDataStat(innerContext, startTime)
			if errors != nil {
				g.Log().Error(ctx, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdOptimizerDataStat) CalcAdOptimizerDataStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdOptimizerDataStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdOptimizerDataStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
		err = s.CalcAdOptimizerDataStat(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "优化师数据统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcAdOptimizerDataStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "优化师数据统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdOptimizerDataStat) CalcTodayAdOptimizerDataStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdOptimizerDataStatTodayLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdOptimizerDataStatTodayLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		today := gtime.Now().Format("Y-m-d")
		err = s.CalcAdOptimizerDataStat(ctx, today)
		liberr.ErrIsNil(ctx, err, "当天优化师数据统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcTodayAdOptimizerDataStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "当天优化师数据统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// CalcAdOptimizerDataStat 优化师数据统计
func (s *sAdOptimizerDataStat) CalcAdOptimizerDataStat(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		optimizerAccountList, _ := s.CalcOptimizerAccountList(ctx, statDate)
		hasCostAdNumsDataList, _ := s.CalcHasCostAdNumsDataList(ctx, statDate)
		learnedAdNumsDataList, _ := s.CalcLearnedAdNumsDataList(ctx, statDate)
		promotionMetricsDataList, _ := s.CalcPromotionMetricsDataList(ctx, statDate)
		var statList = make([]*model.AdOptimizerDataStatInfoRes, 0)
		for _, v := range optimizerAccountList {
			v.CreateDate = statDate
			for _, item := range learnedAdNumsDataList {
				if v.AdvertiserId == item.AdvertiserId {
					v.LearnedAdNums = item.LearnedAdNums
					break
				}
			}
			for _, item := range hasCostAdNumsDataList {
				if v.AdvertiserId == item.AdvertiserId {
					v.HasCostAdNums = item.HasCostAdNums
					break
				}
			}
			for _, item := range promotionMetricsDataList {
				if v.AdvertiserId == item.AdvertiserId {
					v.StatCost = item.StatCost
					v.StatPayAmount = item.StatPayAmount
					v.ShowCnt = item.ShowCnt
					v.ClickCnt = item.ClickCnt
					v.ConvertCnt = item.ConvertCnt
					v.Active = item.Active
					v.AttributionGameInAppLtv1Day = item.AttributionGameInAppLtv1Day
					v.AttributionMicroGame0DLtv = item.AttributionMicroGame0DLtv
					v.ActivePay = item.ActivePay
					v.GamePayCount = item.GamePayCount
					break
				}
			}
			if v.TotalAdNums == 0 && v.HasCostAdNums == 0 && v.LearnedAdNums == 0 &&
				v.StatCost == 0 && v.StatPayAmount == 0 && v.ShowCnt == 0 && v.ClickCnt == 0 &&
				v.ConvertCnt == 0 && v.Active == 0 && v.AttributionGameInAppLtv1Day == 0 &&
				v.AttributionMicroGame0DLtv == 0 && v.ActivePay == 0 && v.GamePayCount == 0 {
				continue
			}
			statList = append(statList, v)
			if len(statList) >= 100 {
				_, err = dao.AdOptimizerDataStat.Ctx(ctx).Save(statList)
				statList = slices.Delete(statList, 0, len(statList))
			}
		}
		if len(statList) > 0 {
			_, err = dao.AdOptimizerDataStat.Ctx(ctx).Save(statList)
		}
	})
	return
}

// CalcOptimizerAccountList 查询所有优化师账号列表
func (s *sAdOptimizerDataStat) CalcOptimizerAccountList(ctx context.Context, statDate string) (res []*model.AdOptimizerDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.AdAdvertiserAccount.Ctx(ctx).As("a").
			LeftJoin("ad_promotion", "b",
				fmt.Sprintf("a.advertiser_id = b.advertiser_id AND b.promotion_create_time >= '%s' AND b.promotion_create_time <= '%s'", startTime, endTime)).
			Fields("a.user_id as userId").
			Fields("a.advertiser_id as advertiserId").
			FieldCount("DISTINCT b.promotion_id", "totalAdNums").
			Group("a.user_id, a.advertiser_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询所有优化师广告数据列表失败")
	})
	return
}

// CalcHasCostAdNumsDataList 查询有消耗广告数据列表
func (s *sAdOptimizerDataStat) CalcHasCostAdNumsDataList(ctx context.Context, statDate string) (res []*model.AdOptimizerDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.AdPromotionAnalytic.Ctx(ctx).As("a").
			LeftJoin("ad_promotion_metrics_data", "b", "a.promotion_id = b.promotion_id").
			WhereGTE("a.promotion_create_time", startTime).
			WhereLTE("a.promotion_create_time", endTime).
			Fields("a.advertiser_id as advertiserId").
			FieldCount("DISTINCT CASE WHEN b.stat_cost > 0 THEN b.promotion_id END", "hasCostAdNums").
			Group("a.advertiser_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询有消耗广告数据列表失败")
	})
	return
}

// CalcLearnedAdNumsDataList 查询过学习期广告数据列表
func (s *sAdOptimizerDataStat) CalcLearnedAdNumsDataList(ctx context.Context, statDate string) (res []*model.AdOptimizerDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.AdPromotionAnalytic.Ctx(ctx).
			WhereLTE("promotion_create_time", endTime).
			Fields("advertiser_id as advertiserId").
			FieldCount("DISTINCT CASE WHEN learning_phase = 'LEARNED' THEN promotion_id END", "learnedAdNums").
			Group("advertiser_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询过学习期广告数据列表失败")
	})
	return
}

// CalcPromotionMetricsDataList 查询广告指标统计数据
func (s *sAdOptimizerDataStat) CalcPromotionMetricsDataList(ctx context.Context, statDate string) (res []*model.AdOptimizerDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPromotionMetricsDataAnalytic.Ctx(ctx).As("a").
			LeftJoin("ad_promotion", "b", "a.promotion_id = b.promotion_id").
			Where("a.create_date", statDate).
			Fields("b.advertiser_id as advertiserId").
			Fields("ROUND(SUM(a.stat_cost),2) as statCost").
			Fields("ROUND(SUM(a.stat_pay_amount),2) as statPayAmount").
			Fields("SUM(a.show_cnt) as showCnt").
			Fields("SUM(a.click_cnt) as clickCnt").
			Fields("SUM(a.convert_cnt) as convertCnt").
			Fields("SUM(a.active) as active").
			Fields("ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day").
			Fields("ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv").
			Fields("SUM(a.active_pay) as activePay").
			Fields("SUM(a.game_pay_count) as gamePayCount").
			Group("b.advertiser_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询广告指标统计数据失败")
	})
	return
}
