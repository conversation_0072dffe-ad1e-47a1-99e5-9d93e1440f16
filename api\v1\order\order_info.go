// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-27 10:06:43
// 生成路径: api/v1/order/order_info.go
// 生成人：gfast
// desc:订单主表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
)

// OrderInfoSearchReq 分页请求参数
type OrderInfoSearchReq struct {
	g.Meta `path:"/list" tags:"订单主表信息" method:"post" summary:"订单主表信息列表"`
	commonApi.Author
	model.OrderInfoSearchReq
}

// OrderInfoSearchRes 列表返回结果
type OrderInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.OrderInfoSearchRes
}

// OrderInfoExportReq 导出请求
type OrderInfoExportReq struct {
	g.Meta `path:"/export" tags:"订单主表信息" method:"post" summary:"订单主表信息导出"`
	commonApi.Author
	model.OrderInfoSearchReq
}

// OrderInfoExportRes 导出响应
type OrderInfoExportRes struct {
	commonApi.EmptyRes
}

// OrderInfoExportCvsReq 导出请求
type OrderInfoExportCvsReq struct {
	g.Meta `path:"/exportCvs" tags:"订单主表信息" method:"post" summary:"订单转化分析数据导出"`
	commonApi.Author
	model.OrderInfoSearchReq
}

// OrderInfoExportCvsRes 导出响应
type OrderInfoExportCvsRes struct {
	commonApi.EmptyRes
}

// OrderInfoAddReq 添加操作请求参数
type OrderInfoAddReq struct {
	g.Meta `path:"/add" tags:"订单主表信息" method:"post" summary:"订单主表信息添加"`
	commonApi.Author
	*model.OrderInfoAddReq
}

// OrderInfoAddRes 添加操作返回结果
type OrderInfoAddRes struct {
	commonApi.EmptyRes
}

// OrderInfoEditReq 修改操作请求参数
type OrderInfoEditReq struct {
	g.Meta `path:"/edit" tags:"订单主表信息" method:"put" summary:"订单主表信息修改"`
	commonApi.Author
	*model.OrderInfoEditReq
}

// OrderInfoEditRes 修改操作返回结果
type OrderInfoEditRes struct {
	commonApi.EmptyRes
}

// OrderInfoGetReq 获取一条数据请求
type OrderInfoGetReq struct {
	g.Meta `path:"/get" tags:"订单主表信息" method:"get" summary:"获取订单主表信息信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// OrderInfoGetRes 获取一条数据结果
type OrderInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.OrderInfoInfoRes
}

// OrderInfoDeleteReq 删除数据请求
type OrderInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"订单主表信息" method:"post" summary:"删除订单主表信息"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// OrderInfoDeleteRes 删除数据返回
type OrderInfoDeleteRes struct {
	commonApi.EmptyRes
}

// RepurchaseRateReq 分销用户统计 Req
type RepurchaseRateReq struct {
	g.Meta `path:"/get/repurchaseRate" tags:"分销统计" method:"post" summary:"分销用户统计"`
	commonApi.Author
	*model.RepurchaseRateReq
}

// RepurchaseRateRes 分销用户统计 Res
type RepurchaseRateRes struct {
	g.Meta `mime:"application/json"`
	*model.RepurchaseRateSearchRes
}

// RechargeStatReq 小程序充值统计
type RechargeStatReq struct {
	g.Meta `path:"/applet/recharge/stat" tags:"分销统计" method:"post" summary:"小程序充值统计"`
	commonApi.Author
	*model.RechargeStatReq
}

// RechargeStatRes 小程序充值统计
type RechargeStatRes struct {
	g.Meta `mime:"application/json"`
	*model.RechargeStatListRes
}

// RechargeStatExportReq 导出请求
type RechargeStatExportReq struct {
	g.Meta `path:"/applet/recharge/stat/export" tags:"分销统计" method:"post" summary:"订单主表信息导出"`
	commonApi.Author
	model.RechargeStatReq
}

// RechargeStatExportRes 导出响应
type RechargeStatExportRes struct {
	commonApi.EmptyRes
}

// IncomeStatisticsReq 收益统计 分销统计 / 收益汇总
type IncomeStatisticsReq struct {
	g.Meta `path:"/income/statistics" tags:"分销统计" method:"get" summary:"收益统计"`
	commonApi.Author
	//model.IncomeStatisticsRes
}

// IncomeStatisticsRes 收益统计 返回
type IncomeStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.IncomeStatisticsRes
}

// AdvertisingChannelStatisticsReq 分销统计 / 收益汇总 广告渠道统计
type AdvertisingChannelStatisticsReq struct {
	g.Meta `path:"/advertising/channel/statistics" tags:"分销统计" method:"get" summary:"广告渠道统计"`
	commonApi.Author
	model.AdvertisingChannelStatisticsReq
}

// AdvertisingChannelStatisticsRes 广告渠道统计返回
type AdvertisingChannelStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.AdvertisingChannelStatisticsRes
}

// MoneyFlowSearchReq 分页请求参数
type MoneyFlowSearchReq struct {
	g.Meta `path:"/getMoneyFlow" tags:"数据统计" method:"post" summary:"订单资金流水信息"`
	commonApi.Author
	model.MoneyFlowSearchReq
}
type MoneyFlowSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MoneyFlowSearchRes
}

// MoneyFlowExportReq 金币流水导出
type MoneyFlowExportReq struct {
	g.Meta `path:"/exportMoneyFlow" tags:"数据统计" method:"post" summary:"订单资金流水导出"`
	commonApi.Author
	model.MoneyFlowSearchReq
}
type MoneyFlowExportRes struct {
	g.Meta `mime:"application/json"`
	*model.MoneyFlowListRes
}

// IpInfoSearchReq 查询IP归属
type IpInfoSearchReq struct {
	g.Meta `path:"/ipaddr" tags:"订单主表信息" method:"get" summary:"查询IP归属"`
	commonApi.Author
	model.IpInfoSearchReq
}

// IpInfoSearchRes 查询IP归属
type IpInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.IpInfoSearchRes
}

// AdCallbackReq 订单补回传
type AdCallbackReq struct {
	g.Meta `path:"/adCallback" tags:"订单主表信息" method:"get" summary:"订单补回传"`
	commonApi.Author
	model.AdCallbackReq
}

// AdCallbackRes 订单补回传
type AdCallbackRes struct {
	g.Meta `mime:"application/json"`
}

// AdCallbackBatchReq 批量订单补回传
type AdCallbackBatchReq struct {
	g.Meta `path:"/adCallback/batch" tags:"订单主表信息" method:"get" summary:"批量订单补回传"`
	commonApi.Author
	model.AdCallbackBatchReq
}

// AdCallbackBatchRes 批量订单补回传
type AdCallbackBatchRes struct {
	g.Meta `mime:"application/json"`
}

// RefundCreateReq 发起退款
type RefundCreateReq struct {
	g.Meta `path:"/refund" tags:"订单主表信息" method:"get" summary:"发起退款"`
	commonApi.Author
	model.RefundCreateReq
}

// RefundCreateRes 发起退款
type RefundCreateRes struct {
	g.Meta `mime:"application/json"`
}

// PayMoneyGroupSearchReq  订单支付金额分组数据查询
type PayMoneyGroupSearchReq struct {
	g.Meta `path:"/payMoneyGroupInfo" tags:"订单主表信息" method:"post" summary:"订单充值分布统计"`
	commonApi.Author
	model.PayMoneyGroupSearchReq
}
type PayMoneyGroupSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.PayMoneyGroupSearchRes
}

// PromotionalListReq 推广数据
type PromotionalListReq struct {
	g.Meta `path:"/promotional/list" tags:"渠道统计" method:"post" summary:"推广数据列表"`
	commonApi.Author
	model.PromotionalListReq
}

type PromotionalListRes struct {
	g.Meta `mime:"application/json"`
	*model.PromotionalListRes
}

// PromotionalListExportReq 导出请求
type PromotionalListExportReq struct {
	g.Meta `path:"/promotional/list/export" tags:"渠道统计" method:"post" summary:"推广数据列表导出"`
	commonApi.Author
	model.PromotionalListReq
}

// PromotionalListExportRes 导出响应
type PromotionalListExportRes struct {
	commonApi.EmptyRes
}

type PromotionalTheaterStatReq struct {
	g.Meta `path:"/promotional/theater/stat" tags:"渠道统计" method:"post" summary:"剧集推广统计"`
	commonApi.Author
	model.PromotionalTheaterStatReq
}

type PromotionalTheaterStatRes struct {
	g.Meta `mime:"application/json"`
	*model.PromotionalTheaterStatRes
}
type UserOrderSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.UserOrderSearchRes
}

// OrderInfoSearchReq 分页请求参数
type UserOrderSearchReq struct {
	g.Meta `path:"/userOrderList" tags:"用户管理" method:"post" summary:"用户订单列表"`
	commonApi.Author
	model.UserOrderSearchReq
}
type PriceGroupSearchReq struct {
	g.Meta `path:"/getPriceGroupCount" tags:"订单主表信息" method:"post" summary:"订单价格分组信息列表"`
	commonApi.Author
	model.PriceGroupSearchReq
}
type PriceGroupSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.PriceGroupListRes
}

type UpdateOrderRechargeTypeReq struct {
	g.Meta `path:"/update/recharge/type" tags:"订单主表信息" method:"post" summary:"更新订单充值类型"`
	commonApi.Author
	model.PriceGroupSearchReq
}

type UpdateOrderRechargeTypeRes struct {
	g.Meta `mime:"application/json"`
}

type UpdateOrderAdInfoReq struct {
	g.Meta `path:"/update/adInfo" tags:"订单主表信息" method:"post" summary:"更新订单广告信息"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间格式 yyyy-MM-dd hh:mm:ss"`
	EndTime   string `p:"endTime" dc:"结束时间格式 yyyy-MM-dd hh:mm:ss"`
	OrderId   int    `p:"orderId" dc:"订单ID"`
	AppId     string `p:"appId" dc:"小程序ID"`
}

type UpdateOrderAdInfoRes struct {
	g.Meta `mime:"application/json"`
}

type FqVideoRechargeStatTaskReq struct {
	g.Meta `path:"/fqVideoRechargeStatTask" tags:"定时任务" method:"post" summary:"番茄短剧充值统计"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

type FqVideoRechargeStatTaskRes struct {
	commonApi.EmptyRes
}

type DzVideoRechargeStatTaskReq struct {
	g.Meta `path:"/dzVideoRechargeStatTask" tags:"定时任务" method:"post" summary:"点众短剧充值统计"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}
