// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/dao/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/theater/dao/internal"
)

// dzNativeLinkDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzNativeLinkDao struct {
	*internal.DzNativeLinkDao
}

var (
	// DzNativeLink is globally public accessible object for table tools_gen_table operations.
	DzNativeLink = dzNativeLinkDao{
		internal.NewDzNativeLinkDao(),
	}
)

// Fill with you ideas below.
