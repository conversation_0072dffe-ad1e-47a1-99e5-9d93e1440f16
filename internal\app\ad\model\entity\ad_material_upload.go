// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-23 15:51:08
// 生成路径: internal/app/ad/model/entity/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterialUpload is the golang structure for table ad_material_upload.
type AdMaterialUpload struct {
	gmeta.Meta   `orm:"table:ad_material_upload"`
	Id           int         `orm:"id,primary" json:"id"`                       // 自增ID
	MediaType    int         `orm:"media_type" json:"mediaType"`                // 媒体类型：图片或视频 1 图片 2视频
	AdMaterialId int         `orm:"ad_material_id" json:"adMaterialId"`         // 素材id 关联ad_material 表格 material_id
	AdvertiserId string      `orm:"advertiser_id" json:"advertiserId"`          // 广告账户id
	MediaId      string      `orm:"media_id" json:"mediaId"`                    // 图片/视频ID
	MaterialName string      `orm:"material_name" json:"materialName" dc:"素材名"` // 素材名
	Size         int64       `orm:"size" json:"size"`                           // 媒体文件大小
	Width        int         `orm:"width" json:"width"`                         // 宽度
	Height       int         `orm:"height" json:"height"`                       // 高度
	Url          string      `orm:"url" json:"url"`                             // 图片预览地址或视频地址
	Format       string      `orm:"format" json:"format"`                       // 图片格式
	Signature    string      `orm:"signature" json:"signature"`                 // 图片md5 或视频md5
	MaterialId   string      `orm:"material_id" json:"materialId"`              // 素材id，即多合一报表中的素材id，唯一对应一个素材id
	Duration     float64     `orm:"duration" json:"duration"`                   // 视频时长，如果是视频，则填充此字段
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt"`                // 创建时间
	UpdatedAt    *gtime.Time `orm:"updated_at" json:"updatedAt"`                // 更新时间
}
