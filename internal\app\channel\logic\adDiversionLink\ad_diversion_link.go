// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-06-18 16:18:52
// 生成路径: internal/app/channel/logic/ad_diversion_link.go
// 生成人：cq
// desc:广告导流链接配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	sysModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterModel "github.com/tiger1103/gfast/v3/internal/app/theater/model"
	theaterService "github.com/tiger1103/gfast/v3/internal/app/theater/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/channel/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdDiversionLink(New())
}

func New() service.IAdDiversionLink {
	return &sAdDiversionLink{}
}

type sAdDiversionLink struct{}

func (s *sAdDiversionLink) GetLatestByAccount(ctx context.Context, account string) (res *model.AdDiversionLinkListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdDiversionLink.Ctx(ctx).WithAll().Where(dao.AdDiversionLink.Columns().Account, account).Order("create_time desc").Limit(1).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdDiversionLink) GetLatestListByAccount(ctx context.Context, accounts []string) (res []*model.AdDiversionLinkListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(accounts) > 0 {
			err = dao.AdDiversionLink.Ctx(ctx).Raw(`SELECT ad.*
		FROM ad_diversion_link ad
		INNER JOIN (
		    SELECT account, MAX(create_time) AS latest_create_time
		    FROM ad_diversion_link
		    WHERE account IN (?) 
		    GROUP BY account
		) latest_data
		ON ad.account = latest_data.account AND ad.create_time = latest_data.latest_create_time;`, accounts).Scan(&res)

			liberr.ErrIsNil(ctx, err, "获取信息失败")
		}
	})
	return
}

func (s *sAdDiversionLink) GetLatestDyByAccount(ctx context.Context, account string) (res *model.AdDiversionLinkListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdDiversionLink.Ctx(ctx).WithAll().
			Where(dao.AdDiversionLink.Columns().Account, account).
			WhereLike(dao.AdDiversionLink.Columns().AppId, "%tt%").
			Order("create_time desc").
			Limit(1).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdDiversionLink) List(ctx context.Context, req *model.AdDiversionLinkSearchReq) (listRes *model.AdDiversionLinkSearchRes, err error) {
	listRes = new(model.AdDiversionLinkSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		user, _ := sysService.SysUser().GetUserById(ctx, userInfo.Id)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &sysModel.ContextUser{
			LoginUserRes: &sysModel.LoginUserRes{
				Id:     user.Id,
				DeptId: user.DeptId,
			},
		})
		channelInfo, err1 := service.SChannel().GetByCode(ctx, req.Account)
		liberr.ErrIsNil(ctx, err1, "渠道不存在！")
		if !admin && !libUtils.FindTargetInt(userIds, channelInfo.UserId) {
			err = errors.New("没有权限")
			liberr.ErrIsNil(ctx, err, err.Error())
		}
		m := dao.AdDiversionLink.Ctx(ctx).WithAll().Where(dao.AdDiversionLink.Columns().Account+" = ?", req.Account)
		if req.Id != "" {
			m = m.Where(dao.AdDiversionLink.Columns().Id+" = ?", req.Id)
		}
		if req.Platform != "" {
			m = m.Where(dao.AdDiversionLink.Columns().Platform+" = ?", gconv.Int(req.Platform))
		}
		if req.AppId != "" {
			m = m.Where(dao.AdDiversionLink.Columns().AppId+" = ?", req.AppId)
		}
		if req.VideoId != "" {
			m = m.Where(dao.AdDiversionLink.Columns().VideoId+" = ?", gconv.Int(req.VideoId))
		}
		if req.Num != "" {
			m = m.Where(dao.AdDiversionLink.Columns().Num+" = ?", gconv.Int(req.Num))
		}
		if req.Remarks != nil && len(req.Remarks) > 0 {
			m = m.Where(dao.AdDiversionLink.Columns().Remark+" in(?)", req.Remarks)
		}
		if req.StartTime != "" && req.EndTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.Where(dao.AdDiversionLink.Columns().CreateTime+" >= ?", dayStartTime)
			m = m.Where(dao.AdDiversionLink.Columns().CreateTime+" <= ?", dayEndTime)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "create_time desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdDiversionLinkListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")

		// 获取小程序名称
		appIds := make([]string, 0)
		linq.From(res).SelectT(func(item *model.AdDiversionLinkListRes) string {
			return item.AppId
		}).ToSlice(&appIds)
		miniInfos, err1 := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIds)
		liberr.ErrIsNil(ctx, err1, "获取小程序名称失败")

		// 获取短剧名称
		var videoIds []string
		linq.From(res).SelectT(func(s *model.AdDiversionLinkListRes) string {
			return gconv.String(s.VideoId)
		}).ToSlice(&videoIds)
		videoInfos, err2 := theaterService.TheaterInfo().GetVideoByIds(ctx, videoIds)
		liberr.ErrIsNil(ctx, err2, "获取短剧名称失败")

		adSetting, err3 := service.AdSetting().GetByAccount(ctx, req.Account)
		liberr.ErrIsNil(ctx, err3, "获取广告配置失败")

		listRes.List = make([]*model.AdDiversionLinkListRes, len(res))
		for k, v := range res {
			adDiversionLink := &model.AdDiversionLinkListRes{
				Id:             v.Id,
				Account:        v.Account,
				Remark:         v.Remark,
				Platform:       v.Platform,
				AppId:          v.AppId,
				VideoId:        v.VideoId,
				Num:            v.Num,
				Link:           v.Link,
				CreateTime:     v.CreateTime,
				UpdateTime:     v.UpdateTime,
				UnlockSwitch:   v.UnlockSwitch,
				UnlockEpisodes: v.UnlockEpisodes,
				VideoAccountId: v.VideoAccountId,
				WxOriginalId:   v.WxOriginalId,
			}
			appInterface := linq.From(miniInfos).WhereT(func(item *sysModel.GetMiniInfoListRes) bool {
				return item.AppId == v.AppId
			}).First()
			if miniInfo, ok := appInterface.(*sysModel.GetMiniInfoListRes); ok {
				adDiversionLink.AppName = miniInfo.AppName
				if v.Platform == commonConsts.XHSAD {
					adDiversionLink.VideoAccountId = miniInfo.OriginalId
				}
			}
			statInterface := linq.From(videoInfos).WhereT(func(item *theaterModel.TheaterInfoInfoRes) bool {
				return item.Id == gconv.Uint(v.VideoId)
			}).First()
			if stat, ok := statInterface.(*theaterModel.TheaterInfoInfoRes); ok {
				adDiversionLink.VideoName = stat.Title
			}
			if v.Platform == commonConsts.BaiDuAd {
				adDiversionLink.BaiduToken = adSetting.BaiduToken
			} else if v.Platform == commonConsts.VivoAd {
				adDiversionLink.AdvertiserId = adSetting.AdvertiserId
				adDiversionLink.SourceId = adSetting.SourceId
			}
			listRes.List[k] = adDiversionLink
		}
	})
	return
}

func (s *sAdDiversionLink) GetById(ctx context.Context, id int64) (res *model.AdDiversionLinkInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdDiversionLink.Ctx(ctx).WithAll().Where(dao.AdDiversionLink.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if res != nil {
			theaterInfo, _ := theaterService.TheaterInfo().GetById(ctx, uint(res.VideoId))
			if theaterInfo != nil {
				res.VideoName = theaterInfo.Title
			}
			adSetting, _ := service.AdSetting().GetByAccount(ctx, res.Account)
			if adSetting != nil {
				res.BaiduToken = adSetting.BaiduToken
				res.AdvertiserId = adSetting.AdvertiserId
				res.SourceId = adSetting.SourceId
			}
		}
	})
	return
}

func (s *sAdDiversionLink) GetByVideoAccountId(ctx context.Context, id string) (res *model.AdDiversionLinkInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdDiversionLink.Ctx(ctx).WithAll().Where(dao.AdDiversionLink.Columns().VideoAccountId, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdDiversionLink) Add(ctx context.Context, req *model.AdDiversionLinkAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.VideoAccountId) > 0 {
			req.VideoAccountId = strings.TrimSpace(req.VideoAccountId)
			link, _ := s.GetByVideoAccountId(ctx, req.VideoAccountId)
			if link != nil && link.Id > 0 {
				liberr.ErrIsNil(ctx, errors.New("该视频号ID已绑定，不可重复绑定，重复渠道:"+link.Account))

				return
			}
		}
		channelInfo, err1 := service.SChannel().GetByCode(ctx, req.Account)
		liberr.ErrIsNil(ctx, err1, "获取渠道信息失败")
		if channelInfo == nil {
			err1 = errors.New("渠道不存在")
			liberr.ErrIsNil(ctx, err1)
		}
		if req.Platform == commonConsts.XHSAD {
			// 根据appid 获取 数据
			info, _ := sysService.SPlatRules().GetByAppId(ctx, req.AppId)
			if info != nil {
				req.WxOriginalId = info.OriginalId
			} else {
				liberr.ErrIsNil(ctx, errors.New("Appid没找到数据:"+req.AppId))
			}
		}
		s.VerifyAdInfo(ctx, req.Platform, req.BaiduToken, req.AdvertiserId, req.SourceId)
		editReq := &model.AdDiversionLinkEditReq{}
		err = gconv.Struct(req, editReq)
		liberr.ErrIsNil(ctx, err)
		link, err2 := s.GetAdLink(ctx, editReq)
		liberr.ErrIsNil(ctx, err2, "获取导流链接失败")

		_, err = dao.AdDiversionLink.Ctx(ctx).Insert(do.AdDiversionLink{
			Account:        req.Account,
			Remark:         req.Remark,
			Platform:       req.Platform,
			AppId:          req.AppId,
			VideoId:        req.VideoId,
			Num:            req.Num,
			Link:           link,
			CreateTime:     gtime.Now(),
			UpdateTime:     gtime.Now(),
			UnlockSwitch:   req.UnlockSwitch,
			UnlockEpisodes: req.UnlockEpisodes,
			VideoAccountId: req.VideoAccountId,
			WxOriginalId:   req.WxOriginalId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
		err = EditBaiduOrVivoAdOrChannelFrom(ctx, req.Platform, req.Account, req.BaiduToken, req.AdvertiserId, req.SourceId)
		liberr.ErrIsNil(ctx, err)
		go func() {
			if !strings.Contains(req.AppId, commonConsts.DyAppIdPrefix) {
				return
			}
			innerCtx, cancel := context.WithCancel(context.Background())
			defer cancel()
			err3 := service.PProduct().SyncPanelInfoToDy(innerCtx, req.Account)
			if err3 != nil {
				g.Log().Errorf(innerCtx, "渠道: %s, 同步面板信息到抖音失败: %v", req.Account, err3)
			}
		}()
	})
	return
}

func (s *sAdDiversionLink) BatchAdd(ctx context.Context, req []*model.AdDiversionLinkAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		linkList := make([]*do.AdDiversionLink, 0)
		for _, v := range req {
			editReq := &model.AdDiversionLinkEditReq{}
			_ = gconv.Struct(v, editReq)
			link, _ := s.GetAdLink(ctx, editReq)
			if v.Platform == commonConsts.XHSAD {
				// 根据appid 获取 数据
				info, _ := sysService.SPlatRules().GetByAppId(ctx, v.AppId)
				if info != nil {
					v.WxOriginalId = info.OriginalId
				} else {
					liberr.ErrIsNil(ctx, errors.New("Appid没找到数据:"+v.AppId))
				}
			}
			linkList = append(linkList, &do.AdDiversionLink{
				Account:        v.Account,
				Remark:         v.Remark,
				Platform:       v.Platform,
				AppId:          v.AppId,
				VideoId:        v.VideoId,
				Num:            v.Num,
				Link:           link,
				CreateTime:     gtime.Now(),
				UpdateTime:     gtime.Now(),
				UnlockSwitch:   v.UnlockSwitch,
				UnlockEpisodes: v.UnlockEpisodes,
				VideoAccountId: v.VideoAccountId,
				WxOriginalId:   v.WxOriginalId,
			})
			err = EditBaiduOrVivoAdOrChannelFrom(ctx, v.Platform, v.Account, v.BaiduToken, v.AdvertiserId, v.SourceId)
			liberr.ErrIsNil(ctx, err)
		}
		if len(linkList) > 0 {
			_, err = dao.AdDiversionLink.Ctx(ctx).Batch(len(linkList)).Insert(linkList)
			liberr.ErrIsNil(ctx, err, "批量创建导流链接失败")
		}
	})
	return
}

func (s *sAdDiversionLink) BatchAddFq(ctx context.Context, req []*model.AdDiversionLinkAddFqReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		linkList := make([]*do.AdDiversionLink, 0)
		for _, v := range req {
			var link string
			if v.FqPromotionUrl != "" {
				// 番茄渠道的推广链接，根据我们后台的样式拆分，微小的维持不变，抖小的将页面和参数拆分显示
				if strings.Contains(v.FqPromotionUrl, "tt_album_id") {
					array := strings.Split(v.FqPromotionUrl, "?")
					if len(array) == 2 {
						link += fmt.Sprintf("巨量引擎抖小跳转路径:%s\n", array[0])
						link += fmt.Sprintf("巨量引擎抖小启动参数:%s\n", array[1])
					}
				} else {
					link += fmt.Sprintf("推广链url:%s\n", v.FqPromotionUrl)
				}

			}
			if v.FqPromotionHttpUrl != "" {
				link += fmt.Sprintf("推广链http短链:%s", v.FqPromotionHttpUrl)
			}
			linkList = append(linkList, &do.AdDiversionLink{
				Account:     v.Account,
				Remark:      v.Remark,
				Platform:    v.Platform,
				Num:         v.Num,
				Link:        link,
				FqAppId:     v.FqAppId,
				FqAppName:   v.FqAppName,
				FqBookId:    v.FqBookId,
				FqBookName:  v.FqBookName,
				FqWxVideoId: v.FqWxVideoId,
				CreateTime:  gtime.Now(),
				UpdateTime:  gtime.Now(),
			})
		}
		if len(linkList) > 0 {
			_, err = dao.AdDiversionLink.Ctx(ctx).Batch(len(linkList)).Insert(linkList)
			liberr.ErrIsNil(ctx, err, "批量创建导流链接失败")
		}
	})
	return
}

func (s *sAdDiversionLink) BatchAddDz(ctx context.Context, req []*model.AdDiversionLinkAddDzReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		linkList := make([]*do.AdDiversionLink, 0)
		for _, v := range req {
			var link string
			if v.Platform == commonConsts.TouTiaoAd && strings.Contains(v.DzPath, "tt_album_id") {
				array := strings.Split(v.DzPath, "?")
				if len(array) == 2 {
					link += fmt.Sprintf("巨量引擎抖小跳转路径:%s\n", array[0])
					link += fmt.Sprintf("巨量引擎抖小启动参数:%s\n", array[1])
				}
			} else {
				link += fmt.Sprintf("推广链接path:%s\n", v.DzPath)
			}
			if v.DzMonitor1 != "" {
				link += fmt.Sprintf("抖音监测链接1.0:%s\n", v.DzMonitor1)
			}
			if v.DzMonitor2 != "" {
				link += fmt.Sprintf("抖音监测链接2.0:%s", v.DzMonitor2)
			}
			if v.DzMonitor != "" {
				link += fmt.Sprintf("抖音监测链接:%s", v.DzMonitor)
			}
			linkList = append(linkList, &do.AdDiversionLink{
				Account:    v.Account,
				Remark:     v.Remark,
				Platform:   v.Platform,
				Num:        v.Num,
				Link:       link,
				DzBookId:   v.DzBookId,
				DzBookName: v.DzBookName,
				CreateTime: gtime.Now(),
				UpdateTime: gtime.Now(),
			})
		}
		if len(linkList) > 0 {
			_, err = dao.AdDiversionLink.Ctx(ctx).Batch(len(linkList)).Insert(linkList)
			liberr.ErrIsNil(ctx, err, "批量创建导流链接失败")
		}
	})
	return
}

func (s *sAdDiversionLink) Edit(ctx context.Context, req *model.AdDiversionLinkEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.VideoAccountId) > 0 {
			req.VideoAccountId = strings.TrimSpace(req.VideoAccountId)
			link, _ := s.GetByVideoAccountId(ctx, req.VideoAccountId)
			if link != nil && link.Id > 0 && link.Id != req.Id {
				liberr.ErrIsNil(ctx, errors.New("该视频号ID已绑定，不可重复绑定，重复渠道:"+link.Account))
				return
			}
		}
		if req.Platform == commonConsts.XHSAD {
			// 根据appid 获取 数据
			info, _ := sysService.SPlatRules().GetByAppId(ctx, req.AppId)
			if info != nil {
				req.WxOriginalId = info.OriginalId
			} else {
				liberr.ErrIsNil(ctx, errors.New("Appid没找到数据:"+req.AppId))
			}
		}
		s.VerifyAdInfo(ctx, req.Platform, req.BaiduToken, req.AdvertiserId, req.SourceId)
		link, err1 := s.GetAdLink(ctx, req)
		liberr.ErrIsNil(ctx, err1, "获取导流链接失败")
		_, err = dao.AdDiversionLink.Ctx(ctx).WherePri(req.Id).Update(do.AdDiversionLink{
			Account:        req.Account,
			Remark:         req.Remark,
			Platform:       req.Platform,
			AppId:          req.AppId,
			VideoId:        req.VideoId,
			Num:            req.Num,
			Link:           link,
			UpdateTime:     gtime.Now(),
			UnlockSwitch:   req.UnlockSwitch,
			UnlockEpisodes: req.UnlockEpisodes,
			VideoAccountId: req.VideoAccountId,
			WxOriginalId:   req.WxOriginalId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
		err = EditBaiduOrVivoAdOrChannelFrom(ctx, req.Platform, req.Account, req.BaiduToken, req.AdvertiserId, req.SourceId)
		liberr.ErrIsNil(ctx, err)
		go func() {
			if !strings.Contains(req.AppId, commonConsts.DyAppIdPrefix) {
				return
			}
			innerCtx, cancel := context.WithCancel(context.Background())
			defer cancel()
			err3 := service.PProduct().SyncPanelInfoToDy(innerCtx, req.Account)
			if err3 != nil {
				g.Log().Errorf(innerCtx, "渠道: %s, 同步面板信息到抖音失败: %v", req.Account, err3)
			}
		}()
	})
	return
}

func EditBaiduOrVivoAdOrChannelFrom(ctx context.Context, platform int, account string, baiduToken string, advertiserId string, sourceId string) (err error) {
	// 修改adSetting表的channel_from字段，渠道消耗是根据这个字段去查询的
	channelFrom := commonConsts.GetAdTypeByAdType(platform)
	editBaiduOrVivoReq := &model.AdSettingEditBaiduOrVivoReq{
		Account:     account,
		ChannelFrom: gconv.String(channelFrom),
	}
	if platform == commonConsts.BaiDuAd || platform == commonConsts.VivoAd {
		editBaiduOrVivoReq.BaiduToken = baiduToken
		editBaiduOrVivoReq.AdvertiserId = advertiserId
		editBaiduOrVivoReq.SourceId = sourceId
	}
	err = service.AdSetting().EditBaiduOrVivoAd(ctx, editBaiduOrVivoReq)
	return
}

func (s *sAdDiversionLink) GetByAccount(ctx context.Context, account string) (res *model.AdDiversionLinkListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdDiversionLink.Ctx(ctx).
			Where(dao.AdDiversionLink.Columns().Account, account).
			OrderDesc(dao.AdDiversionLink.Columns().CreateTime).
			Limit(1).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取导流链接配置失败")
		if res != nil {
			// 获取小程序名称
			miniInfo, err1 := sysService.SPlatRules().GetByAppId(ctx, res.AppId)
			liberr.ErrIsNil(ctx, err1, "获取小程序信息失败")
			if miniInfo != nil {
				res.AppName = miniInfo.AppName
			}
			// 获取短剧名称
			videoInfo, err2 := theaterService.TheaterInfo().GetById(ctx, gconv.Uint(res.VideoId))
			liberr.ErrIsNil(ctx, err2, "获取短剧信息失败")
			if videoInfo != nil {
				res.VideoName = videoInfo.Title
			}
		}
	})
	return
}

func (s *sAdDiversionLink) GetByAccounts(ctx context.Context, accounts []string, appType int) (res []*model.AdDiversionLinkListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdDiversionLink.Ctx(ctx).
			WhereIn(dao.AdDiversionLink.Columns().Account, accounts).
			WhereNot(dao.AdDiversionLink.Columns().Platform, commonConsts.WechatAd)
		if appType == 1 {
			m = m.WhereLike(dao.AdDiversionLink.Columns().AppId, commonConsts.DyAppIdPrefix+"%")
		} else if appType == 2 {
			m = m.WhereLike(dao.AdDiversionLink.Columns().AppId, commonConsts.WxAppIdPrefix+"%")
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取导流链接配置失败")
	})
	return
}

func (s *sAdDiversionLink) GetByAccountList(ctx context.Context, accounts []string) (res []*model.AdDiversionLinkListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(accounts) == 0 {
			return
		}
		err = dao.AdDiversionLinkMaster.Ctx(ctx).WhereIn(dao.AdDiversionLink.Columns().Account, accounts).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取导流链接配置失败")
	})
	return
}

func (s *sAdDiversionLink) RemarkList(ctx context.Context, req *model.AdDiversionLinkRemarkReq) (listRes *model.AdDiversionLinkRemarkRes, err error) {
	listRes = new(model.AdDiversionLinkRemarkRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdDiversionLink.Ctx(ctx).WithAll().
			Where(dao.AdDiversionLink.Columns().Account+" = ?", req.Account).
			WhereNotNull(dao.AdDiversionLink.Columns().Remark)
		if req.Remark != "" {
			m = m.Where("remark like ?", "%"+req.Remark+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var res []*model.AdDiversionLinkInfoRes
		err = m.Fields("DISTINCT remark").
			Page(req.PageNum, req.PageSize).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		for _, v := range res {
			listRes.List = append(listRes.List, v.Remark)
		}
	})
	return
}

// GetAddAdLinkParams 构建批量添加导流链接参数
func (s *sAdDiversionLink) GetAddAdLinkParams(ctx context.Context, edit *model.SChannelBatchEdit, adDiversionLinkList []*model.AdDiversionLinkAddReq) []*model.AdDiversionLinkAddReq {
	if edit.AdLinkConfig.Platform == 0 && edit.AdLinkConfig.AppId == "" && edit.AdLinkConfig.VideoId == 0 && edit.AdLinkConfig.Num == 0 && edit.AdLinkConfig.Remark == "" {
		return adDiversionLinkList
	}
	adSetting, _ := service.AdSetting().GetByAccount(ctx, edit.ChannelCode)
	if edit.AdLinkConfig.BaiduToken == "" && edit.AdLinkConfig.Platform == commonConsts.BaiDuAd {
		if adSetting.BaiduToken == "" {
			return adDiversionLinkList
		}
	}
	if edit.AdLinkConfig.AdvertiserId == "" && edit.AdLinkConfig.SourceId == "" && edit.AdLinkConfig.Platform == commonConsts.VivoAd {
		if adSetting.AdvertiserId == "" && adSetting.SourceId == "" {
			return adDiversionLinkList
		}
	}
	// 获取新的导流链接
	adDiversionLink, _ := s.GetByAccount(ctx, edit.ChannelCode)
	if adDiversionLink != nil {
		adLinkAddReq := &model.AdDiversionLinkAddReq{
			Account:        edit.ChannelCode,
			Remark:         adDiversionLink.Remark,
			Platform:       adDiversionLink.Platform,
			AppId:          adDiversionLink.AppId,
			VideoId:        int64(adDiversionLink.VideoId),
			Num:            int64(adDiversionLink.Num),
			BaiduToken:     adSetting.BaiduToken,
			AdvertiserId:   adSetting.AdvertiserId,
			SourceId:       adSetting.SourceId,
			UnlockSwitch:   adDiversionLink.UnlockSwitch,
			UnlockEpisodes: adDiversionLink.UnlockEpisodes,
			VideoAccountId: adDiversionLink.VideoAccountId,
		}
		adDiversionLinkList = s.BuildAdLinkListParams(edit, adLinkAddReq, adDiversionLinkList)
		return adDiversionLinkList
	}
	// 新的导流链接不存在，获取旧的导流链接
	linkConfig, _ := service.AdSetting().GetLinkConfig(ctx, &model.AdSettingLinkConfigGetReq{ChannelCode: edit.ChannelCode})
	if linkConfig == nil {
		return adDiversionLinkList
	}
	// 根据ad_setting表的channel_from获取广告平台类型
	var platform = commonConsts.TouTiaoAd
	if adSetting != nil {
		switch gconv.Int(adSetting.ChannelFrom) {
		case commonConsts.TOUTIAO, commonConsts.DOUYIN:
			platform = commonConsts.TouTiaoAd
			break
		case commonConsts.BAIDU:
			platform = commonConsts.BaiDuAd
			break
		case commonConsts.VIVO:
			platform = commonConsts.VivoAd
			break
		case commonConsts.TENCENT:
			platform = commonConsts.TencentAd
			break
		case commonConsts.KS:
			platform = commonConsts.KsAd
			break
		case commonConsts.XINGTU, commonConsts.XINGTUDOUYIN:
			platform = commonConsts.XingTuAd
			break
		case commonConsts.WEIBO:
			platform = commonConsts.WeiBoAd
			break
		case commonConsts.XHS:
			platform = commonConsts.XHSAD
			break
		default:
			break
		}
	}
	adLinkAddReq := &model.AdDiversionLinkAddReq{
		Account:      edit.ChannelCode,
		Platform:     platform,
		AppId:        linkConfig.AppId,
		VideoId:      linkConfig.TargetId,
		Num:          linkConfig.Num,
		BaiduToken:   adSetting.BaiduToken,
		AdvertiserId: adSetting.AdvertiserId,
		SourceId:     adSetting.SourceId,
	}
	adDiversionLinkList = s.BuildAdLinkListParams(edit, adLinkAddReq, adDiversionLinkList)
	return adDiversionLinkList
}

func (s *sAdDiversionLink) BuildAdLinkListParams(edit *model.SChannelBatchEdit, adLinkAddReq *model.AdDiversionLinkAddReq, adDiversionLinkList []*model.AdDiversionLinkAddReq) []*model.AdDiversionLinkAddReq {
	if edit.AdLinkConfig.Platform != 0 {
		adLinkAddReq.Platform = edit.AdLinkConfig.Platform
	}
	if edit.AdLinkConfig.Remark != "" {
		adLinkAddReq.Remark = edit.AdLinkConfig.Remark
	}
	if edit.AdLinkConfig.AppId != "" {
		adLinkAddReq.AppId = edit.AdLinkConfig.AppId
	}
	if edit.AdLinkConfig.VideoId != 0 {
		adLinkAddReq.VideoId = edit.AdLinkConfig.VideoId
	}
	if edit.AdLinkConfig.Num != 0 {
		adLinkAddReq.Num = edit.AdLinkConfig.Num
	}
	if edit.AdLinkConfig.BaiduToken != "" {
		adLinkAddReq.BaiduToken = edit.AdLinkConfig.BaiduToken
	}
	if edit.AdLinkConfig.AdvertiserId != "" {
		adLinkAddReq.AdvertiserId = edit.AdLinkConfig.AdvertiserId
	}
	if edit.AdLinkConfig.SourceId != "" {
		adLinkAddReq.SourceId = edit.AdLinkConfig.SourceId
	}
	if edit.AdLinkConfig.UnlockSwitch != nil {
		adLinkAddReq.UnlockSwitch = *edit.AdLinkConfig.UnlockSwitch
	}
	if edit.AdLinkConfig.UnlockEpisodes != nil {
		adLinkAddReq.UnlockEpisodes = *edit.AdLinkConfig.UnlockEpisodes
	}
	adDiversionLinkList = append(adDiversionLinkList, adLinkAddReq)
	return adDiversionLinkList
}

func (s *sAdDiversionLink) VerifyAdInfo(ctx context.Context, platform int, baiduToken string, advertiserId string, sourceId string) {
	if platform == commonConsts.BaiDuAd && baiduToken == "" {
		liberr.ErrIsNil(ctx, errors.New("百度token不能为空"))
	}
	if platform == commonConsts.VivoAd {
		if advertiserId == "" {
			liberr.ErrIsNil(ctx, errors.New("广告主ID不能为空"))
		}
		if sourceId == "" {
			liberr.ErrIsNil(ctx, errors.New("来源ID不能为空"))
		}
	}
	return
}

// GetAdLink 根据广告平台获取导流链接
func (s *sAdDiversionLink) GetAdLink(ctx context.Context, req *model.AdDiversionLinkEditReq) (link string, err error) {
	if req.Platform == commonConsts.WXShiPingHao {
		return "", nil
	}
	videoId := req.VideoId
	num := req.Num
	account := req.Account
	appId := req.AppId
	if videoId == 0 {
		return
	}
	if appId == "" {
		return
	}
	theaterDetail, err := theaterService.TheaterDetail().GetByParentIdAndNum(ctx, videoId, num)
	if theaterDetail == nil || err != nil {
		err = errors.New("获取剧集详情信息失败")
		liberr.ErrIsNil(ctx, err, err.Error())
	}
	theaterInfo, err := theaterService.TheaterInfo().GetById(ctx, gconv.Uint(videoId))
	if theaterInfo == nil || err != nil {
		err = errors.New("获取短剧详情信息失败")
		liberr.ErrIsNil(ctx, err, err.Error())
	}

	dyParam := fmt.Sprintf("&tt_album_id=%s&tt_episode_id=%s", theaterInfo.AlbumId, theaterDetail.EpisodeId)
	wxParam := fmt.Sprintf("&dramaId=%s&serialNo=%v", theaterInfo.DramaId, num)

	// 查询小程序类型
	platRules, err := sysService.SPlatRules().GetByAppId(ctx, appId)
	liberr.ErrIsNil(ctx, err, "获取小程序信息失败")

	// 获取监测链接请求域名
	monitorLinkHost := g.Cfg().MustGet(ctx, "ad.monitorLinkHost").String()

	switch req.Platform {
	case commonConsts.TouTiaoAd:
		if platRules.ChannelType == 1 {
			ttToWxPath := fmt.Sprintf("巨量引擎微小跳转路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.TOUTIAO, wxParam)
			suffix := fmt.Sprintf("&account=%s&channel=%v", account, commonConsts.TOUTIAO)
			monitoringLink := fmt.Sprintf("巨量引擎监测链接:%s%s%s", monitorLinkHost, commonConsts.TouTiaoLink, suffix)
			link = fmt.Sprintf("%s\n%s", ttToWxPath, monitoringLink)
		} else if platRules.ChannelType == 2 {
			dyStartPage := fmt.Sprintf("巨量引擎抖小跳转路径:%s", commonConsts.MiniProgramPathNew)
			dyStartParam := fmt.Sprintf("巨量引擎抖小启动参数:parentId=%v&subId=%v&adid=__AID__&creativeid=__CID__&creativetype=__CTYPE__&clickid=__CLICKID__&imei=__IMEI__&idfa=__IDFA__&mac=__MAC__&trackId=__TRACK_ID__&oaid=__OAID__&caid=__CAID__&account=%s&channel=%v%s",
				videoId, theaterDetail.SubId, account, commonConsts.DOUYIN, dyParam)
			suffix := fmt.Sprintf("&account=%s&channel=%v", account, commonConsts.DOUYIN)
			monitoringLink := fmt.Sprintf("巨量引擎监测链接:%s%s%s", monitorLinkHost, commonConsts.TouTiaoLink, suffix)
			link = fmt.Sprintf("%s\n%s\n%s", dyStartPage, dyStartParam, monitoringLink)
		}
		break
	case commonConsts.BaiDuAd:
		suffix := fmt.Sprintf("&account=%s&channel=%v", account, commonConsts.BAIDU)
		monitoringLink := fmt.Sprintf("百度监测链接:%s%s%s", monitorLinkHost, commonConsts.BaiDuLink, suffix)
		urlScheme := fmt.Sprintf("urlScheme地址:%s%s", monitorLinkHost, commonConsts.UrlScheme)
		if platRules.ChannelType == 1 {
			bdToWxPath := fmt.Sprintf("百度微小跳转路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.BAIDU, wxParam)
			link = fmt.Sprintf("%s\n%s\n%s", bdToWxPath, monitoringLink, urlScheme)
		} else if platRules.ChannelType == 2 {
			bdToDyPath := fmt.Sprintf("百度抖小跳转路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.BAIDU, dyParam)
			link = fmt.Sprintf("%s\n%s\n%s", bdToDyPath, monitoringLink, urlScheme)
		}
		break
	case commonConsts.VivoAd:
		suffix := fmt.Sprintf("&account=%s&channel=%v", account, commonConsts.VIVO)
		monitoringLink := fmt.Sprintf("VIVO监测链接:%s%s%s", monitorLinkHost, commonConsts.VivoLink, suffix)
		if platRules.ChannelType == 1 {
			vivoToWxPath := fmt.Sprintf("VIVO微小跳转路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.VIVO, wxParam)
			link = fmt.Sprintf("%s\n%s", vivoToWxPath, monitoringLink)
		} else if platRules.ChannelType == 2 {
			vivoToDyPath := fmt.Sprintf("VIVO抖小跳转路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.VIVO, dyParam)
			link = fmt.Sprintf("%s\n%s", vivoToDyPath, monitoringLink)
		}
		break
	case commonConsts.TencentAd:
		suffix := fmt.Sprintf("&account=%s&channel=%v", account, commonConsts.TENCENT)
		monitoringLink := fmt.Sprintf("广点通监测链接:%s%s%s", monitorLinkHost, commonConsts.TencentLink, suffix)
		if platRules.ChannelType == 1 {
			txToWxPath := fmt.Sprintf("广点通微小跳转路径:%s?parentId=%v&subId=%v&aId=__ACCOUNT_ID__&clickId=__CLICK_ID__&clickTime=__CLICK_TIME__&callback=__CALLBACK__&qzGdt=__QZ_GDT__&gdtVid=__GDT_VID__&pageUrl=__PAGE_URL__&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.TENCENT, wxParam)
			link = fmt.Sprintf("%s\n%s", txToWxPath, monitoringLink)
		} else if platRules.ChannelType == 2 {
			txToDyPath := fmt.Sprintf("广点通抖小跳转路径:%s?parentId=%v&subId=%v&aId=__ACCOUNT_ID__&clickId=__CLICK_ID__&clickTime=__CLICK_TIME__&callback=__CALLBACK__&qzGdt=__QZ_GDT__&gdtVid=__GDT_VID__&pageUrl=__PAGE_URL__&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.TENCENT, dyParam)
			link = fmt.Sprintf("%s\n%s", txToDyPath, monitoringLink)
		}
		break
	case commonConsts.KsAd:
		if platRules.ChannelType == 1 {
			ksToWxPath := fmt.Sprintf("快手微小跳转路径:%s?parentId=%v&subId=%v&aId=__ACCOUNTID__&adId=__CID__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.KS, wxParam)
			miniJumpURL := fmt.Sprintf("%s%s?num=%v&targetId=%v&channelCode=%s&appId=%s", monitorLinkHost, commonConsts.GenerateShortLinkRequestURL, num, videoId, account, appId)
			ksMiniJumpURL := fmt.Sprintf("快手微信内部广告跳转链接:%s&aId=__ACCOUNTID__&adId=__CID__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&channel%v", miniJumpURL, commonConsts.KS)
			link = fmt.Sprintf("%s\n%s", ksToWxPath, ksMiniJumpURL)
		} else if platRules.ChannelType == 2 {
			ksToDyPath := fmt.Sprintf("快手抖小跳转路径:%s?parentId=%v&subId=%v&aId=__ACCOUNTID__&adId=__CID__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.KS, dyParam)
			link = ksToDyPath
		}
		break
	case commonConsts.XingTuAd:
		if platRules.ChannelType == 1 {
			xtToWxPath := fmt.Sprintf("星图微小启动页面和参数:%s?parentId=%v&subId=%v&click_id=__CLICK_ID__&item_id=__ITEM_ID__&channel=%v&account=%s%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, commonConsts.XINGTU, account, wxParam)
			xtWxStartPage := fmt.Sprintf("星图微小启动页面:%s", commonConsts.MiniProgramPathNew)
			xtWxStartParam := fmt.Sprintf("星图微小启动参数:parentId=%v&subId=%v&click_id=__CLICK_ID__&item_id=__ITEM_ID__&channel=%v&account=%s%s",
				videoId, theaterDetail.SubId, commonConsts.XINGTU, account, wxParam)
			link = fmt.Sprintf("%s\n%s\n%s", xtToWxPath, xtWxStartPage, xtWxStartParam)
		} else if platRules.ChannelType == 2 {
			xtDyStartPage := fmt.Sprintf("星图抖小启动页面:%s?parentId=%v&subId=%v&click_id=__CLICK_ID__&item_id=__ITEM_ID__&imei=__IMEI__&idfa=__IDFA__&mac=__MAC__&trackId=__TRACK_ID__&oaid=__OAID__&caid=__CAID__&channel=%v&account=%s%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, commonConsts.XINGTUDOUYIN, account, dyParam)
			link = xtDyStartPage
		}
		break
	case commonConsts.WeiBoAd:
		suffix := fmt.Sprintf("&account=%s&channel=%v", account, commonConsts.WEIBO)
		monitoringLink := fmt.Sprintf("微博监测链接:%s%s%s", monitorLinkHost, commonConsts.WeiBoLink, suffix)
		if platRules.ChannelType == 1 {
			wbToWxPath := fmt.Sprintf("微博跳转微小路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.WEIBO, wxParam)
			link = fmt.Sprintf("%s\n%s", wbToWxPath, monitoringLink)
		} else if platRules.ChannelType == 2 {
			wbToDyPath := fmt.Sprintf("微博跳转抖小路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.WEIBO, dyParam)
			link = fmt.Sprintf("%s\n%s", wbToDyPath, monitoringLink)
		}
		break
	case commonConsts.DengHuoAd:
		// todo 等feature_ali_ad分支代码发布了再加上
	case commonConsts.WechatAd:
		if platRules.ChannelType == 1 {
			miniJumpURL := fmt.Sprintf("微信内部广告跳转链接:%s%s?num=%v&targetId=%v&channelCode=%s&appId=%s&channel=9", monitorLinkHost, commonConsts.GenerateShortLinkRequestURL, num, videoId, account, appId)
			link = miniJumpURL
		}
		break
	case commonConsts.BiliAd:
		if platRules.ChannelType == 1 {
			link = fmt.Sprintf("B站跳转微小路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s&trackid=__TRACKID__",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.BILIBILI, wxParam)
		} else if platRules.ChannelType == 2 {
			link = fmt.Sprintf("B站跳转抖小路径:%s?parentId=%v&subId=%v&account=%s&channel=%v%s&trackid=__TRACKID__",
				commonConsts.MiniProgramPathNew, videoId, theaterDetail.SubId, account, commonConsts.BILIBILI, dyParam)
		}
	case commonConsts.XHSAD:
		if platRules.ChannelType == 1 {
			dyStartPage := fmt.Sprintf("微小原始ID:%s", platRules.OriginalId)
			dyStartParam := fmt.Sprintf("小红书投放路径:pages/miniMovie/index?parentId=%v&subId=%v&click_id=__CLICK_ID__&advertiser_id=__ADVERTISER_ID__&campaign_id=__CAMPAIGN_ID__&unit_id=__UNIT_ID__&creativity_id=__CREATIVITY_ID__&account=%s&channel=%v%s",
				videoId, theaterDetail.SubId, account, commonConsts.XHS, wxParam)
			suffix := fmt.Sprintf("&account=%s&channel=%v&parentId=%v&subId=%v", account, commonConsts.DOUYIN, videoId, theaterDetail.SubId)
			monitoringLink := fmt.Sprintf("小红书监测链接:%s%s%s", monitorLinkHost, commonConsts.XHSLLink, suffix)
			link = fmt.Sprintf("%s\n%s\n%s", dyStartPage, dyStartParam, monitoringLink)
		}
	default:
		err = errors.New("不支持的广告平台")
		liberr.ErrIsNil(ctx, err)
	}
	return
}
