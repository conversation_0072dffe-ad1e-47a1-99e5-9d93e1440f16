/*
* @desc:常量
* @company:云南奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON><<EMAIL>>
* @Date:   2022/3/30 11:54
 */

package consts

import (
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	"strings"
)

const (
	PAY_WX         = iota + 1 //  微信
	PAY_ALI                   //  支付宝
	PAY_GOLD_COIN             //  金币
	PAY_H5                    //  H5微信
	PAY_WX_VIRTUAL            //  微信虚拟支付
	PAY_MH                    //  米花支付
	PAY_DY         = 10       // 抖音支付
	PAY_DY_DIAMOND = 20       // 抖音钻石支付
)

const (
	PayWx        = "PAY_WX"         //  微信
	PayAli       = "PAY_ALI"        //  支付宝
	PayGoldCoin  = "PAY_GOLD_COIN"  //  金币
	PayH5        = "PAY_H5"         //  H5微信
	PayWxVirtual = "PAY_WX_VIRTUAL" //  微信虚拟支付
	PayMh        = "PAY_MH"         //  米花支付
	PayDy        = "PAY_DY"         //  抖音支付
	PayDyDiamond = "PAY_DY_DIAMOND" //  抖音钻石支付
)

const DyCurrency = "DIAMOND"

// 广告平台渠道号
const (
	TOUTIAO      = iota + 4 //巨量
	BAIDU                   //百度
	VIVO                    //vivo
	TENCENT                 //腾讯
	KS                      //快手
	DOUYIN                  //抖音小程序
	XINGTU                  //星图
	XINGTUDOUYIN            //星图抖小
	WEIBO                   //微博
	DENGHUO                 //灯火
	BILIBILI                //哔哩哔哩
	SHIPINGHAO              // 微信视频号
	XHS                     // 小红书
)

// GetADTypeByChannelType 根据订单的channel type 获取广告平台
func GetADTypeByChannelType(channelType int) string {
	switch channelType {
	case TOUTIAO, DOUYIN:
		return GetAdStrByAdEmu(TouTiaoAd)

	case BAIDU:
		return GetAdStrByAdEmu(BaiDuAd)

	case VIVO:
		return GetAdStrByAdEmu(VivoAd)

	case TENCENT:
		return GetAdStrByAdEmu(TencentAd)

	case KS:
		return GetAdStrByAdEmu(KsAd)

	case XINGTU, XINGTUDOUYIN:
		return GetAdStrByAdEmu(XingTuAd)

	case WEIBO:
		return GetAdStrByAdEmu(WeiBoAd)
	case DENGHUO:
		return GetAdStrByAdEmu(DengHuoAd)
	case BILIBILI:
		return GetAdStrByAdEmu(BiliAd)
	case SHIPINGHAO:
		return GetAdStrByAdEmu(WXShiPingHao)
	default:
		return ""
	}
}

const (
	TouTiao      = "巨量"
	BaiDu        = "百度"
	Vivo         = "vivo"
	Tencent      = "广点通"
	Ks           = "快手"
	DouYin       = "抖音小程序"
	XingTu       = "星图"
	XingTuDouYin = "星图抖小"
	WeiBo        = "微博"
	DengHuo      = "灯火"
	BiliBili     = "B站"
	WxShiPingHao = "视频号"
)
const DY_TOKEN = "FDFD66F1C869EB38421AE607C38A6AFA"

// 充值类型
const (
	GOLD            = "金币"
	VIP             = "VIP"
	UNLOCK_EPISODES = "解锁全集"
)

// 单号标识
const (
	O  = "O"  //订单开头
	R  = "R"  //转账
	W  = "W"  //提现
	S  = "S"  //系统
	RW = "RW" //奖励
	B  = "B"  //买单
	Y  = "Y"  //云仓
	A  = "A"  //签到
	SP = "sp" //供货
	P  = "P"  //交易
	ZC = "ZC" //注册
)

// 广告回传状态
const (
	SUCCESS      = "成功"
	IGNORE       = "忽略"
	INCOMPATIBLE = "不符合"
)

// vip类型
const (
	MonthVip            = 1  //月卡
	QuarterVip          = 2  //季卡
	YearVip             = 3  //年卡
	HalfYearVip         = 4  //半年卡
	ThreeVip            = 5  //三天vip
	OneVip              = 6  //1天vip
	PlayVip             = 7  //剧vip
	WeekVip             = 8  //周vip
	BusCouponsRecharge  = 10 //乘车劵
	MonthSubscribe      = 11 //连续包月
	MonthSubscribeRenew = 12 //连续包月续订，这个是给订单用的，后续充值面板要加类型不能用这个
	FifteenVip          = 13 //15天vip
)

// 订单表order channelType 注册渠道
const (
	DyChannelType  = "DY"
	WXChannelType  = "WX"
	ZFBChannelType = "ZFB"
)

// 订单状态
const (
	UnPay         = iota + 1 //待支付
	Paid                     //已完成
	Refund                   //退款中
	RefundSuccess            //退款成功
	RefundFailed             //退款失败
)

const AliPaySuccess = "REFUND_SUCCESS"
const AliPayClosed = "TRADE_CLOSED"

// DyDetailUrl 小程序xxx详情页跳转路径
const DyDetailUrl = "pages/mine/index"

// 头条线索回传行为类型
const (
	TtRegister = 1 //注册
	TtPay      = 2 //激活付费
)

// 头条精准回传行为类型
const (
	TtAccurateRegister = "0" //激活
	TtAccuratePay      = "2" //付费
)

// 百度回传行为类型
const (
	BdRegister = 25  //注册
	BdLogin    = 49  //登录
	BdPayWatch = 118 //付费观剧
)

// 腾讯广点通回传行为类型
const (
	TxRegister      = "REGISTER"       //注册
	TxCompleteOrder = "COMPLETE_ORDER" //下单
	TxPurchase      = "PURCHASE"       //付费
)

// 快手回传行为类型
const (
	KsActivate  = 1 //激活
	KsRegister  = 2 //注册
	KsPay       = 3 //付费
	KsWakeUpApp = 4 //唤起应用
)

// 抖音回传行为类型
const (
	DyActive        = "active"         //激活
	DyActivePay     = "active_pay"     //付费
	DyGameAddiction = "game_addiction" //关键行为（激励广告）
)

// 星图回传行为类型
const (
	XtActivate = 0 //激活
	XtRegister = 1 //注册
	XtPay      = 2 //付费
)

const (
	RegisterBehavior      = 3002 //注册
	PayBehavior           = 3003 //支付
	MonitorRegisterActive = 3    //监控链接注册
	MonitorPayActive      = 4    //监控链接付费
)

const (
	AlreadyDeleteFlag = 1 //已删除

	NoDeleteFlag = 0 //未删除
)
const (
	Register = "注册"
	Pay      = "付费"
)

const (
	AllRecharges         = "所有充值"
	FirstRecharge        = "首充"
	OldUserFirstRecharge = "老用户首充"
	UserReward           = "激励广告配置"
	NewUserReward        = "激励广告新用户配置"
	OldUserReward        = "激励广告老用户配置"
)

const UrlScheme = "/api/wx/getSchemeTest"

var (
	ReportAll         = &model.ReportType{Key: "report_all", Title: "回传用户的所有充值", Time: -1}
	ReportAllDay      = &model.ReportType{Key: "report_all_day", Title: "回传用户在注册当天自然日的所有充值", Time: 24}
	ReportAllFirst6   = &model.ReportType{Key: "report_all_first6", Title: "回传用户在渠道注册6小时内的所有充值", Time: 6}
	ReportAllFirst24  = &model.ReportType{Key: "report_all_first24", Title: "回传用户在渠道注册后24小时内的所有充值", Time: 24}
	ReportAllFirst48  = &model.ReportType{Key: "report_all_first48", Title: "回传用户在渠道注册后48小时内的所有充值", Time: 48}
	ReportAllFirst72  = &model.ReportType{Key: "report_all_first72", Title: "回传用户在渠道注册后72小时内的所有充值", Time: 72}
	ReportAllFirst168 = &model.ReportType{Key: "report_all_first168", Title: "回传用户在渠道注册后168小时内的所有充值", Time: 168}

	ReportNewAll            = &model.ReportType{Key: "report_new_all", Title: "回传所有新用户的首充", Time: -1}
	ReportNewAllDay         = &model.ReportType{Key: "report_new_all_day", Title: "回传新用户在注册当天自然日的首充", Time: 24}
	ReportNewHourlyFirst6   = &model.ReportType{Key: "report_new_hourly_first6", Title: "回传新用户在渠道注册6小时内的首充", Time: 6}
	ReportNewHourlyFirst24  = &model.ReportType{Key: "report_new_hourly_first24", Title: "回传新用户在渠道注册24小时内的首充", Time: 24}
	ReportNewHourlyFirst48  = &model.ReportType{Key: "report_new_hourly_first48", Title: "回传新用户在渠道注册48小时内的首充", Time: 48}
	ReportNewHourlyFirst72  = &model.ReportType{Key: "report_new_hourly_first72", Title: "回传新用户在渠道注册72小时内的首充", Time: 72}
	ReportNewHourlyFirst168 = &model.ReportType{Key: "report_new_hourly_first168", Title: "回传新用户在渠道注册168小时内的首充", Time: 168}

	ReportOldHourlyFirst24  = &model.ReportType{Key: "report_old_hourly_first24", Title: "回传老用户在渠道注册24小时后的首充", Time: 24}
	ReportOldHourlyFirst48  = &model.ReportType{Key: "report_old_hourly_first48", Title: "回传老用户在渠道注册48小时后的首充", Time: 48}
	ReportOldHourlyFirst72  = &model.ReportType{Key: "report_old_hourly_first72", Title: "回传老用户在渠道注册72小时后首充", Time: 72}
	ReportOldHourlyFirst168 = &model.ReportType{Key: "report_old_hourly_first168", Title: "回传老用户在渠道注册168小时后首充", Time: 168}
	ReportOldAll            = &model.ReportType{Key: "report_old_all", Title: "回传所有老用户的首充", Time: -1}

	ReportRewardAll         = &model.ReportType{Key: "report_reward_all", Title: "回传所有用户", Time: -1}
	ReportRewardAllDay      = &model.ReportType{Key: "report_reward_all_day", Title: "回传注册当天自然日的所有用户", Time: 24}
	ReportRewardAllFirst6   = &model.ReportType{Key: "report_reward_all_first6", Title: "回传在渠道注册6小时内的所有用户", Time: 6}
	ReportRewardAllFirst24  = &model.ReportType{Key: "report_reward_all_first24", Title: "回传在渠道注册24小时内的所有用户", Time: 24}
	ReportRewardAllFirst48  = &model.ReportType{Key: "report_reward_all_first48", Title: "回传在渠道注册48小时内的所有用户", Time: 48}
	ReportRewardAllFirst72  = &model.ReportType{Key: "report_reward_all_first72", Title: "回传在渠道注册72小时内的所有用户", Time: 72}
	ReportRewardAllFirst168 = &model.ReportType{Key: "report_reward_all_first168", Title: "回传在渠道注册168小时内的所有用户", Time: 168}

	ReportRewardNewAll            = &model.ReportType{Key: "report_reward_new_all", Title: "回传所有新用户", Time: -1}
	ReportRewardNewAllDay         = &model.ReportType{Key: "report_reward_new_all_day", Title: "回传注册当天自然日的新用户", Time: 24}
	ReportRewardNewHourlyFirst6   = &model.ReportType{Key: "report_reward_new_hourly_first6", Title: "回传在渠道注册6小时内的新用户", Time: 6}
	ReportRewardNewHourlyFirst24  = &model.ReportType{Key: "report_reward_new_hourly_first24", Title: "回传在渠道注册24小时内的新用户", Time: 24}
	ReportRewardNewHourlyFirst48  = &model.ReportType{Key: "report_reward_new_hourly_first48", Title: "回传在渠道注册48小时内的新用户", Time: 48}
	ReportRewardNewHourlyFirst72  = &model.ReportType{Key: "report_reward_new_hourly_first72", Title: "回传在渠道注册72小时内的新用户", Time: 72}
	ReportRewardNewHourlyFirst168 = &model.ReportType{Key: "report_reward_new_hourly_first168", Title: "回传在渠道注册168小时内的新用户", Time: 168}

	ReportRewardOldHourlyFirst24  = &model.ReportType{Key: "report_reward_old_hourly_first24", Title: "回传在渠道注册24小时后的老用户", Time: 24}
	ReportRewardOldHourlyFirst48  = &model.ReportType{Key: "report_reward_old_hourly_first48", Title: "回传在渠道注册48小时后的老用户", Time: 48}
	ReportRewardOldHourlyFirst72  = &model.ReportType{Key: "report_reward_old_hourly_first72", Title: "回传在渠道注册72小时后的老用户", Time: 72}
	ReportRewardOldHourlyFirst168 = &model.ReportType{Key: "report_reward_old_hourly_first168", Title: "回传在渠道注册168小时后的老用户", Time: 168}
	ReportRewardOldAll            = &model.ReportType{Key: "report_reward_old_all", Title: "回传所有老用户", Time: -1}

	ReportMap = map[string]*model.ReportType{
		"report_all":                        ReportAll,
		"report_all_day":                    ReportAllDay,
		"report_all_first6":                 ReportAllFirst6,
		"report_all_first24":                ReportAllFirst24,
		"report_all_first48":                ReportAllFirst48,
		"report_all_first72":                ReportAllFirst72,
		"report_all_first168":               ReportAllFirst168,
		"report_new_all":                    ReportNewAll,
		"report_new_all_day":                ReportNewAllDay,
		"report_new_hourly_first6":          ReportNewHourlyFirst6,
		"report_new_hourly_first24":         ReportNewHourlyFirst24,
		"report_new_hourly_first48":         ReportNewHourlyFirst48,
		"report_new_hourly_first72":         ReportNewHourlyFirst72,
		"report_new_hourly_first168":        ReportNewHourlyFirst168,
		"report_old_hourly_first24":         ReportOldHourlyFirst24,
		"report_old_hourly_first48":         ReportOldHourlyFirst48,
		"report_old_hourly_first72":         ReportOldHourlyFirst72,
		"report_old_hourly_first168":        ReportOldHourlyFirst168,
		"report_old_all":                    ReportOldAll,
		"report_reward_all":                 ReportRewardAll,
		"report_reward_all_day":             ReportRewardAllDay,
		"report_reward_all_first6":          ReportRewardAllFirst6,
		"report_reward_all_first24":         ReportRewardAllFirst24,
		"report_reward_all_first48":         ReportRewardAllFirst48,
		"report_reward_all_first72":         ReportRewardAllFirst72,
		"report_reward_all_first168":        ReportRewardAllFirst168,
		"report_reward_new_all":             ReportRewardNewAll,
		"report_reward_new_all_day":         ReportRewardNewAllDay,
		"report_reward_new_hourly_first6":   ReportRewardNewHourlyFirst6,
		"report_reward_new_hourly_first24":  ReportRewardNewHourlyFirst24,
		"report_reward_new_hourly_first48":  ReportRewardNewHourlyFirst48,
		"report_reward_new_hourly_first72":  ReportRewardNewHourlyFirst72,
		"report_reward_new_hourly_first168": ReportRewardNewHourlyFirst168,
		"report_reward_old_hourly_first24":  ReportRewardOldHourlyFirst24,
		"report_reward_old_hourly_first48":  ReportRewardOldHourlyFirst48,
		"report_reward_old_hourly_first72":  ReportRewardOldHourlyFirst72,
		"report_reward_old_hourly_first168": ReportRewardOldHourlyFirst168,
		"report_reward_old_all":             ReportRewardOldAll,
	}
)

// 监测链接
const (
	TouTiaoLink = "/base/base/getLinkMonitor?aId=__AID__&projectId=__PROJECT_ID__&promotionId=__PROMOTION_ID__&adId=__ADVERTISER_ID__&callback=__CALLBACK_PARAM__&ip=__IP__&model=__MODEL__&ua=__UA__&imei=__IMEI__&idfa=__IDFA__&mac=__MAC__&trackId=__TRACK_ID__&oaid=__OAID__&caid=__CAID__"
	BaiDuLink   = "/base/base/baiduLinkMonitor?bd_vid=__BD_VID__&&ext_info=__EXT_INFO__&ip=__IP__&ua=__UA__"
	VivoLink    = "/base/base/vivoLinkMonitor?ip=__IP__&ua=__UA__&advertiserId=__ADVERTISERID__&requestId=__REQUESTID__&creativeId=__CREATIVEID__"
	TencentLink = "/base/base/tencentLinkMonitor?click_id=__CLICK_ID__&click_time=__CLICK_TIME__&ad_id=__AD_ID__&dynamic_creative_id=__DYNAMIC_CREATIVE_ID__&account_id=__ACCOUNT_ID__&request_id=__REQUEST_ID__&callback=__CALLBACK__&impression_id=__IMPRESSION_ID__&wechat_openid=__WECHAT_OPEN_ID__"
	WeiBoLink   = "/base/base/weiboMonitor?ip={ip}&ua={ua}&clicktime={clicktime}&IMP={IMP}&ad_id={ad_id}&creative_id={creative_id}&TSID={TSID}"
	DengHuoLink = "/api/ad/aliLinkMonitor?oaid=__OAID_MD5__&idfa=__IDFA_MD5__&request=__REQUEST_ID__&ad=__AD_ID__&plan=__PLAN_ID__&aduser=__PRINCIPAL_ID__&callback_param=__CALLBACK_EXT_INFO__&ip=__IP__&ua=__UA__&openid=__OPENID__"
	XHSLLink    = "/base/base/xhsMonitor?click_id=__CLICK_ID__&advertiser_id=__ADVERTISER_ID__&campaign_id=__CAMPAIGN_ID__&unit_id=__UNIT_ID__&creativity_id=__CREATIVITY_ID__"
)

// DefaultChannel 默认渠道号/充值模板/回传模板
const DefaultChannel = "default"
const DefaultChannelName = "默认模板"
const CustomAdSettingTemplateName = "自定义模板"
const ChannelCodeDailyNumKey = "CHANNEL:CODE:DAILY:NUM:"

// MiniProgramPath 小程序首页路径
const (
	MiniProgramPath    = "/pages/pageReadMovie/index"
	MiniProgramPathNew = "pages/miniMovie/index"
)

// GenerateShortLinkRequestURL 生成短链请求url
const GenerateShortLinkRequestURL = "/api/wx/echoLink"

// NewlineCharacter 换行符
const NewlineCharacter = "\n"

// Placeholder 占位符
const Placeholder = "####"

const ColonCharacter = ":"

const UnknownFactorCharacter = "?"

const (
	DyLinkPathPrefix  = "巨量引擎抖小跳转路径:"
	DyLinkParamPrefix = "巨量引擎抖小启动参数:"
	WxLinkPathPrefix  = "推广链接path:"
)

// OssUrlTestPrefix oss url前缀
const OssUrlTestPrefix = "test."
const OssUrlScheme = "://"

// 排序方式 (asc顺序 desc倒序)
const (
	Asc  = "asc"
	Desc = "desc"
)

// 排序字段
const (
	CreateTime                  = "createTime"
	ActivationNum               = "activationNum"
	DayTotalAmount              = "dayTotalAmount"
	WechatAndroidRechargeAmount = "wechatAndroidRechargeAmount"
	WechatIosRechargeAmount     = "wechatIosRechargeAmount"
	DyRechargeAmount            = "dyRechargeAmount"
	AccountCoinConsume          = "accountCoinConsume"
	DayRoi                      = "dayRoi"
	TotalRoi                    = "totalRoi"
	DayNewTopUp                 = "dayNewTopUp"
	TotalAmount                 = "totalAmount"
	NewUserAmount               = "newUserAmount"
	RechargeNums                = "rechargeNums"
)

// 短剧审核状态
const (
	// NotSubmitReview 未提审
	NotSubmitReview = 0
	// UnderReview 审核中
	UnderReview = 1
	// Dismissed 已驳回
	Dismissed = 2
	// Passed 已过审
	Passed = 3
)

// 微信剧目审核状态
const (
	// WxInvalidValue 无效值
	WxInvalidValue = 0
	// WxUnderReview 审核中
	WxUnderReview = 1
	// WxDismissed 审核驳回
	WxDismissed = 2
	// WxPassed 审核通过
	WxPassed = 3
	// WxRejectAndRefill 驳回重填
	WxRejectAndRefill = 4
)
const (
	FlowDetailTypeRecharge = 1 //普通充值 流水
	FlowDetailTypeMonth    = 2 //月卡充值流水
	FlowDetailTypeQuarter  = 3 //季卡充值流水

	FlowDetailTypeYear           = 4  //年卡充值流水
	FlowDetailTypeYearWatchVideo = 5  //看剧流水
	FlowDetailTypeHalfYear       = 8  //半年卡充值
	FlowDetailTypeDays           = 9  //日卡充值
	FlowDetailTypeThreeDays      = 10 //三天卡充值流水
	FlowDetailTypePlayTheater    = 11 //剧vip流水
	FlowDetailTypeSevenDays      = 12 //周卡vip流水
	FlowDetailTypeSysOperateGold = 13 //系统  操作金币流水
)
const (
	FlowTypeIncome  = 1 //流水类型，收入
	FlowTypeExpense = 2 //流水类型，支出
)

// source type 流水来源类型
const (
	SourceRecharge      = iota + 1 //普通充值
	SourceWatchVideo               //看剧
	SourceSystemOperate            //系统操作
	SourceTaskExchange             // 任务兑换
	SourceTaskRewards              // 任务奖励
	SourceShopGive                 // 商城赠送
)

const (
	FlowPayTypeWx        = 1  //流水支付类型，微信
	FlowPayTypeAli       = 2  //流水支付类型  支付宝，
	FlowPayTypeGold      = 3  //流水支付类型  金币，
	FlowPayTypeH5Wx      = 4  //流水支付类型  h5微信，
	FlowPayTypeWxVirtual = 5  //流水支付类型  微信虚拟支付，
	FlowPayTypeDy        = 10 //抖音支付
	FlowPayTypeMiHua     = 6  //米花支付
)
const (
	FlowPlatformApp = 1 //app平台
	FlowPlatformWx  = 2 //微信平台
	FlowPlatformDy  = 3 //抖音平台，
	FlowPayTypeKs   = 4 //快手
)

// 抖音广告类型
const (
	RewardAdType = "激励视频广告" //激励广告
	ScreenAdType = "插屏广告"   //插屏广告
)

const (
	Source      = "COMMON_TARGET" // 来源
	PropertyKey = "tinyapp_id"
	UuidType    = "PID"
)

// 灯火回传类型
const (
	DengHuoPayNums  = "326" //支付宝小程序网剧付费人数
	DengHuoPayMoney = "327" //支付宝小程序网剧付费金额
)

// 广告平台 用于新的导流链接配置
const (
	TouTiaoAd    = iota + 1 //巨量
	BaiDuAd                 //百度
	VivoAd                  //vivo
	TencentAd               //广点通
	KsAd                    //快手
	XingTuAd                //星图
	WeiBoAd                 //微博
	DengHuoAd               //灯火
	WechatAd                //微信
	BiliAd                  //B站
	WXShiPingHao            // 微信视频号
	XHSAD                   // 小红书
)

// GetAdStrByAdEmu 根据广告平台枚举获取枚举字符串
func GetAdStrByAdEmu(i int) string {
	switch i {
	case TouTiaoAd:
		return "巨量"
	case BaiDuAd:
		return "百度"
	case VivoAd:
		return "VIVO"
	case TencentAd:
		return "广点通"
	case KsAd:
		return "快手"
	case XingTuAd:
		return "星图"
	case WeiBoAd:
		return "微博"
	case DengHuoAd:
		return "灯火"
	case WechatAd:
		return "微信"
	case BiliAd:
		return "B站"
	case WXShiPingHao:
		return "视频号"
	default:
		return ""
	}
}

// GetProgramTypeByAppId 根据appid获取类型
func GetProgramTypeByAppId(appid string) string {
	if strings.HasPrefix(appid, "wx") {
		return "WX"
	}
	if strings.HasPrefix(appid, "tt") {
		return "DY"
	}
	return ""
}

const (
	TaskType             = "task_type"             // 任务类型
	ExchangeRewardType   = "exchange_reward_type"  // 兑换奖励类型
	VipPayType           = "VIP_PAY_TYPE"          // vip类型
	ProductType          = "product_type"          // 商品类型
	ProductSpecification = "product_specification" // 商品规格
	GiftType             = "gift_type"             // 赠品类型
)

const (
	SignIn              = iota + 1 //签到
	UnlockEpisodesDaily            //每日解锁剧集
	WatchVideosDaily               //每日看广告
	WatchDramaDaily                //每日看剧
	EnableSignIn                   //开启签到提醒
	AddDesktop                     //加止桌面
	TaskShareDaily
	TaskAddCatchUp
	TaskBrowseTheTheater
	TaskInviteFriends
)

const (
	ExchangeTickets      = iota + 1 //乘车券
	ExchangeGold                    //金币
	ExchangeVip                     //VIP
	ExchangeMovieTickets            //观影券
)

type ExchangeType int

func GetExchangeType(exchangeType ExchangeType) string {
	switch exchangeType {
	case ExchangeTickets:
		return "乘车券"
	case ExchangeGold:
		return "金币"
	case ExchangeVip:
		return "VIP"
	case ExchangeMovieTickets:
		return "观影券"
	}
	return ""
}

const (
	RewardTypeCoin = iota + 1
	RewardTypeGold
)

type RewardType int

func GetRewardType(rewardType RewardType) string {
	switch rewardType {
	case RewardTypeCoin:
		return "积分"
	case RewardTypeGold:
		return "金币"
	}
	return ""
}

const (
	PlatformWx = iota + 1 //微信平台
	PlatformDy            //抖音平台
	PlatformKs            //快手
)

const (
	PlatformWxName = "微信"
	PlatformDyName = "抖音"
	PlatformKsName = "快手"
)

const (
	ActionPublisherAdPosGeneral  = "publisher_adpos_general"
	ActionPublisherAdUnitGeneral = "publisher_adunit_general"
	ActionPublisherCpsGeneral    = "publisher_cps_general"
	ActionPublisherSettlement    = "publisher_settlement"
)

const AdSlot = "ad_slot"
const DyAppIdPrefix = "tt"
const WxAppIdPrefix = "wx"

// 签约结果返回状态 SUCCESS（用户签约成功 ） TIME_OUT （用户未签约，订单超时关单） CANCEL (解约成功) DONE （服务完成，已到期）
const (
	SignSuccess = "SUCCESS"
	SignCancel  = "CANCEL"
	SignDone    = "DONE"
	SignTimeOut = "TIME_OUT"
)

// 代扣单状态
const (
	SignPaySuccess    = "SUCCESS"    // 扣款成功
	SignPayProcessing = "PROCESSING" // 订单处理中
	SignPayTimeOut    = "TIME_OUT"   // 超时未支付 ｜超时未扣款成功
	SignPayFail       = "FAIL"       // 扣款失败，特殊情况下发起代扣直接失败才会有该状态，正常情况下用户账户余额不足，状态都是流转到TIME_OUT
)

const DyCallbackUrl = "/api/notify/dyCommonAsynchro"

// GetAdTypeByAdType 根据导流链接推广平台获取广告类型
func GetAdTypeByAdType(platform int) int {
	switch platform {
	case TouTiaoAd:
		return TOUTIAO
	case BaiDuAd:
		return BAIDU
	case VivoAd:
		return VIVO
	case TencentAd:
		return TENCENT
	case KsAd:
		return KS
	case XingTuAd:
		return XINGTU
	case WeiBoAd:
		return WEIBO
	case DengHuoAd:
		return DENGHUO
	case BiliAd:
		return BILIBILI
	case WXShiPingHao:
		return SHIPINGHAO
	default:
		return TOUTIAO
	}
}

const (
	FqTouTiao    = 1
	FqKs         = 3
	FqTencent    = 4
	FqShiPingHao = 10
)

// GetFqMediaSource 获取广告类型
func GetFqMediaSource(platform int) int {
	switch platform {
	case TouTiaoAd:
		return FqTouTiao
	case KsAd:
		return FqKs
	case TencentAd:
		return FqTencent
	case WXShiPingHao:
		return FqShiPingHao
	default:
		return FqTouTiao
	}
}

func GetPlatformByMediaSource(mediaSource int) int {
	switch mediaSource {
	case FqTouTiao:
		return TouTiaoAd
	case KsAd:
		return FqKs
	case TencentAd:
		return FqTencent
	case WXShiPingHao:
		return FqShiPingHao
	default:
		return TouTiaoAd
	}
}

const (
	DzDy  = "dy"  // 抖音
	DzKs  = "ks"  // 快手
	DzBd  = "bd"  // 百度
	DzAdq = "adq" // 广点通
)

// GetDzMediaSource 获取广告类型
func GetDzMediaSource(platform int) string {
	switch platform {
	case TouTiaoAd:
		return DzDy
	case KsAd:
		return DzKs
	case BaiDuAd:
		return DzBd
	case TencentAd:
		return DzAdq
	default:
		return DzDy
	}
}

func GetPlatformByDzMediaSource(mediaSource string) int {
	switch mediaSource {
	case DzDy:
		return TouTiaoAd
	case DzKs:
		return FqKs
	case DzBd:
		return BaiDuAd
	case DzAdq:
		return FqTencent
	default:
		return TouTiaoAd
	}
}

const (
	DzDyDomain = 14 // 抖音小程序
	DzWxDomain = 16 // 微信小程序
	DzKsDomain = 17 // 快手小程序
)

const (
	DzAndroidOs = "0" // 安卓
	DzIosOs     = "1" // ios
)
