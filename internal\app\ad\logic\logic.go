package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adAnchorPoint"
	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adAnchorPointImages"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adAnchorPointUpload"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adAppConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adBatchTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adBatchTaskDetail"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adCallback"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adCommonAssetCategory"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adCommonAssetPackage"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adCommonAssetTitle"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adDesignerMaterialReport"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adHomepageData"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adLandingPageTemp"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adMaterial"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adMaterialAlbum"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adMaterialAlbumDepts"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adMaterialAlbumUsers"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adMaterialFile"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adMaterialUpload"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adPlanChannelExecute"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adPlanExecute"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adPlanLog"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adPlanRule"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adPlanSetting"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adThirdMiniProgramConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adToolsSite"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adXtAccount"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adXtTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adXtTaskSettle"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/adXtTaskSettleDaily"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdAccount"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdAccountChannel"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdAccountDepts"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdAccountUsers"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdEcpm"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdOrderInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzAdUserInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/dzTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdAccount"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdAccountChannel"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdAccountDepts"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdAccountUsers"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdAnalyzeData"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdAnalyzeDataDay"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdUserInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdUserPaymentRecord"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/fqAdUserRewardClick"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAccountSeries"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAdAccountInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAdInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAdOrderDetail"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAdOrderSettle"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAdReportStats"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksAdSalerCopyRight"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/ksSeriesReportCoreData"

	_ "github.com/tiger1103/gfast/v3/internal/app/ad/logic/mpAdEvent"
)
