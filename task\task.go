/*
* @desc:测试定时任务
* @company:云南省奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON>
* @Date:   2021/7/16 15:52
 */

package task

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	adxservice "github.com/tiger1103/gfast/v3/internal/app/adx/service"
	appletService "github.com/tiger1103/gfast/v3/internal/app/applet/service"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	memberService "github.com/tiger1103/gfast/v3/internal/app/member/service"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	orderService "github.com/tiger1103/gfast/v3/internal/app/order/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterService "github.com/tiger1103/gfast/v3/internal/app/theater/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

func Test1(ctx context.Context) {
	fmt.Println("无参测试")
	service.SysJobLog().Add(ctx, &do.SysJobLog{
		TargetName: "test1",
		CreatedAt:  gtime.Now(),
		Result:     "无参测试运行成功",
	})
}

func Test2(ctx context.Context) {
	//获取参数
	t := service.TaskList().GetByName("test2")
	if t == nil {
		return
	}
	for _, v := range t.Param {
		fmt.Printf("参数:%s;  ", v)
	}
	fmt.Println()
	service.SysJobLog().Add(ctx, &do.SysJobLog{
		TargetName: "test2",
		CreatedAt:  gtime.Now(),
		Result:     "有参测试运行成功",
	})
}

func HourRefreshCoinTask(ctx context.Context) {
	err := service.SysJobLog().ChannelRechargeStat(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "HourRefreshCoinTask",
			CreatedAt:  gtime.Now(),
			Result:     "HourRefreshCoinTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "HourRefreshCoinTask",
			CreatedAt:  gtime.Now(),
			Result:     "HourRefreshCoinTask运行成功",
		})
	}
}

func OrderStatTask(ctx context.Context) {
	err := orderService.OrderTask().OrderStatTask(ctx, libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "OrderStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "OrderStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "OrderStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "OrderStatTask运行成功",
		})
	}
}

func SetUserAdditionalVideoByTimeTask(ctx context.Context) {
	err := service.SysUser().SetUserAdditionalVideoByTime(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SetUserAdditionalVideoByTimeTask",
			CreatedAt:  gtime.Now(),
			Result:     "SetUserAdditionalVideoByTimeTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SetUserAdditionalVideoByTimeTask",
			CreatedAt:  gtime.Now(),
			Result:     "SetUserAdditionalVideoByTimeTask运行成功",
		})
	}
}

func UpdateMetricsTask(ctx context.Context) {
	err := oceanengineService.AdAdvertiserAccountMetricsData().AdAdvertiserAllMetricsReportStat(ctx, libUtils.GetYesterdayDateString(), libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "UpdateMetricsTask",
			CreatedAt:  gtime.Now(),
			Result:     "UpdateMetricsTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "UpdateMetricsTask",
			CreatedAt:  gtime.Now(),
			Result:     "UpdateMetricsTask运行成功",
		})
	}
}

func ChannelStatTask(ctx context.Context) {
	err := orderService.OrderTask().ChannelStatTask(ctx, libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelStatTask运行成功",
		})
	}
}

func ChannelRechargeStatTask(ctx context.Context) {
	// 同步番茄推广链接
	_ = channelService.SChannel().SyncFqPromotionUrl(ctx, libUtils.GetYesterdayDateString(), "")
	// 同步点众推广链接
	_ = channelService.SChannel().SyncDzPromotionUrl(ctx, libUtils.GetYesterdayDateString(), "")
	err := orderService.OrderTask().CreateChannelRechargeStat(ctx, libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelRechargeStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelRechargeStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelRechargeStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelRechargeStatTask运行成功",
		})
	}
}

func ChannelRechargeStatTodayTask(ctx context.Context) {
	err := orderService.OrderTask().CreateChannelRechargeStat(ctx, libUtils.GetNowDate())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelRechargeStatTodayTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelRechargeStatTodayTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelRechargeStatTodayTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelRechargeStatTodayTask运行成功",
		})
	}
}

func NoRechargeHaveCoinConsumeChannelStatTask(ctx context.Context) {
	err := orderService.OrderTask().CreateNoRechargeHaveCoinConsumeStat(ctx, libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "NoRechargeHaveCoinConsumeChannelStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "NoRechargeHaveCoinConsumeChannelStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "NoRechargeHaveCoinConsumeChannelStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "NoRechargeHaveCoinConsumeChannelStatTask运行成功",
		})
	}

}

func WxNoRechargeHaveCoinConsumeChannelStatTask(ctx context.Context) {
	err := orderService.OrderTask().CreateWxNoRechargeHaveCoinConsumeStat(ctx, libUtils.GetNowDate())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "WxNoRechargeHaveCoinConsumeChannelStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "WxNoRechargeHaveCoinConsumeChannelStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "WxNoRechargeHaveCoinConsumeChannelStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "WxNoRechargeHaveCoinConsumeChannelStatTask运行成功",
		})
	}

}

func ChannelDetailStatTask(ctx context.Context) {
	err := orderService.OrderTask().ChannelDetailStatTask(ctx, libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelDetailStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelDetailStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelDetailStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelDetailStatTask运行成功",
		})
	}

}

func VideoStatTask(ctx context.Context) {
	err := orderService.OrderTask().VideoStatTask(ctx, libUtils.GetYesterdayDateString())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "VideoStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "VideoStatTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "VideoStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "VideoStatTask运行成功",
		})
	}

}

func VideoRechargeStatisticsTask(ctx context.Context) {
	err := orderService.OrderTask().SVideoRechargeStatistics(ctx, libUtils.GetYesterdayDateString(), libUtils.GetYesterdayDateString(), false)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "VideoRechargeStatisticsTask",
			CreatedAt:  gtime.Now(),
			Result:     "VideoRechargeStatisticsTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "VideoRechargeStatisticsTask",
			CreatedAt:  gtime.Now(),
			Result:     "VideoRechargeStatisticsTask运行成功",
		})
	}

}

func SendSignRemind(ctx context.Context) {
	err := memberService.MMemberSignRemind().SendSignRemind(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SendSignRemind",
			CreatedAt:  gtime.Now(),
			Result:     "SendSignRemind运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SendSignRemind",
			CreatedAt:  gtime.Now(),
			Result:     "SendSignRemind运行成功",
		})
	}
}

// SAppletDepositRetentionStatistics 同步昨天剧集的留存数据
func SAppletDepositRetentionStatistics(ctx context.Context) {
	err := appletService.SAppletDepositRetentionStatistics().Statistics(ctx, libUtils.GetYesterdayDateString(), libUtils.GetYesterdayDateString(), false)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SAppletDepositRetentionStatistics",
			CreatedAt:  gtime.Now(),
			Result:     "SAppletDepositRetentionStatistics运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SAppletDepositRetentionStatistics",
			CreatedAt:  gtime.Now(),
			Result:     "SAppletDepositRetentionStatistics运行成功",
		})
	}
}

func SAppletDepositRetentionStatisticsToday(ctx context.Context) {
	//SyncTheaterRetentionStat
	err := appletService.SAppletDepositRetentionStatistics().Statistics(ctx, libUtils.GetNowDate(), libUtils.GetNowDate(), false)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SAppletDepositRetentionStatisticsToday",
			CreatedAt:  gtime.Now(),
			Result:     "SAppletDepositRetentionStatisticsToday运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SAppletDepositRetentionStatisticsToday",
			CreatedAt:  gtime.Now(),
			Result:     "SAppletDepositRetentionStatisticsToday运行成功",
		})
	}
}

// SyncTheaterRetentionStat 同步昨天剧集的留存数据
func SyncTheaterRetentionStat(ctx context.Context) {
	//SyncTheaterRetentionStat
	err := theaterService.TheaterUserRetentionStat().SyncTheaterRetentionStat(ctx, libUtils.GetYesterdayDateString(), libUtils.GetYesterdayDateString(), false)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SyncTheaterRetentionStat",
			CreatedAt:  gtime.Now(),
			Result:     "SyncTheaterRetentionStat运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SyncTheaterRetentionStat",
			CreatedAt:  gtime.Now(),
			Result:     "SyncTheaterRetentionStat运行成功",
		})
	}
}

func SyncTheaterRetentionStatToday(ctx context.Context) {
	//SyncTheaterRetentionStat
	err := theaterService.TheaterUserRetentionStat().SyncTheaterRetentionStat(ctx, libUtils.GetNowDate(), libUtils.GetNowDate(), false)
	err = theaterService.TheaterUserRetentionStat().SetTheaterHourRetentionStat(ctx, libUtils.GetNowDate(), -1)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SyncTheaterRetentionStatToday",
			CreatedAt:  gtime.Now(),
			Result:     "SyncTheaterRetentionStatToday运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SyncTheaterRetentionStatToday",
			CreatedAt:  gtime.Now(),
			Result:     "SyncTheaterRetentionStatToday运行成功",
		})
	}
}

func RefreshTokenTask(ctx context.Context) {
	//RefreshTokenTask
	err := commonService.AdTask().RefreshTokenTask(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "RefreshTokenTask",
			CreatedAt:  gtime.Now(),
			Result:     "RefreshTokenTask运行失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "RefreshTokenTask",
			CreatedAt:  gtime.Now(),
			Result:     "RefreshTokenTask运行成功",
		})
	}
}
func ChannelHourStatTask(ctx context.Context) {
	err := orderService.OrderTask().ChannelHourStatTask(ctx, gtime.Now().String())
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelHourStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelHourStatTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ChannelHourStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "ChannelHourStatTask",
		})
	}
}

func CalcDistributionStatisticsTodayTask(ctx context.Context) {
	err := orderService.SDistributionStatistics().CalcDistributionStatisticsTodayTask(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcDistributionStatisticsTodayTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcDistributionStatisticsTodayTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcDistributionStatisticsTodayTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcDistributionStatisticsTodayTask",
		})
	}
}
func CalcPitcherVideoRechargeTask(ctx context.Context) {

	//使用参数
	//statDate := service.TaskList().GetByName("statDate")

	startDate := gtime.Now().Format("Y-m-d")
	//startDate := "2024-08-08"

	err := orderService.OrderInfo().CalPitcherVideoRechargeTask(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcPitcherVideoRechargeTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcPitcherVideoRechargeTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcPitcherVideoRechargeTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcPitcherVideoRechargeTask执行成功",
		})
	}
}
func CalcPitcherVideoYesterRechargeTask(ctx context.Context) {

	//使用参数
	//statDate := service.TaskList().GetByName("statDate")
	startDate := libUtils.GetYesterdayDate()
	err := orderService.OrderInfo().CalPitcherVideoRechargeTask(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcPitcherVideoYesterRechargeTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcPitcherVideoYesterRechargeTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcPitcherVideoYesterRechargeTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcPitcherVideoYesterRechargeTask执行成功",
		})
	}
}
func TheaterPlayRechargeStatTask(ctx context.Context) {

	startDate := gtime.Now().Format("Y-m-d")
	err := theaterService.TheaterPlayRechargeStat().RunStat(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "TheaterPlayRechargeStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "TheaterPlayRechargeStatTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "TheaterPlayRechargeStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "TheaterPlayRechargeStatTask执行成功",
		})
	}
}
func TheaterPlayRechargeYesterStatTask(ctx context.Context) {

	startDate := libUtils.GetYesterdayDate()
	err := theaterService.TheaterPlayRechargeStat().RunStat(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "TheaterPlayRechargeYesterStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "TheaterPlayRechargeYesterStatTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "TheaterPlayRechargeYesterStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "TheaterPlayRechargeYesterStatTask执行成功",
		})
	}
}
func CalcAppletRechargeTask(ctx context.Context) {
	startDate := gtime.Now().Format("Y-m-d")
	err := orderService.OrderInfo().CalAppletRechargeTask(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcAppletRechargeTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcAppletRechargeTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcAppletRechargeTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcAppletRechargeTask执行成功",
		})
	}
}
func CalcAppletRechargeYesterTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	err := orderService.OrderInfo().CalAppletRechargeTask(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcAppletRechargeYesterTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcAppletRechargeYesterTask，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "CalcAppletRechargeYesterTask",
			CreatedAt:  gtime.Now(),
			Result:     "CalcAppletRechargeYesterTask执行成功",
		})
	}
}
func OrderCouponCodeRecoveryTask(ctx context.Context) {
	err := orderService.OrderInfo().OrderCouponCodeRecoveryTask(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "OrderCouponCodeRecoveryTask",
			CreatedAt:  gtime.Now(),
			Result:     "OrderCouponCodeRecoveryTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "OrderCouponCodeRecoveryTask",
			CreatedAt:  gtime.Now(),
			Result:     "OrderCouponCodeRecoveryTask执行成功",
		})
	}
}

func KsADDataTask(ctx context.Context) {
	err := adService.KsAdReportStats().DayAdDataTask(ctx, "")
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "KsADDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "KsADDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "KsADDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "KsADDataTask执行成功",
		})
	}
}

func XTADYesterdayDataTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	_, err := adService.AdXtTask().Async(ctx, startDate, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "XTADYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "XTADYesterdayDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "XTADYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "XTADYesterdayDataTask执行成功",
		})
	}
}

func XTADDetailDataTask(ctx context.Context) {
	err := adService.AdXtTaskSettle().PullDetail(ctx, "")
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "XTADDetailDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "XTADDetailDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "XTADDetailDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "XTADDetailDataTask执行成功",
		})
	}
}

func KsSeriesReportCoreDataTask(ctx context.Context) {
	err := adService.KsSeriesReportCoreData().DayAdDataTask(ctx, "")
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "KsSeriesReportCoreDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "KsSeriesReportCoreDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "KsSeriesReportCoreDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "KsSeriesReportCoreDataTask执行成功",
		})
	}
}

func FqUserInfoDataTask(ctx context.Context) {
	startDate := gtime.Now().Format("Y-m-d")
	_, err := adService.FqAdUserInfo().TimedPull(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqUserInfoDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqUserInfoDataTask失败，用户信息Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqUserInfoDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqUserInfoDataTask执行成功",
		})
	}
}

func FqUserPayDataTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	_, err := adService.FqAdUserPaymentRecord().TimedPull(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqUserPayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqUserPayDataTask，用户支付Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqUserPayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqUserPayDataTask.执行成功",
		})
	}
}

func DzOrderInfoDataTask(ctx context.Context) {
	_, err := adService.DzAdOrderInfo().TimedPull(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "DzOrderInfoDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "DzOrderInfoDataTask，用户支付Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "DzOrderInfoDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "DzOrderInfoDataTask.执行成功",
		})
	}
}
func DzOrderInfoYesterdayDataTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	err := adService.DzAdOrderInfo().PullOrderData(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "DzOrderInfoYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "DzOrderInfoYesterdayDataTask，用户支付Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "DzOrderInfoYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "DzOrderInfoYesterdayDataTask.执行成功",
		})
	}

}

func FqUserPayTodayDataTask(ctx context.Context) {
	startDate := libUtils.GetNowDate()
	_, err := adService.FqAdUserPaymentRecord().TimedPull(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqUserPayTodayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqUserPayTodayDataTask，用户支付Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqUserPayTodayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqUserPayTodayDataTask.执行成功",
		})
	}
}

// ADXHotRankingYesterdayDataTask 获取热力榜昨天的数据
func ADXHotRankingYesterdayDataTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	err := adxservice.AdxHotRanking().PullAllData(ctx, startDate, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ADXHotRankingYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "ADXHotRankingYesterdayDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ADXHotRankingYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "ADXHotRankingYesterdayDataTask执行成功",
		})
	}
}

// ADXHotRankingNowDataTask 获取热力榜当前的数据 （废弃）
func ADXHotRankingNowDataTask(ctx context.Context) {
	startDate := libUtils.GetNowDate()
	err := adxservice.AdxHotRanking().PullAllData(ctx, startDate, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ADXHotRankingNowDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "ADXHotRankingNowDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "ADXHotRankingNowDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "ADXHotRankingNowDataTask执行成功",
		})
	}
}

// 同步mp_ad_event
func SyncMpAdEventTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	err := adService.MpAdEvent().SyncMpAdEventTask(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SyncMpAdEventTask",
			CreatedAt:  gtime.Now(),
			Result:     "SyncMpAdEventTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "SyncMpAdEventTask",
			CreatedAt:  gtime.Now(),
			Result:     "SyncMpAdEventTask执行成功",
		})
	}
}

// 拉取ADX 剧集所有数据
func AdxPlayletDataTask(ctx context.Context) {
	err := adxservice.AdxPlaylet().PullAllData(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxPlayletDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxPlayletDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxPlayletDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxPlayletDataTask执行成功",
		})
	}
}

func PullAdDesignerMaterialReport(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	_, err := adService.AdDesignerMaterialReport().PullData(ctx, &ad.AdDesignerMaterialReportPullDataReq{
		StartTime: startDate,
		EndTime:   startDate,
	})
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "PullAdDesignerMaterialReport",
			CreatedAt:  gtime.Now(),
			Result:     "PullAdDesignerMaterialReport失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "PullAdDesignerMaterialReport",
			CreatedAt:  gtime.Now(),
			Result:     "PullAdDesignerMaterialReport执行成功",
		})
	}
}

func AdxHotRankingDetailDataTask(ctx context.Context) {
	err := adxservice.AdxPlaylet().GetDetail3Timer(ctx)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxHotRankingDetailDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxHotRankingDetailDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxHotRankingDetailDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxHotRankingDetailDataTask执行成功",
		})
	}
}

// 拉取ADX 昨天剧集所有数据
func AdxPlayletYesterdayDataTask(ctx context.Context) {
	startDate := libUtils.GetYesterdayDate()
	err := adxservice.AdxPlaylet().PullIncrementalData(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxPlayletYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxPlayletYesterdayDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxPlayletYesterdayDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxPlayletYesterdayDataTask执行成功",
		})
	}
}

// 拉取ADX 当天剧集数据
func AdxPlayletNowDataTask(ctx context.Context) {
	startDate := libUtils.GetNowDate()
	err := adxservice.AdxPlaylet().PullIncrementalData(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxPlayletNowDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxPlayletNowDataTask失败，Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "AdxPlayletNowDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "AdxPlayletNowDataTask执行成功",
		})
	}
}

func FqAnalyzeDataTask(ctx context.Context) {
	startDate := gtime.Now().Format("Y-m-d")
	_, err := adService.FqAdAnalyzeData().TimedPull(ctx, startDate)
	if err != nil {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqAnalyzeDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqAnalyzeDataTask失败，回本统计Err：" + err.Error(),
		})
	} else {
		service.SysJobLog().Add(ctx, &do.SysJobLog{
			TargetName: "FqAnalyzeDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "FqAnalyzeDataTask执行成功",
		})
	}
}
