// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-23 15:51:09
// 生成路径: internal/app/ad/logic/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/entity"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoApi "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"time"
)

func init() {
	service.RegisterAdMaterialUpload(New())
}

func New() service.IAdMaterialUpload {
	return &sAdMaterialUpload{}
}

type sAdMaterialUpload struct{}

func (s *sAdMaterialUpload) List(ctx context.Context, req *model.AdMaterialUploadSearchReq) (listRes *model.AdMaterialUploadSearchRes, err error) {
	listRes = new(model.AdMaterialUploadSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdMaterialUpload.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Id+" = ?", req.Id)
		}
		if req.MediaType != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().MediaType+" = ?", gconv.Int(req.MediaType))
		}
		if req.AdMaterialId != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().AdMaterialId+" = ?", gconv.Int(req.AdMaterialId))
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.MediaId != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().MediaId+" = ?", req.MediaId)
		}
		if req.Size != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Size+" = ?", gconv.Int64(req.Size))
		}
		if req.Width != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Width+" = ?", gconv.Int(req.Width))
		}
		if req.Height != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Height+" = ?", gconv.Int(req.Height))
		}
		if req.Url != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Url+" = ?", req.Url)
		}
		if req.Format != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Format+" = ?", req.Format)
		}
		if req.Signature != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().Signature+" = ?", req.Signature)
		}
		if req.MaterialId != "" {
			m = m.Where(dao.AdMaterialUpload.Columns().MaterialId+" = ?", gconv.Int(req.MaterialId))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdMaterialUpload.Columns().CreatedAt+" >=? AND "+dao.AdMaterialUpload.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdMaterialUploadListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdMaterialUploadListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdMaterialUploadListRes{
				Id:           v.Id,
				MediaType:    v.MediaType,
				AdMaterialId: v.AdMaterialId,
				AdvertiserId: v.AdvertiserId,
				MediaId:      v.MediaId,
				Size:         v.Size,
				Width:        v.Width,
				Height:       v.Height,
				Url:          v.Url,
				Format:       v.Format,
				Signature:    v.Signature,
				MaterialId:   v.MaterialId,
				Duration:     v.Duration,
				CreatedAt:    v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdMaterialUpload) GetById(ctx context.Context, id int) (res *model.AdMaterialUploadInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialUpload.Ctx(ctx).WithAll().Where(dao.AdMaterialUpload.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialUpload) GetByMaterialId(ctx context.Context, id, VideoThumbnailId int, haveImage bool) (res *model.AdMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterial.Ctx(ctx).WithAll().Where(dao.AdMaterial.Columns().MaterialId, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if res.MaterialId == 0 {
			err = fmt.Errorf("素材不存在")
		}
		upload := new(model.AdMaterialUploadInfoRes)
		err = dao.AdMaterialUpload.Ctx(ctx).WithAll().Where(dao.AdMaterialUpload.Columns().MaterialId, id).Scan(&upload)
		if upload.Id > 0 {
			res.MediaId = upload.MediaId

		} else {

		}

		// 自己上传替换了自动生成的图片
		//if haveImage {
		//	VideoThumbnailId, err := s.GetByMaterialId(ctx, VideoThumbnailId,0, false)
		//} else {
		//	// 默认使用缩略图进行替换
		//
		//}

	})
	return
}

func (s *sAdMaterialUpload) Add(ctx context.Context, req *model.AdMaterialUploadAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialUpload.Ctx(ctx).Insert(do.AdMaterialUpload{
			MediaType:    req.MediaType,
			AdMaterialId: req.AdMaterialId,
			AdvertiserId: req.AdvertiserId,
			MediaId:      req.MediaId,
			Size:         req.Size,
			Width:        req.Width,
			Height:       req.Height,
			Url:          req.Url,
			Format:       req.Format,
			Signature:    req.Signature,
			MaterialId:   req.MaterialId,
			Duration:     req.Duration,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdMaterialUpload) Edit(ctx context.Context, req *model.AdMaterialUploadEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialUpload.Ctx(ctx).WherePri(req.Id).Update(do.AdMaterialUpload{
			MediaType:    req.MediaType,
			AdMaterialId: req.AdMaterialId,
			AdvertiserId: req.AdvertiserId,
			MediaId:      req.MediaId,
			Size:         req.Size,
			Width:        req.Width,
			Height:       req.Height,
			Url:          req.Url,
			Format:       req.Format,
			Signature:    req.Signature,
			MaterialId:   req.MaterialId,
			Duration:     req.Duration,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdMaterialUpload) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialUpload.Ctx(ctx).Delete(dao.AdMaterialUpload.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdMaterialUpload) GetByIds(ctx context.Context, req []int) (res []*model.AdMaterialUploadInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialUpload.Ctx(ctx).WithAll().WhereIn(dao.AdMaterialUpload.Columns().Id, req).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialUpload) SyncMediaMaterialToLocal(ctx context.Context, req *model.SyncMediaMaterialToLocalReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		// 根据id 获取素材
		list, err := s.GetByIds(ctx, req.Ids)
		liberr.ErrIsNil(ctx, err)
		batchAddReq := make([]*model.AdMaterialAddReq, 0)
		for _, item := range list {
			item.Id = 0
			item.AdvertiserId = req.AdvertiserId
			item.CreatedAt = gtime.Now()
			batchAddReq = append(batchAddReq, UploadInfoToAdMaterialAdd(ctx, req.AlbumId, req.FileId, int(userInfo.Id), item))
		}
		// 构建批量插入ad_material
		err = service.AdMaterial().BatchAdd(ctx, req.FileId, batchAddReq)
		liberr.ErrIsNil(ctx, err, "批量添加失败")
	})
	return err
}

func UploadInfoToAdMaterialAdd(ctx context.Context, albumId, fileId, userId int, req *model.AdMaterialUploadInfoRes) *model.AdMaterialAddReq {
	if req == nil {
		return nil
	}
	materialType := ""
	if req.MediaType == 1 {
		materialType = consts.ImageMaterialType
	} else {
		materialType = consts.VideoMaterialType
	}
	return &model.AdMaterialAddReq{
		//MaterialId:   req.MaterialId,
		AlbumId:      albumId,
		FileId:       fileId,
		MaterialName: req.MaterialName,
		MaterialType: materialType,
		UserId:       userId,
		FileUri:      req.Url,
		ThumbnailUri: req.Url,
		FileFormat:   req.Format,
		FileSize:     fmt.Sprintf("%d", req.Size),
		Width:        req.Width,
		Height:       req.Height,
		//ManageStatus:  req.s,
		//Remark:        req.rem,
		VideoDuration: int(req.Duration),
	}
}

// Pull 拉取素材
func (s *sAdMaterialUpload) Pull(ctx context.Context, aId string, materialType string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 根据id 获取 token
		token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aId)
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		//根据参数调用底层接口拉取数据
		addList := make([]*entity.AdMaterialUpload, 0)
		var pageNo int64 = 1
		var pageSize int64 = 100
		for {
			if materialType == consts.ImageMaterialType {
				result, err := advertiser.GetToutiaoApiClient().FileImageGetV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoApi.ApiOpenApi2FileImageGetGetRequest{
					AdvertiserId: gconv.Int64(aId),
					Page:         &pageNo,
					PageSize:     &pageSize,
				}).Do()
				liberr.ErrIsNil(ctx, err)
				if result.Data == nil || result.Data.List == nil || len(result.Data.List) == 0 {
					break
				}
				for _, item := range result.Data.List {
					addList = append(addList, imageToEntity(aId, item))
				}
				if *result.Data.PageInfo.TotalPage <= pageNo {
					break
				}

			} else {
				result, err := advertiser.GetToutiaoApiClient().FileVideoGetV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoApi.ApiOpenApi2FileVideoGetGetRequest{
					AdvertiserId: gconv.Int64(aId),
					Page:         &pageNo,
					PageSize:     &pageSize,
				}).Do()
				liberr.ErrIsNil(ctx, err)
				if result.Data == nil || result.Data.List == nil || len(result.Data.List) == 0 {
					break
				}
				for _, item := range result.Data.List {
					addList = append(addList, videoToEntity(aId, item))
				}
				if *result.Data.PageInfo.TotalPage <= pageNo {
					break
				}
			}
			pageNo++
		}
		// 将数据存储到表中 // 需要判断当前素材是否已经存在
		// 先入库upload 表格
		_, err = dao.AdMaterialUpload.Ctx(ctx).Save(addList)
		liberr.ErrIsNil(ctx, err)

	})
	return
}

// UpLoad 推送
func (s *sAdMaterialUpload) UpLoad(ctx context.Context, aId string, fileId int) (meadId string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if exist, mediaId, _ := s.IsExist(ctx, aId, fileId); exist {
			meadId = mediaId
			return
		}

		file := new(model.AdMaterialInfoRes)
		err = dao.AdMaterial.Ctx(ctx).Where(dao.AdMaterial.Columns().MaterialId, fileId).Scan(&file)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if file.MaterialType == consts.VideoMaterialType {
			meadId, err = s.UpLoadVideo(ctx, aId, *file)
			return
		}
		token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aId)
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		//FileImageAdV2ApiService
		req := toutiaoApi.ApiOpenApi2FileImageAdPostRequest{
			AdvertiserId: gconv.Int64(aId),
			Filename:     file.MaterialName,
			ImageUrl:     file.FileUri,
			UploadType:   toutiaoModels.UPLOAD_BY_URL_FileImageAdV2UploadType,
			IsAigc:       false,
		}

		// 6. 发起上传
		result, uploadErr := advertiser.GetToutiaoApiClient().
			FileImageAdV2ApiService.AccessToken(token.AccessToken).
			SetRequest(req).Do()
		if uploadErr != nil || result == nil || result.Data == nil {
			errMsg := fmt.Sprintf("上传图片失败！err:%v, req:%+v, token:%s, result:%+v", uploadErr, req, token.AccessToken, result)
			g.Log().Error(ctx, errMsg)
			liberr.ErrIsNil(ctx, errors.New("上传图片失败"), errMsg)
		}

		// 7. 入库
		insertReq := do.AdMaterialUpload{
			MediaType:    1,
			AdMaterialId: file.MaterialId,
			AdvertiserId: gconv.Int64(aId),
			MediaId:      result.Data.Id,
			MaterialName: file.MaterialName,
			Size:         result.Data.Size,
			Width:        result.Data.Width,
			Height:       result.Data.Height,
			Url:          file.FileUri,
			Format:       result.Data.Format,
			MaterialId:   gconv.Int64(result.Data.MaterialId),
			CreatedAt:    gtime.Now(),
		}
		_, err = dao.AdMaterialUpload.Ctx(ctx).Insert(insertReq)
		liberr.ErrIsNil(ctx, err, "插入 AdMaterialUpload 失败")

		// 8. 返回结果
		meadId = *result.Data.Id
	})
	return
}

// IsExist 判断当前素材在aid中是否存在
func (s *sAdMaterialUpload) IsExist(ctx context.Context, aId string, fileId int) (isExist bool, mediaId string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		isExist = false
		mediaId = ""
		var file *model.AdMaterialInfoRes
		err = dao.AdMaterialUpload.Ctx(ctx).Where(dao.AdMaterialUpload.Columns().AdMaterialId, fileId).Where(dao.AdMaterialUpload.Columns().AdvertiserId, aId).Scan(&file)
		//liberr.ErrIsNil(ctx, err, "获取信息失败")
		if file != nil && len(file.MediaId) > 0 {
			isExist = true
			mediaId = file.MediaId
			return
		}
	})
	return
}

// UpLoadVideoAsync 异步上传视频
func (s *sAdMaterialUpload) UpLoadVideoAsync(ctx context.Context, aId string, fileId int) (taskId int64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		file := new(model.AdMaterialInfoRes)
		err = dao.AdMaterial.Ctx(ctx).Where(dao.AdMaterial.Columns().MaterialId, fileId).Scan(&file)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		// todo 已经存在medeaId 的话直接返回
		token, innerError := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aId)
		liberr.ErrIsNil(ctx, innerError, "获取accessToken失败")
		//FileImageAdV2ApiService
		result, innerErr := advertiser.GetToutiaoApiClient().FileUploadTaskCreateV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoModels.FileUploadTaskCreateV2Request{
			AccountId:   gconv.Int64(aId),
			AccountType: toutiaoModels.ADVERTISER_FileUploadTaskCreateV2AccountType,
			Filename:    file.MaterialName,
			VideoUrl:    file.FileUri,
		}).Do()
		liberr.ErrIsNil(ctx, innerErr, "上传视频失败！")
		if result.Data == nil {
			liberr.ErrIsNil(ctx, innerErr, "上传视频失败！")
		}
		if *result.Data.TaskId > 0 {
			taskId = *result.Data.TaskId
			return
		}
		// todo 等待优化
		//time.Sleep(2 * time.Second)
	})
	return
}

// GetUpLoadVideoAsync 获取一部上传视频结果
func (s *sAdMaterialUpload) GetUpLoadVideoAsync(ctx context.Context, aId string, taskId int64, file *model.AdMaterialInfoRes) (videoId string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		token, innerError := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aId)
		liberr.ErrIsNil(ctx, innerError, "获取accessToken失败")
		taskIds := []int64{taskId}
		// 获取视频结果
		result, innerErr := advertiser.GetToutiaoApiClient().FileVideoUploadTaskListV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoApi.ApiOpenApi2FileVideoUploadTaskListGetRequest{
			AccountId:   gconv.Int64(aId),
			AccountType: toutiaoModels.ADVERTISER_FileVideoUploadTaskListV2AccountType,
			TaskIds:     &taskIds,
		}).Do()
		liberr.ErrIsNil(ctx, innerErr)
		if result.Data == nil {
			liberr.ErrIsNil(ctx, innerErr, "上传视频失败！")
		}
		videoMaterialList := make([]do.AdMaterialUpload, 0)
		for _, item := range result.Data.List {
			// 上传成功
			if *item.Status == toutiaoModels.SUCCESS_FileVideoUploadTaskListV2DataListStatus {
				videoMaterialList = append(videoMaterialList, do.AdMaterialUpload{
					MediaType:    2,
					AdMaterialId: file.MaterialId,
					AdvertiserId: gconv.Int(aId),
					MediaId:      item.VideoInfo.VideoId,
					MaterialName: file.MaterialName,
					Size:         item.VideoInfo.Size,
					Width:        item.VideoInfo.Width,
					Height:       item.VideoInfo.Height,
					Url:          file.FileUri,
					MaterialId:   gconv.Int(item.VideoInfo.MaterialId),
					CreatedAt:    gtime.Now(),
				})
				videoId = *item.VideoInfo.VideoId
			}
		}

		// 存储到 ad_material_upload 表格
		if len(videoMaterialList) > 0 {
			_, innerErr = dao.AdMaterialUpload.Ctx(ctx).Save(videoMaterialList)
		}
		liberr.ErrIsNil(ctx, innerErr, "插入表格AdMaterialUpload失败！")
	})
	return
}

// UpLoadByUrl 仅供视频自带缩略图使用
func (s *sAdMaterialUpload) UpLoadByUrl(ctx context.Context, aId string, url string, fileName string) (meadId string, err error) {
	token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aId)
	liberr.ErrIsNil(ctx, err, "获取accessToken失败")
	//FileImageAdV2ApiService
	result, innerErr := advertiser.GetToutiaoApiClient().FileImageAdV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoApi.ApiOpenApi2FileImageAdPostRequest{
		AdvertiserId:   gconv.Int64(aId),
		Filename:       fileName,
		ImageFile:      nil,
		ImageSignature: "",
		ImageUrl:       url,
		IsAigc:         false,
		UploadType:     toutiaoModels.UPLOAD_BY_URL_FileImageAdV2UploadType,
	}).Do()
	liberr.ErrIsNil(ctx, innerErr, "上传图片失败！")
	if result.Data == nil {
		liberr.ErrIsNil(ctx, innerErr, "上传图片失败！")
		return "0", err
	}
	if len(*result.Data.Id) > 0 {
		//imgIds := strings.Split(*result.Data.Id, "/")
		//if len(imgIds) > 0 {
		//	return imgIds[len(imgIds)-1], nil
		//} else {
		//	return gconv.String(*result.Data.Id), nil
		//}
		meadId = *result.Data.Id
		return
	}
	return "", errors.New("上传图片返回为空！")
}

// UpLoadVideo 推送视频
func (s *sAdMaterialUpload) UpLoadVideo(ctx context.Context, aId string, file model.AdMaterialInfoRes) (meadId string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aId)
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		//FileImageAdV2ApiService
		result, innerErr := advertiser.GetToutiaoApiClient().FileVideoAdV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoApi.ApiOpenApi2FileVideoAdPostRequest{
			AdvertiserId: gconv.Int64(aId),
			Filename:     file.MaterialName,
			UploadType:   toutiaoModels.UPLOAD_BY_URL_FileVideoAdV2UploadType,
			VideoUrl:     file.FileUri,
		}).Do()
		liberr.ErrIsNil(ctx, innerErr, "上传视频失败！")
		if result.Data == nil {
			liberr.ErrIsNil(ctx, innerErr, "上传视频失败！")
		}
		// 存储到 ad_material_upload 表格
		_, innerErr = dao.AdMaterialUpload.Ctx(ctx).Insert(do.AdMaterialUpload{
			MediaType:    2,
			AdMaterialId: file.MaterialId,
			AdvertiserId: gconv.Int(aId),
			MediaId:      result.Data.VideoId,
			MaterialName: file.MaterialName,
			Size:         result.Data.Size,
			Width:        result.Data.Width,
			Height:       result.Data.Height,
			Url:          result.Data.VideoUrl,
			Format:       file.FileFormat,
			MaterialId:   gconv.Int(result.Data.MaterialId),
			Duration:     result.Data.Duration,
			CreatedAt:    gtime.Now(),
		})
		liberr.ErrIsNil(ctx, innerErr, "插入表格AdMaterialUpload失败！")
		meadId = *result.Data.VideoId
	})
	return
}

func imageToEntity(id string, req *toutiaoModels.FileImageGetV2ResponseDataListInner) *entity.AdMaterialUpload {
	// 将  req 转换成 entity
	return &entity.AdMaterialUpload{
		MediaType:    1,
		AdMaterialId: gconv.Int(req.MaterialId),
		AdvertiserId: id,
		MediaId:      *req.Id,
		Size:         *req.Size,
		Width:        int(*req.Width),
		Height:       int(*req.Height),
		Url:          *req.Url,
		Format:       *req.Format,
		Signature:    *req.Signature,
		MaterialId:   gconv.String(*req.MaterialId),
		CreatedAt:    gtime.NewFromStrFormat(*req.CreateTime, time.DateTime),
	}
}

func videoToEntity(id string, req *toutiaoModels.FileVideoGetV2ResponseDataListInner) *entity.AdMaterialUpload {
	// 将  req 转换成 entity
	return &entity.AdMaterialUpload{
		MediaType:    2,
		AdMaterialId: gconv.Int(req.MaterialId),
		AdvertiserId: id,
		MediaId:      *req.Id,
		Size:         *req.Size,
		Width:        int(*req.Width),
		Height:       int(*req.Height),
		Url:          *req.Url,
		Format:       *req.Format,
		Signature:    *req.Signature,
		MaterialId:   gconv.String(*req.MaterialId),
		Duration:     *req.Duration,
		CreatedAt:    gtime.NewFromStrFormat(*req.CreateTime, time.DateTime),
	}
}
