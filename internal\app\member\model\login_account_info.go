// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-01-24 14:14:38
// 生成路径: internal/app/system/model/login_account_info.go
// 生成人：gfast
// desc:用户账户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// LoginAccountInfoInfoRes is the golang structure for table login_account_info.
type LoginAccountInfoInfoRes struct {
	gmeta.Meta       `orm:"table:login_account_info"`
	Uid              string      `orm:"uid,primary" json:"uid" dc:"用户UID  唯一主键"`                      // 用户UID  唯一主键
	SaasId           string      `orm:"saas_id" json:"saasId" dc:"saasid，统一单个平台登录方式渠道"`               // saasid，统一单个平台登录方式渠道
	BindPhone        string      `orm:"bind_phone" json:"bindPhone" dc:"当前登录方式绑定的手机号"`                // 当前登录方式绑定的手机号
	SignMode         int         `orm:"sign_mode" json:"signMode" dc:"注册方式 ：1微信  2手机号  3抖音"`          // 注册方式 ：1微信  2手机号  3抖音
	ChannelType      int         `orm:"channel_type" json:"channelType" dc:"注册渠道： 1:APP 2:小程序 3:公众号"` // 注册渠道： 1:APP 2:小程序 3:公众号
	UnionId          string      `orm:"union_id" json:"unionId" dc:"第三方唯一id"`                         // 第三方唯一id
	OpenId           string      `orm:"open_id" json:"openId" dc:"登录唯一id =openid "`                   // 登录唯一id =openid
	Status           int         `orm:"status" json:"status" dc:"账号状态：0-锁定 1-正常"`                     // 账号状态：0-锁定 1-正常
	LastLoginTime    *gtime.Time `orm:"last_login_time" json:"lastLoginTime" dc:"上次登录时间"`             // 上次登录时间
	PlatCode         string      `orm:"plat_code" json:"platCode" dc:"平台编号 0表示主平台 其他表示其他小程序平台编号"`     // 平台编号 0表示主平台 其他表示其他小程序平台编号
	CreateTime       *gtime.Time `orm:"create_time" json:"createTime" dc:"创建时间"`                      // 创建时间
	UpdateTime       *gtime.Time `orm:"update_time" json:"updateTime" dc:"更新时间"`                      // 更新时间
	Account          string      `orm:"account" json:"account" dc:"0小程序  1501-1509头条  2501-2509百度"`   // 0小程序  1501-1509头条  2501-2509百度
	AppId            string      `orm:"app_id" json:"appId" dc:"小程序ID"`                               // 小程序ID
	DistributorId    int         `orm:"distributor_id" json:"distributorId" dc:"分销商ID"`               // 分销商ID
	CreateDate       string      `orm:"create_date" json:"createDate" dc:"创建日期"`                      // 创建日期
	PitcherId        int         `orm:"pitcher_id" json:"pitcherId" dc:"投手ID"`                        // 投手ID
	DeviceCode       string      `orm:"device_code" json:"deviceCode" dc:"设备码"`                       // 设备码
	SdkUserId        string      `orm:"sdk_user_id" json:"sdkUserId" dc:"SDK用户id"`                    // SDK用户id
	Path             string      `orm:"path" json:"path" dc:"小程序路径"`                                  // 小程序路径
	UserIp           string      `orm:"user_ip" json:"userIp" dc:"ip"`                                // ip
	IntervalHours    int         `orm:"interval_hours" json:"intervalHours" dc:"间隔上次登录时长 单位：小时"`      // 间隔上次登录时长 单位：小时
	UseRetain        int         `orm:"use_retain" json:"useRetain" dc:"是否使用挽留模板 0-否 1-是"`            // 是否使用挽留模板 0-否 1-是
	ParentId         int         `orm:"parent_id" json:"parentId" dc:"短剧父id"`                         // 短剧父id
	SecondCreateTime *gtime.Time `orm:"second_create_time" json:"secondCreateTime" dc:"新广告渠道进来的注册时间"` // 新广告渠道进来的注册时间
	SecondAccount    string      `orm:"second_account" json:"secondAccount" dc:"第二渠道号"`               // 第二渠道号
	LoginDeviceType  int         `orm:"login_device_type" json:"loginDeviceType" dc:"登录设备类型"`
}

type UserStatListRes struct {
	SaasId         string      `json:"saasId" dc:"saasid，统一单个平台登录方式渠道"`
	Channel        string      `json:"channel" dc:"0小程序  1501-1509头条  2501-2509百度"`
	AppId          string      `json:"appId" dc:"小程序ID"`
	AppName        string      `json:"appName" dc:"小程序Name"`
	CreateTime     *gtime.Time `json:"createTime" dc:"创建时间"`
	RecentlyPlayed string      `json:"recentlyPlayed" dc:"最近播放"`
	PlayTime       *gtime.Time `json:"playTime" dc:"最近播放时间"`
}

type LoginAccountInfoListRes struct {
	Uid              string      `json:"uid" dc:"用户UID  唯一主键"`
	SaasId           string      `json:"saasId" dc:"saasid，统一单个平台登录方式渠道"`
	BindPhone        string      `json:"bindPhone" dc:"当前登录方式绑定的手机号"`
	SignMode         int         `json:"signMode" dc:"注册方式 ：1微信  2手机号  3抖音"`
	ChannelType      int         `json:"channelType" dc:"注册渠道： 1:APP 2:小程序 3:公众号"`
	UnionId          string      `json:"unionId" dc:"第三方唯一id"`
	OpenId           string      `json:"openId" dc:"登录唯一id =openid "`
	Status           int         `json:"status" dc:"账号状态：0-锁定 1-正常"`
	LastLoginTime    *gtime.Time `json:"lastLoginTime" dc:"上次登录时间"`
	PlatCode         string      `json:"platCode" dc:"平台编号 0表示主平台 其他表示其他小程序平台编号"`
	CreateTime       *gtime.Time `json:"createTime" dc:"创建时间"`
	UpdateTime       *gtime.Time `json:"updateTime" dc:"更新时间"`
	Account          string      `json:"account" dc:"0小程序  1501-1509头条  2501-2509百度"`
	AppId            string      `json:"appId" dc:"小程序ID"`
	DistributorId    int         `json:"distributorId" dc:"分销商ID"`
	CreateDate       string      `json:"createDate" dc:"创建日期"`
	PitcherId        int         `json:"pitcherId" dc:"投手ID"`
	DeviceCode       string      `json:"deviceCode" dc:"设备码"`
	SdkUserId        string      `json:"sdkUserId" dc:"SDK用户id"`
	Path             string      `json:"path" dc:"小程序路径"`
	UserIp           string      `json:"userIp" dc:"ip"`
	IntervalHours    int         `json:"intervalHours" dc:"间隔上次登录时长 单位：小时"`
	UseRetain        int         `json:"useRetain" dc:"是否使用挽留模板 0-否 1-是"`
	ParentId         int         `json:"parentId" dc:"短剧父id"`
	SecondCreateTime *gtime.Time `json:"secondCreateTime" dc:"新广告渠道进来的注册时间"`
	SecondAccount    string      `json:"secondAccount" dc:"第二渠道号"`
}

type RefreshVideoOrderReq struct {
	SecAccount    string `p:"secAccount"  dc:"渠道号"`
	ChannelType   int    `p:"channelType" v:"required#注册渠道： 1:APP 2:小程序 3:公众号不能为空" dc:"注册渠道： 1:APP 2:小程序 3:公众号"`
	DistributorId uint64 `p:"distributorId"  dc:"分销商ID"`
}

type Path struct {
	DramaID      string `json:"dramaId"`
	WxExportID   string `json:"wxExportId"`
	WxTicket     string `json:"wxTicket"`
	WxFinderID   string `json:"wxFinderId"`
	WxSourceType string `json:"wxSourceType"`
	SrcAppid     string `json:"srcAppid"`
	SerialNo     int    `json:"serialNo"`
	PlayerID     string `json:"playerId"`
	SrcDramaID   string `json:"srcDramaId"`
	ExtParam     string `json:"extParam"`
	DramaAdType  int    `json:"dramaAdType"`
	AdParam      string `json:"adParam"`
}

type UserStatSearchReq struct {
	comModel.PageReq
	SaasId        string `p:"saasId" dc:"saasid，统一单个平台登录方式渠道"`
	AppId         string `p:"appId" dc:"小程序ID"`
	Channel       string `p:"channel" dc:"0小程序  1501-1509头条  2501-2509百度"` //0小程序  1501-1509头条  2501-2509百度
	PitcherId     int    `p:"pitcherId"  dc:"投手ID"`                        //投手ID
	DistributorId int    `p:"distributorId"   dc:"分销商ID"`                  //分销商ID
	//Pitcher       int         `p:"pitcher"   dc:"是否投手 0: 否 1: 是"`               //是否是投手
	StartTime string `p:"startTime"  dc:"开始时间"`
	EndTime   string `p:"endTime"  dc:"结束时间"`
}

type UserStatReq struct {
	AppId       string   `p:"appId" dc:"小程序ID"`
	ChannelType int      `p:"channelType" dc:"注册渠道： 1:APP 2:小程序 3:公众号"`
	Accounts    []string `p:"accounts" dc:"渠道id"`
	//Channel       string `p:"channel" dc:"0小程序  1501-1509头条  2501-2509百度"`
	PitcherId     int    `p:"pitcherId"  dc:"投手ID"`       //投手ID
	DistributorId int    `p:"distributorId"   dc:"分销商ID"` //分销商ID
	IsAdmin       bool   `p:"isAdmin"   dc:"是否是超级管理员"`
	Ids           []int  `p:"ids"   dc:"权限过滤之后的id"` //分销商ID
	StartTime     string `p:"startTime"  dc:"开始时间"`
	EndTime       string `p:"endTime"  dc:"结束时间"`
}

// LoginAccountInfoSearchReq 分页请求参数
type LoginAccountInfoSearchReq struct {
	comModel.PageReq
	Uid              string `p:"uid" dc:"用户UID  唯一主键"`                                                                                 //用户UID  唯一主键
	SaasId           string `p:"saasId" dc:"saasid，统一单个平台登录方式渠道"`                                                                      //saasid，统一单个平台登录方式渠道
	BindPhone        string `p:"bindPhone" dc:"当前登录方式绑定的手机号"`                                                                          //当前登录方式绑定的手机号
	SignMode         string `p:"signMode" v:"signMode@integer#注册方式 ：1微信  2手机号  3抖音需为整数" dc:"注册方式 ：1微信  2手机号  3抖音"`                     //注册方式 ：1微信  2手机号  3抖音
	ChannelType      string `p:"channelType" v:"channelType@integer#注册渠道： 1:APP 2:小程序 3:公众号需为整数" dc:"注册渠道： 1:APP 2:小程序 3:公众号"`         //注册渠道： 1:APP 2:小程序 3:公众号
	UnionId          string `p:"unionId" dc:"第三方唯一id"`                                                                                 //第三方唯一id
	OpenId           string `p:"openId" dc:"登录唯一id =openid "`                                                                          //登录唯一id =openid
	Status           string `p:"status" v:"status@integer#账号状态：0-锁定 1-正常需为整数" dc:"账号状态：0-锁定 1-正常"`                                     //账号状态：0-锁定 1-正常
	LastLoginTime    string `p:"lastLoginTime" v:"lastLoginTime@datetime#上次登录时间需为YYYY-MM-DD hh:mm:ss格式" dc:"上次登录时间"`                   //上次登录时间
	PlatCode         string `p:"platCode" dc:"平台编号 0表示主平台 其他表示其他小程序平台编号"`                                                              //平台编号 0表示主平台 其他表示其他小程序平台编号
	CreateTime       string `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                             //创建时间
	UpdateTime       string `p:"updateTime" v:"updateTime@datetime#更新时间需为YYYY-MM-DD hh:mm:ss格式" dc:"更新时间"`                             //更新时间
	Account          string `p:"account" dc:"0小程序  1501-1509头条  2501-2509百度"`                                                          //0小程序  1501-1509头条  2501-2509百度
	AppId            string `p:"appId" dc:"小程序ID"`                                                                                     //小程序ID
	DistributorId    string `p:"distributorId" v:"distributorId@integer#分销商ID需为整数" dc:"分销商ID"`                                         //分销商ID
	CreateDate       string `p:"createDate" dc:"创建日期"`                                                                                 //创建日期
	PitcherId        string `p:"pitcherId" v:"pitcherId@integer#投手ID需为整数" dc:"投手ID"`                                                   //投手ID
	DeviceCode       string `p:"deviceCode" dc:"设备码"`                                                                                  //设备码
	SdkUserId        string `p:"sdkUserId" dc:"SDK用户id"`                                                                               //SDK用户id
	Path             string `p:"path" dc:"小程序路径"`                                                                                      //小程序路径
	UserIp           string `p:"userIp" dc:"ip"`                                                                                       //ip
	IntervalHours    string `p:"intervalHours" v:"intervalHours@integer#间隔上次登录时长 单位：小时需为整数" dc:"间隔上次登录时长 单位：小时"`                       //间隔上次登录时长 单位：小时
	UseRetain        string `p:"useRetain" v:"useRetain@integer#是否使用挽留模板 0-否 1-是需为整数" dc:"是否使用挽留模板 0-否 1-是"`                           //是否使用挽留模板 0-否 1-是
	ParentId         string `p:"parentId" v:"parentId@integer#短剧父id需为整数" dc:"短剧父id"`                                                   //短剧父id
	SecondCreateTime string `p:"secondCreateTime" v:"secondCreateTime@datetime#新广告渠道进来的注册时间需为YYYY-MM-DD hh:mm:ss格式" dc:"新广告渠道进来的注册时间"` //新广告渠道进来的注册时间
	SecondAccount    string `p:"secondAccount" dc:"第二渠道号"`                                                                             //第二渠道号
}

// LoginAccountInfoSearchRes 列表返回结果
type LoginAccountInfoSearchRes struct {
	comModel.ListRes
	List []*LoginAccountInfoListRes `json:"list"`
}

type UserStatSearchRes struct {
	comModel.ListRes
	List []*UserStatListRes `json:"list"`
}

// LoginAccountInfoAddReq 添加操作请求参数
type LoginAccountInfoAddReq struct {
	Uid              string      `p:"uid" v:"required#主键ID不能为空" dc:"用户UID  唯一主键"`
	SaasId           string      `p:"saasId"  dc:"saasid，统一单个平台登录方式渠道"`
	BindPhone        string      `p:"bindPhone"  dc:"当前登录方式绑定的手机号"`
	SignMode         int         `p:"signMode" v:"required#注册方式 ：1微信  2手机号  3抖音不能为空" dc:"注册方式 ：1微信  2手机号  3抖音"`
	ChannelType      int         `p:"channelType" v:"required#注册渠道： 1:APP 2:小程序 3:公众号不能为空" dc:"注册渠道： 1:APP 2:小程序 3:公众号"`
	UnionId          string      `p:"unionId"  dc:"第三方唯一id"`
	OpenId           string      `p:"openId"  dc:"登录唯一id =openid "`
	Status           int         `p:"status" v:"required#账号状态：0-锁定 1-正常不能为空" dc:"账号状态：0-锁定 1-正常"`
	LastLoginTime    *gtime.Time `p:"lastLoginTime"  dc:"上次登录时间"`
	PlatCode         string      `p:"platCode"  dc:"平台编号 0表示主平台 其他表示其他小程序平台编号"`
	CreateTime       *gtime.Time `p:"createTime"  dc:"创建时间"`
	UpdateTime       *gtime.Time `p:"updateTime"  dc:"更新时间"`
	Account          string      `p:"account"  dc:"0小程序  1501-1509头条  2501-2509百度"`
	AppId            string      `p:"appId"  dc:"小程序ID"`
	DistributorId    int         `p:"distributorId"  dc:"分销商ID"`
	CreateDate       string      `p:"createDate"  dc:"创建日期"`
	PitcherId        int         `p:"pitcherId"  dc:"投手ID"`
	DeviceCode       string      `p:"deviceCode"  dc:"设备码"`
	SdkUserId        string      `p:"sdkUserId"  dc:"SDK用户id"`
	Path             string      `p:"path"  dc:"小程序路径"`
	UserIp           string      `p:"userIp"  dc:"ip"`
	IntervalHours    int         `p:"intervalHours"  dc:"间隔上次登录时长 单位：小时"`
	UseRetain        int         `p:"useRetain"  dc:"是否使用挽留模板 0-否 1-是"`
	ParentId         int         `p:"parentId"  dc:"短剧父id"`
	SecondCreateTime *gtime.Time `p:"secondCreateTime"  dc:"新广告渠道进来的注册时间"`
	SecondAccount    string      `p:"secondAccount"  dc:"第二渠道号"`
}

// LoginAccountInfoEditReq 修改操作请求参数
type LoginAccountInfoEditReq struct {
	Uid              string      `p:"uid" v:"required#主键ID不能为空" dc:"用户UID  唯一主键"`
	SaasId           string      `p:"saasId"  dc:"saasid，统一单个平台登录方式渠道"`
	BindPhone        string      `p:"bindPhone"  dc:"当前登录方式绑定的手机号"`
	SignMode         int         `p:"signMode" v:"required#注册方式 ：1微信  2手机号  3抖音不能为空" dc:"注册方式 ：1微信  2手机号  3抖音"`
	ChannelType      int         `p:"channelType" v:"required#注册渠道： 1:APP 2:小程序 3:公众号不能为空" dc:"注册渠道： 1:APP 2:小程序 3:公众号"`
	UnionId          string      `p:"unionId"  dc:"第三方唯一id"`
	OpenId           string      `p:"openId"  dc:"登录唯一id =openid "`
	Status           int         `p:"status" v:"required#账号状态：0-锁定 1-正常不能为空" dc:"账号状态：0-锁定 1-正常"`
	LastLoginTime    *gtime.Time `p:"lastLoginTime"  dc:"上次登录时间"`
	PlatCode         string      `p:"platCode"  dc:"平台编号 0表示主平台 其他表示其他小程序平台编号"`
	CreateTime       *gtime.Time `p:"createTime"  dc:"创建时间"`
	UpdateTime       *gtime.Time `p:"updateTime"  dc:"更新时间"`
	Account          string      `p:"account"  dc:"0小程序  1501-1509头条  2501-2509百度"`
	AppId            string      `p:"appId"  dc:"小程序ID"`
	DistributorId    int         `p:"distributorId"  dc:"分销商ID"`
	CreateDate       string      `p:"createDate"  dc:"创建日期"`
	PitcherId        int         `p:"pitcherId"  dc:"投手ID"`
	DeviceCode       string      `p:"deviceCode"  dc:"设备码"`
	SdkUserId        string      `p:"sdkUserId"  dc:"SDK用户id"`
	Path             string      `p:"path"  dc:"小程序路径"`
	UserIp           string      `p:"userIp"  dc:"ip"`
	IntervalHours    int         `p:"intervalHours"  dc:"间隔上次登录时长 单位：小时"`
	UseRetain        int         `p:"useRetain"  dc:"是否使用挽留模板 0-否 1-是"`
	ParentId         int         `p:"parentId"  dc:"短剧父id"`
	SecondCreateTime *gtime.Time `p:"secondCreateTime"  dc:"新广告渠道进来的注册时间"`
	SecondAccount    string      `p:"secondAccount"  dc:"第二渠道号"`
}
type UserListSearchReq struct {
	comModel.PageReq
	SaasId       string   `p:"saasId" dc:"saasid"`
	AppId        string   `p:"appId" dc:"小程序ID"`
	AppIds       []string `p:"appIds" dc:"小程序IDs"`
	VipFlag      string   `p:"vipFlag" dc:"vip状态，0位非vip，1位vip"`
	ChannelCode  string   `p:"channelCode"  dc:"渠道"`  //渠道
	ChannelCodes []string `p:"channelCodes"  dc:"渠道"` //渠道
	Status       string   `p:"status"   dc:"状态"`      //分销商ID
	StartTime    string   `p:"startTime"  dc:"开始时间"`
	EndTime      string   `p:"endTime"  dc:"结束时间"`
}
type UserListSearchRes struct {
	comModel.ListRes
	List []*UserListRes `json:"list"`
}
type UserListRes struct {
	SaasId          string      `json:"saasId" dc:"saasid，统一单个平台登录方式渠道"`
	NickName        string      `json:"nickName" dc:"用户昵称"`
	VipFlag         int         `json:"vipFlag" dc:"vip标志，0为否，1为是"`
	AppName         string      `json:"appName" dc:"小程序名称"`
	LastViewTime    *gtime.Time `json:"lastViewTime"  dc:"最近看剧时间"`
	LastViewTitle   string      `json:"lastViewTitle"  dc:"最近看剧题目"`
	LastViewNum     int         `json:"lastViewNum"  dc:"最近看剧集数"`
	ChannelCode     string      `json:"channelCode" dc:"渠道"`
	LoginDeviceType string      `json:"loginDeviceType" dc:"登录设备信息，1位安卓，2位iphone"`
	UserIp          string      `json:"userIp" dc:"ip信息"`
	Balance         string      `json:"balance" dc:"余额"`
	PointsBalance   string      `json:"pointsBalance" dc:"积分余额"`
	CreateTime      *gtime.Time `json:"createTime"  dc:"创建时间"`
	LastLoginTime   *gtime.Time `json:"lastLoginTime"  dc:"上次登录时间"`
	Status          int         `json:"status" dc:"账号状态：0-锁定 1-正常"`
	LockSaasId      string      `json:"lockSaasId" dc:"lock表中saasId"`
	VipEndTime      *gtime.Time `json:"vipEndTime"  dc:"vip到期时间"`
	Account         string      `json:"account" dc:"第一渠道"`
	SecondAccount   string      `json:"secondAccount" dc:"第二渠道"`
	LockEndTime     *gtime.Time `json:"lockEndTime"  dc:"锁定时间"`
}
type LoginLockInfoRes struct {
	SaasId      string      ` json:"saasId" dc:"saasid，统一单个平台登录方式渠道"` // saasid，统一单个平台登录方式渠道
	Status      int         ` json:"status" dc:"账号状态：0-锁定 1-正常"`      // 账号状态：0-锁定 1-正常
	LockEndTime *gtime.Time ` json:"lockEndTime" dc:"解锁时间,若为空，则永久锁定"` // 上次登录时间
	Reason      string      ` json:"reason" dc:"原因"`
}

type AppIdAccountActivity struct {
	AppId         string ` json:"appId" dc:"appId"`       // appId
	ActivityCount int    ` json:"ActivityCount" dc:"激活数"` // 账号状态：0-锁定 1-正常
	AppName       string ` json:"appName" dc:"应用名称"`      // 账号状态：0-锁定 1-正常
}

type RefreshUserAdCacheReq struct {
	SaasId    string `p:"saasId" dc:"saasid"`
	Account   string `p:"account"  dc:"渠道号"`
	StartTime string `p:"startTime"  dc:"开始时间"`
	EndTime   string `p:"endTime"  dc:"结束时间"`
}

type DelOldUserAdCacheReq struct {
	SaasId        string `p:"saasId" dc:"saasid"`
	StartTime     string `p:"startTime"  dc:"注册开始时间"`
	EndTime       string `p:"endTime"  dc:"注册结束时间"`
	LastLoginTime string `p:"lastLoginTime"  dc:"上次登录时间"`
}

// UserAdInfo 巨量广告 channel=4
type UserAdInfo struct {
	Account       string `json:"account"`
	AdvId         string `json:"adv_id"`
	SerialNo      string `json:"serialNo"`
	PromotionId   string `json:"promotion_id"`
	Mid3          string `json:"mid3"`
	AwemeAuthorId string `json:"aweme_author_id"`
	Channel       string `json:"channel"`
	Clickid       string `json:"clickid"`
	Mid1          string `json:"mid1"`
	DramaId       string `json:"dramaId"`
	ClueId        string `json:"clue_id"`
	Mid4          string `json:"mid4"`
	ProjectId     string `json:"project_id"`
	ParentId      string `json:"parentId"`
	ReqId         string `json:"req_id"`
	SubId         string `json:"subId"`
	ClueToken     string `json:"clue_token"`
	AdvertiserId  string `json:"advertiser_id"`
	ItemId        string `json:"item_id"`
	Mid2          string `json:"mid2"`
	RoomId        string `json:"room_id"`
	Mid5          string `json:"mid5"`
}

// DyAdInfo 巨量广告 channel=4
type DyAdInfo struct {
	Channel             int         `json:"channel"`
	AdvertiserId        string      `json:"advertiserId"`
	SaasId              interface{} `json:"saasId"`
	Account             string      `json:"account"`
	ParentId            int         `json:"parentId"`
	PromotionId         string      `json:"promotionId"`
	ClueToken           string      `json:"clueToken"`
	AdId                string      `json:"adId"`
	CreateId            string      `json:"createId"`
	ReqId               string      `json:"reqId"`
	ProjectId           string      `json:"projectId"`
	IsBackAckActivation int         `json:"isBackAckActivation"`
}

type AdvertisingInfo struct {
	AId              string `json:"aId,omitempty"`
	AdId             string `json:"adId,omitempty"`
	Ip               string `json:"ip,omitempty"`
	Ua               string `json:"ua,omitempty"`
	Callback         string `json:"callback,omitempty"`
	Model            string `json:"model,omitempty"`
	ClickId          string `json:"clickId,omitempty"`
	BdVid            string `json:"bdVid,omitempty"`
	ExtInfo          string `json:"extInfo,omitempty"`
	Channel          int32  `json:"channel,omitempty"`
	Account          string `json:"account,omitempty"`
	CreativeId       string `json:"creativeId,omitempty"`
	RequestId        string `json:"requestId,omitempty"`
	AdvertiserId     string `json:"advertiserId,omitempty"`
	SourceId         string `json:"sourceId,omitempty"`
	ItemId           string `json:"itemId,omitempty"`
	ProjectId        string `json:"projectId,omitempty"`
	PromotionId      string `json:"promotionId,omitempty"`
	ClickTime        string `json:"clickTime,omitempty"`
	PageUrl          string `json:"pageUrl,omitempty"`
	WechatOpenId     string `json:"wechatOpenId,omitempty"`
	ParentId         int32  `json:"parentId,omitempty"`
	SubId            int32  `json:"subId,omitempty"`
	ImpressionId     string `json:"impressionId,omitempty"`
	WeiXinAdInfo     string `json:"weiXinAdInfo,omitempty"`
	Imp              string `json:"imp,omitempty" dc:"微博imp(用户进入小程序唯一编码）"`
	TsId             string `json:"tsId,omitempty" dc:"微博新的广告创意id(旧广告创意id为creative_id，后期会下掉）"`
	WeiboMonitorFlag bool   `json:"weiboMonitorFlag,omitempty" dc:"是否为微博监测"`
	Trackid          string `json:"trackid,omitempty" dc:"B站tract_id"`
	//campaign_id 计划id xhs 跳微信
	CampaignId string `json:"campaignId,omitempty"`
	// unit_id 单元id
	UnitId string `json:"unitId,omitempty"`
	//creativity_id 创意id
	CreativityId string `json:"creativityId,omitempty"`
	Imei         string `json:"imei,omitempty"`
	Idfa         string `json:"idfa,omitempty"`
	Mac          string `json:"mac,omitempty"`
	TrackId      string `json:"trackId,omitempty"`
	Oaid         string `json:"oaid,omitempty"`
	Caid         string `json:"caid,omitempty"`
}
