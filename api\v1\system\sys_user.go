package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model/entity"
)

type UserMenusReq struct {
	g.Meta `path:"/user/getUserMenus" tags:"用户管理" method:"get" summary:"获取用户菜单"`
	commonApi.Author
}

type UserMenusRes struct {
	g.Meta      `mime:"application/json"`
	MenuList    []*model.UserMenus `json:"menuList"`
	Permissions []string           `json:"permissions"`
}

type GetUserInfoReq struct {
	g.Meta `path:"/get/info" tags:"登录" method:"get" summary:"获取用户信息"`
	commonApi.Author
}
type GetUserInfoRes struct {
	g.Meta   `mime:"application/json"`
	UserInfo *model.LoginUserRes `json:"userInfo"`
}

// UserSearchReq 用户搜索请求参数
type UserSearchReq struct {
	g.Meta         `path:"/user/list" tags:"用户管理" method:"post" summary:"用户列表"`
	DeptId         string   `p:"deptId"` //部门id
	RoleId         uint     `p:"roleId"`
	RoleIds        []uint   `p:"roleIds"`
	Mobile         string   `p:"mobile"`
	Status         string   `p:"status"`
	KeyWords       string   `p:"keyWords"`
	UserId         uint64   `p:"userId"`
	DistributorIds []uint64 `p:"userIds" dc:"分销商ID"`
	DeptIds        []int    `p:"deptIds"   dc:"部门ids"`
	commonApi.PageReq
	commonApi.Author
}

type GetDesignerSupervisorReq struct {
	g.Meta `path:"/get/designer/supervisor" tags:"用户管理" method:"get" summary:"获取设计师or优化师主管"`
	commonApi.Author
	KeyWords string `p:"keyWords"`
}

type GetDesignerSupervisorRes struct {
	g.Meta             `mime:"application/json"`
	DesignerSupervisor []*entity.SysUser `json:"designerSupervisor"`
}

// AdUserSearchReq 用户搜索请求参数
type AdUserSearchReq struct {
	g.Meta         `path:"/ad/user/list" tags:"用户管理" method:"post" summary:"Ad用户列表"`
	DeptId         string   `p:"deptId"` //部门id
	RoleId         uint     `p:"roleId"`
	RoleIds        []uint   `p:"roleIds"`
	Mobile         string   `p:"mobile"`
	Status         string   `p:"status"`
	KeyWords       string   `p:"keyWords"`
	UserId         uint64   `p:"userId"`
	DistributorIds []uint64 `p:"userIds" dc:"分销商ID"`
	DeptIds        []int    `p:"deptIds"   dc:"部门ids"`
	commonApi.PageReq
	commonApi.Author
}

type UserSearchRes struct {
	g.Meta   `mime:"application/json"`
	UserList []*model.SysUserRoleDeptRes `json:"userList"`
	commonApi.ListRes
}

type UserGetParamsReq struct {
	g.Meta `path:"/user/params" tags:"用户管理" method:"get" summary:"用户维护参数获取"`
}

type GetSuperAdminIdsReq struct {
	g.Meta `path:"/user/getSuperAdmin/ids" tags:"用户管理" method:"get" summary:"用户超级管理员用户ids"`
	commonApi.Author
}

type GetSuperAdminIdsRes struct {
	g.Meta `mime:"application/json"`
	Ids    []int `json:"ids"`
}

type UserGetParamsRes struct {
	g.Meta     `mime:"application/json"`
	RoleList   []*entity.SysRole `json:"roleList"`
	Posts      []*entity.SysPost `json:"posts"`
	RoleAccess []uint            `json:"roleAccess"`
}

// SetUserReq 添加修改用户公用请求字段
type SetUserReq struct {
	DeptId   uint64  `p:"deptId" v:"required#用户部门不能为空"` //所属部门
	Email    string  `p:"email" `                       //邮箱
	NickName string  `p:"nickName" v:"required#用户昵称不能为空"`
	Mobile   string  `p:"mobile" `
	PostIds  []int64 `p:"postIds"`
	Remark   string  `p:"remark"`
	RoleIds  []uint  `json:"roleIds" p:"roleIds"`
	Sex      int     `p:"sex"`
	Status   uint    `p:"status"`
	IsAdmin  int     `p:"isAdmin"` // 是否后台管理员 1 是  0   否
}

// UserAddReq 添加用户参数
type UserAddReq struct {
	g.Meta `path:"/user/add" tags:"用户管理" method:"post" summary:"添加用户"`
	*SetUserReq
	UserName         string `p:"userName" v:"required#用户账号不能为空"`
	Password         string `p:"password" v:"required|password#密码不能为空|密码以字母开头，只能包含字母、数字和下划线，长度在6~18之间"`
	UserSalt         string
	AppId            string                           `orm:"app_id" json:"appId"`                                  // 小程序ID
	Percentage       float64                          `orm:"percentage" json:"percentage"`                         // 分成比例
	VideoIds         string                           `orm:"video_ids" json:"videoIds"`                            // 可选剧集
	DeductOrderScale float64                          `orm:"deduct_order_scale" json:"deductOrderScale"`           // 扣除订单比例
	HaveNewDrama     int                              `orm:"have_new_drama" json:"haveNewDrama" dc:"0 , 1 是否有新短剧"` //  0 , 1 添加新剧是否推送给当前用户
	ShareRatio       *ShareRatio                      `json:"shareRatio" dc:"分成比例"`
	AdUnitId         string                           `p:"adUnitId" dc:"微信广告位ID"`
	WxAdConfigs      []*model.SysUserWxAdConfigAddReq `p:"wxAdConfigs" dc:"微信广告位ID配置"`
}

type ShareRatio struct {
	WxIapAndroid float64 `json:"wxIapAndroid" dc:"微小IAP安卓分成比例"`
	WxIapIos     float64 `json:"wxIapIos" dc:"微小IAP IOS分成比例"`
	WxIaa        float64 `json:"wxIaa" dc:"微小IAA分成比例"`
	DyIapAndroid float64 `json:"dyIapAndroid" dc:"抖小IAP安卓分成比例"`
	DyIapIos     float64 `json:"dyIapIos" dc:"抖小IAP IOS分成比例"`
	DyIaa        float64 `json:"dyIaa" dc:"抖小IAA分成比例"`
}

type OldUserAdd struct {
	*SetUserReq
	Id               int    `p:"id"  `
	UserName         string `p:"userName" v:"required#用户账号不能为空"`
	Password         string `p:"password" v:"required|password#密码不能为空|密码以字母开头，只能包含字母、数字和下划线，长度在6~18之间"`
	Avatar           string `p:"avatar"` //头像
	UserSalt         string
	AppId            string  // 小程序ID
	Percentage       float64 // 分成比例
	VideoIds         string  // 可选剧集
	DeductOrderScale float64 // 扣除订单比例
}

type UserAddRes struct {
}

// UserEditReq 修改用户参数
type UserEditReq struct {
	g.Meta `path:"/user/edit" tags:"用户管理" method:"put" summary:"修改用户"`
	*SetUserReq
	UserId           int64       `p:"userId" v:"required#用户id不能为空"`
	AppId            string      `orm:"app_id" json:"appId"`                                  // 小程序ID
	Percentage       float64     `orm:"percentage" json:"percentage"`                         // 分成比例
	VideoIds         string      `orm:"video_ids" json:"videoIds"`                            // 可选剧集
	DeductOrderScale float64     `orm:"deduct_order_scale" json:"deductOrderScale"`           // 扣除订单比例
	HaveNewDrama     int         `orm:"have_new_drama" json:"haveNewDrama" dc:"0 , 1 是否有新短剧"` //  0 , 1 添加新剧是否推送给当前用户
	ShareRatio       *ShareRatio `json:"shareRatio" dc:"分成比例"`
	AdUnitId         string      `p:"adUnitId" dc:"微信广告位ID"`
}

type UserEditRes struct {
}

// UserEditRoleReq 修改用户角色参数
type UserEditRoleReq struct {
	g.Meta `path:"/user/edit/role" tags:"用户管理" method:"put" summary:"修改用户角色"`
	commonApi.Author
	UserId  int64  `p:"userId" v:"required#用户id不能为空"`
	RoleIds []uint `p:"roleIds" v:"required#用户角色ID不能为空"`
}

type UserGetEditReq struct {
	g.Meta `path:"/user/getEdit" tags:"用户管理" method:"get" summary:"获取用户信息"`
	Id     uint64 `p:"id"`
}

type UserGetEditRes struct {
	g.Meta           `mime:"application/json"`
	User             *entity.SysUser                   `json:"user"`
	CheckedRoleIds   []uint                            `json:"checkedRoleIds"`
	CheckedPosts     []int64                           `json:"checkedPosts"`
	AppId            string                            `orm:"app_id" json:"appId"`                                  // 小程序ID
	Percentage       float64                           `orm:"percentage" json:"percentage"`                         // 分成比例
	VideoIds         string                            `orm:"video_ids" json:"videoIds"`                            // 可选剧集
	DeductOrderScale float64                           `orm:"deduct_order_scale" json:"deductOrderScale"`           // 扣除订单比例
	HaveNewDrama     int                               `orm:"have_new_drama" json:"haveNewDrama" dc:"0 , 1 是否有新短剧"` //  0 , 1 添加新剧是否推送给当前用户
	ShareRatio       *ShareRatio                       `json:"shareRatio" dc:"分成比例"`
	AdUnitId         string                            `json:"adUnitId" dc:"微信广告位ID"`
	WxAdConfigs      []*model.SysUserWxAdConfigInfoRes `json:"wxAdConfigs" dc:"微信广告位ID配置"`
}

// UserResetPwdReq 重置用户密码状态参数
type UserResetPwdReq struct {
	g.Meta   `path:"/user/resetPwd" tags:"用户管理" method:"put" summary:"重置用户密码"`
	Id       uint64 `p:"userId" v:"required#用户id不能为空"`
	Password string `p:"password" v:"required|password#密码不能为空|密码以字母开头，只能包含字母、数字和下划线，长度在6~18之间"`
}

type UserResetPwdRes struct {
}

// UserStatusReq 设置用户状态参数
type UserStatusReq struct {
	g.Meta     `path:"/user/setStatus" tags:"用户管理" method:"put" summary:"设置用户状态"`
	Id         uint64 `p:"userId" v:"required#用户id不能为空"`
	UserStatus uint   `p:"status" v:"required#用户状态不能为空"`
}

type UserStatusRes struct {
}

type UserDeleteReq struct {
	g.Meta `path:"/user/delete" tags:"用户管理" method:"post" summary:"删除用户"`
	Ids    []int `p:"ids"  v:"required#ids不能为空"`
}

type UserDeleteRes struct {
}

type AsyncUserReq struct {
	g.Meta `path:"/user/async" tags:"用户管理" method:"post" summary:"批量从老系统中同步用户到新系统"`
	Ids    []int `p:"ids"  v:"required#ids不能为空"`
	DeptId int   `p:"deptId"` //部门的根目录  这里默认是深圳公司
}

type AsyncUserRes struct {
	Success bool   `p:"success"`
	Message string `p:"message"`
}

// VideoAuthUserListReq 获取短剧授权用户列表请求参数
type VideoAuthUserListReq struct {
	g.Meta `path:"/user/video/auth/list" tags:"短剧推广" method:"post" summary:"获取短剧授权用户列表"`
	commonApi.Author
	*model.VideoAuthUserListReq
}

// VideoAuthUserListRes 获取短剧授权用户列表响应结果
type VideoAuthUserListRes struct {
	g.Meta `mime:"application/json"`
	*model.VideoAuthUserListRes
}

// VideoAuthReq 短剧授权用户请求参数
type VideoAuthReq struct {
	g.Meta `path:"/user/video/auth" tags:"短剧推广" method:"post" summary:"短剧授权用户"`
	commonApi.Author
	*model.VideoAuthReq
}

// VideoAuthRes 短剧授权用户响应结果
type VideoAuthRes struct {
	commonApi.EmptyRes
}
