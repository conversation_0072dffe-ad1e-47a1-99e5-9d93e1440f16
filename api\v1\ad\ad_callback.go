// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-13 10:42:38
// 生成路径: api/v1/ad/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type KsAdCallBackReq struct {
	g.Meta `path:"/ks/callback" tags:"ks广告授权回调" method:"get" summary:"ks回调"`
	//commonApi.Author
	*model.KsAdCallBackReq
}

type KsAdCallBackRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdCallBackRes
}

type DzCallBackReq struct {
	g.Meta `path:"/dz/callback" tags:"广告授权回调" method:"post" summary:"dz回调"`
	//commonApi.Author
	*model.DzAdCallBackReq
}

type DzCallBackRes struct {
	commonApi.EmptyRes
}

// OceanEngineCallbackReq 分页请求参数
type OceanEngineCallbackReq struct {
	g.Meta `path:"/oceanengine" tags:"广告授权回调" method:"get" summary:"巨量广告授权回调"`
	commonApi.Author
	*model.OceanEngineCallbackReq
}

// OceanEngineCallbackRes 列表返回结果
type OceanEngineCallbackRes struct {
	g.Meta `mime:"application/json"`
	*model.OceanEngineCallbackRes
}

type OceanEngineXTCallbackReq struct {
	g.Meta `path:"/xt" tags:"广告授权回调" method:"get" summary:"星图订阅回调"`
	commonApi.Author
	*model.OceanEngineXTCallbackReq
}

// OceanEngineXTCallbackRes 列表返回结果
type OceanEngineXTCallbackRes struct {
	g.Meta `mime:"application/json"`
	*model.OceanEngineCallbackRes
}

type OceanEngineSubscribeValidReq struct {
	g.Meta `path:"/oceanengine/subscribe" tags:"广告授权回调" method:"get" summary:"巨量订阅回调验证"`
	commonApi.Author
	*model.OceanEngineSubscribeValidReq
}

type OceanEngineSubscribeValidRes struct {
	commonApi.EmptyRes
	*model.OceanEngineSubscribeValidRes
}

type OceanEngineSubscribeReq struct {
	g.Meta `path:"/oceanengine/subscribe" tags:"广告授权回调" method:"post" summary:"巨量订阅回调"`
	commonApi.Author
}

type OceanEngineSubscribeRes struct {
	commonApi.EmptyRes
	*model.OceanEngineSubscribeValidRes
}
