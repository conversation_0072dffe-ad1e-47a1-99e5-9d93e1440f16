/*
* @desc:工具
* @company:云南奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON>
* @Date:   2022/3/4 22:16
 */

package libUtils

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gcharset"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"math"
	"net"
	"net/http"
	"net/url"
	"os"
	"path"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

// EncryptPassword 密码加密
func EncryptPassword(password, salt string) string {
	return gmd5.MustEncryptString(gmd5.MustEncryptString(password) + gmd5.MustEncryptString(salt))
}

// GetDomain 获取当前请求接口域名
func GetDomain(ctx context.Context) string {
	r := g.RequestFromCtx(ctx)
	pathInfo, err := gurl.ParseURL(r.GetUrl(), -1)
	if err != nil {
		g.Log().Error(ctx, err)
		return ""
	}
	return fmt.Sprintf("%s://%s:%s/", pathInfo["scheme"], pathInfo["host"], pathInfo["port"])
}

// GetClientIp 获取客户端IP
func GetClientIp(ctx context.Context) string {
	return g.RequestFromCtx(ctx).GetClientIp()
}

// GetUserAgent 获取user-agent
func GetUserAgent(ctx context.Context) string {
	return ghttp.RequestFromCtx(ctx).Header.Get("User-Agent")
}

// GetLocalIP 服务端ip
func GetLocalIP() (ip string, err error) {
	var addrs []net.Addr
	addrs, err = net.InterfaceAddrs()
	if err != nil {
		return
	}
	for _, addr := range addrs {
		ipAddr, ok := addr.(*net.IPNet)
		if !ok {
			continue
		}
		if ipAddr.IP.IsLoopback() {
			continue
		}
		if !ipAddr.IP.IsGlobalUnicast() {
			continue
		}
		return ipAddr.IP.String(), nil
	}
	return
}

// GetCityByIp 获取ip所属城市
func GetCityByIp(ip string) string {
	if ip == "" {
		return ""
	}
	if ip == "::1" || ip == "127.0.0.1" {
		return "内网IP"
	}
	url := "http://whois.pconline.com.cn/ipJson.jsp?json=true&ip=" + ip
	bytes := g.Client().GetBytes(context.TODO(), url)
	src := string(bytes)
	srcCharset := "GBK"
	tmp, _ := gcharset.ToUTF8(srcCharset, src)
	json, err := gjson.DecodeToJson(tmp)
	if err != nil {
		return ""
	}
	if json.Get("code").Int() == 0 {
		city := fmt.Sprintf("%s %s", json.Get("pro").String(), json.Get("city").String())
		return city
	} else {
		return ""
	}
}

// 提取 URL 中的 app_id
func ExtractAppID(text string) (string, error) {
	lines := strings.Split(text, "\n")
	for _, line := range lines {
		if strings.Contains(line, "启动参数:") {
			// 获取启动参数部分
			parts := strings.SplitN(line, "启动参数:", 2)
			if len(parts) < 2 {
				continue
			}
			queryStr := strings.TrimSpace(parts[1])

			// 解析参数
			values, err := url.ParseQuery(queryStr)
			if err != nil {
				return "", fmt.Errorf("解析失败: %v", err)
			}
			appID := values.Get("app_id")
			if appID == "" {
				return "", fmt.Errorf("app_id 不存在")
			}
			return appID, nil
		}
	}
	return "", fmt.Errorf("未找到启动参数行")
}

// ExtractKey 提取 url 参数
func ExtractKey(paramStr, key string) string {
	if !strings.HasPrefix(paramStr, "?") {
		paramStr = "?" + paramStr
	}

	u, err := url.Parse(paramStr)
	if err != nil {
		return ""
	}

	values, err := url.ParseQuery(u.RawQuery)
	if err != nil {
		return ""
	}

	appID := values.Get(key)
	if appID == "" {
		return ""
	}

	return appID
}

// ExtractSrcAppID 从字符串中提取srcAppid字段的值
func ExtractSrcAppID(text string) (string, error) {
	// 使用正则表达式匹配srcAppid=后面的值，直到遇到&或字符串结束
	re := regexp.MustCompile(`srcAppid=([^&]+)`)
	matches := re.FindStringSubmatch(text)

	if len(matches) < 2 {
		return "", fmt.Errorf("未找到srcAppid字段")
	}

	return matches[1], nil
}

// 写入文件
func WriteToFile(fileName string, content string) error {
	f, err := os.OpenFile(fileName, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0644)
	if err != nil {
		return err
	}
	n, _ := f.Seek(0, os.SEEK_END)
	_, err = f.WriteAt([]byte(content), n)
	defer f.Close()
	return err
}

// 文件或文件夹是否存在
func FileIsExisted(filename string) bool {
	existed := true
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		existed = false
	}
	return existed
}

// 解析路径获取文件名称及后缀
func ParseFilePath(pathStr string) (fileName string, fileType string) {
	fileNameWithSuffix := path.Base(pathStr)
	fileType = path.Ext(fileNameWithSuffix)
	fileName = strings.TrimSuffix(fileNameWithSuffix, fileType)
	return
}

// IsNotExistMkDir 检查文件夹是否存在
// 如果不存在则新建文件夹
func IsNotExistMkDir(src string) error {
	if exist := !FileIsExisted(src); exist == false {
		if err := MkDir(src); err != nil {
			return err
		}
	}

	return nil
}

// MkDir 新建文件夹
func MkDir(src string) error {
	err := os.MkdirAll(src, os.ModePerm)
	if err != nil {
		return err
	}

	return nil
}

// 获取文件后缀
func GetExt(fileName string) string {
	return path.Ext(fileName)
}

// GetType 获取文件类型
func GetType(p string) (result string, err error) {
	file, err := os.Open(p)
	if err != nil {
		g.Log().Error(context.TODO(), err)
		return
	}
	buff := make([]byte, 512)

	_, err = file.Read(buff)

	if err != nil {
		g.Log().Error(context.TODO(), err)
		return
	}
	filetype := http.DetectContentType(buff)
	return filetype, nil
}

// GetFilesPath 获取附件相对路径
func GetFilesPath(ctx context.Context, fileUrl string) (path string, err error) {
	upType := g.Cfg().MustGet(ctx, "upload.default").Int()
	if upType != 0 || (upType == 0 && !gstr.ContainsI(fileUrl, consts.UploadPath)) {
		path = fileUrl
		return
	}
	pathInfo, err := gurl.ParseURL(fileUrl, 32)
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.New("解析附件路径失败")
		return
	}
	pos := gstr.PosI(pathInfo["path"], consts.UploadPath)
	if pos >= 0 {
		path = gstr.SubStr(pathInfo["path"], pos)
	}
	return
}

// SliceUnique 数字元素去重
func SliceUnique[T comparable](slice []T) []T {
	encountered := map[T]bool{}
	result := []T{}

	for _, v := range slice {
		if !encountered[v] {
			encountered[v] = true
			result = append(result, v)
		}
	}
	return result
}

// TryParsNum 尝试将string 转换成为int 不出错
func TryParsNum(s string) (bool, int) {
	i, err := strconv.Atoi(s)
	if err != nil {
		return false, 0
	}
	return true, i
}
func ParsNum(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return i
}
func BuildLimit(page *comModel.PageReq) string {
	if page.PageNum == 0 {
		page.PageNum = 1
	}
	if page.PageSize == 0 {
		page.PageSize = 10
	}
	return fmt.Sprintf(" limit %v,%v ", (page.PageNum-1)*page.PageSize, page.PageSize)
}

// RecordToEntity 使用反射序列化对象 一起维护 已经支持 gtime.Time类型   需要结构体定义time 为  *gtime.Time
func RecordToEntity(record gdb.Record, entity interface{}) {
	// 获取Person对象的Type
	//bT := time.Now()
	personType := reflect.TypeOf(entity).Elem()
	// 遍历Map的所有字段
	for key, value := range record {
		for i := 0; i < personType.NumField(); i++ {
			field := personType.Field(i)
			// 判断该字段是否存在JSON Tag
			jsonTag := field.Tag.Get("json")
			if jsonTag == "" {
				jsonTag = field.Tag.Get("p")
			}
			if len(jsonTag) > 0 && jsonTag == key {
				fieldValue := reflect.ValueOf(entity).Elem().FieldByName(field.Name)
				if value == nil {
					break
				}
				//fmt.Printf("%s -> %s\n", field.Name, jsonTag)
				if fieldValue.IsValid() && fieldValue.CanSet() {
					switch fieldValue.Kind() {
					case reflect.String:
						fieldValue.SetString(value.Val().(string))
					case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
						v, ok := value.Val().(int)
						if ok {
							fieldValue.SetInt(int64(v))
							break
						}
						v64, ok64 := value.Val().(int64)
						if ok64 {
							fieldValue.SetInt(v64)
							break
						}
						v32, ok32 := value.Val().(int32)
						if ok32 {
							fieldValue.SetInt(int64(v32))
							break
						}
						v8, ok8 := value.Val().(int8)
						if ok8 {
							fieldValue.SetInt(int64(v8))
							break
						}

						uV, ok := value.Val().(uint)
						if ok {
							fieldValue.SetInt(int64(uV))
							break
						}
						uV32, ok32 := value.Val().(uint32)
						if ok32 {
							fieldValue.SetInt(int64(uV32))
							break
						}
						uV64, ok64 := value.Val().(uint64)
						if ok64 {
							fieldValue.SetInt(int64(uV64))
							break
						}

						n, _ := strconv.Atoi(value.Val().(string))
						fieldValue.SetInt(int64(n))
					case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
						if value == nil {
							fieldValue.SetUint(0)
							break
						}
						v, ok := value.Val().(uint)
						if ok {
							fieldValue.SetUint(uint64(v))
							break
						}
						v8, ok8 := value.Val().(uint8)
						if ok8 {
							fieldValue.SetUint(uint64(v8))
							break
						}
						v16, ok8 := value.Val().(uint16)
						if ok8 {
							fieldValue.SetUint(uint64(v16))
							break
						}
						v64, ok64 := value.Val().(uint64)
						if ok64 {
							fieldValue.SetUint(v64)
							break
						}
						v32, ok32 := value.Val().(uint32)
						if ok32 {
							fieldValue.SetUint(uint64(v32))
							break
						}

						u, _ := strconv.ParseUint(value.Val().(string), 10, 64) // 第二个参数表示基数（这里是十进制），第三个参数表示目标位数（这里是64位）
						fieldValue.SetUint(u)
					case reflect.Float32, reflect.Float64:
						if value == nil {
							fieldValue.SetFloat(0)
							break
						}
						f64, ok := value.Val().(float64)
						if ok {
							fieldValue.SetFloat(f64)
							break
						}
						f32, ok := value.Val().(float32)
						if ok {
							fieldValue.SetFloat(float64(f32))
							break
						}
						f, _ := strconv.ParseFloat(value.Val().(string), 64) // 调用ParseFloat函数进行转换
						fieldValue.SetFloat(f)
					case reflect.Bool:
						b, _ := strconv.ParseBool(value.Val().(string))
						fieldValue.SetBool(b)
					case reflect.Struct:
						if strings.Contains(strings.ToLower(field.Name), "time") || strings.Contains(strings.ToLower(field.Name), "date") {
							v, ok := value.Val().(gtime.Time)
							if ok {
								fieldValue.Set(reflect.ValueOf(v))
								break
							}

							thisTime, err := ParseDateString(value.Val().(string))
							if err != nil {
								break
							}
							fieldValue.Set(reflect.ValueOf(gtime.NewFromTime(thisTime)))

						}

					default:
						if strings.Contains(strings.ToLower(field.Name), "time") || strings.Contains(strings.ToLower(field.Name), "date") {
							v, ok := value.Val().(*gtime.Time)
							if ok {
								fieldValue.Set(reflect.ValueOf(v))
								break
							}

							thisTime, err := ParseDateString(value.Val().(string))
							if err != nil {
								break
							}
							fieldValue.Set(reflect.ValueOf(gtime.NewFromTime(thisTime)))
							break
						}
						//fieldValue.SetString(value.Val().(string))
						break
					}
				}
				break
			}
		}
	}
	//eT := time.Since(bT)
	//fmt.Println("Run time: ", eT.Nanoseconds())
}

// IsEqualDateTime 判断两个gtime 是否是同一天
func IsEqualDateTime(t1, t2 *gtime.Time) bool {
	return t1.Time.Year() == t2.Time.Year() && t1.Time.Month() == t2.Time.Month() && t1.Time.Day() == t2.Time.Day()

}

// RoundMode 是一个枚举类型，表示舍入模式
type RoundMode int

const (
	// RoundUp 表示向上取整
	RoundUp RoundMode = iota
	// RoundDown 表示向下取整
	RoundDown
	// RoundHalfEven 表示四舍五入
	RoundHalfEven
)

// DivideAndRound 两个数相除结果保留两位小数
func DivideAndRound(a, b float64, scale int, mode RoundMode) float64 {
	if b == 0 {
		return 0
	}
	result := a / b
	return ToRound(result, scale, mode)
}

func ToRound(result float64, scale int, mode RoundMode) float64 {
	pow := math.Pow(10, float64(scale))
	if mode == RoundUp {
		result = math.Ceil(result*pow) / pow
	} else if mode == RoundDown {
		result = math.Floor(result*pow) / pow
	} else if mode == RoundHalfEven {
		result = math.Round(result*pow) / pow
	}
	//无限大或无限小时 默认为0
	if fmt.Sprintf("%v", result) == "+Inf" || fmt.Sprintf("%v", result) == "-Inf" {
		result = 0
	}

	return result
}

// BuildSqlInStr 将list字符串转换成sql in 参数
func BuildSqlInStr(list []string) string {
	var strIn string
	if list != nil {
		for k, v := range list {
			strIn = strIn + "'" + v + "'"
			if k < len(list)-1 {
				strIn = strIn + ","
			}
		}
	}
	return strIn
}

func BuildSqlIntArray(list []int) string {
	var strIn string
	if list != nil {
		for k, v := range list {
			strIn = strIn + gconv.String(v)
			if k < len(list)-1 {
				strIn = strIn + ","
			}
		}
	}
	return "(" + strIn + ")"
}

// IntToStringSlice 将int 切片转成string 切片
func IntToStringSlice(numbers []int) []string {
	var stringSlice []string

	for _, num := range numbers {
		// 使用strconv.Itoa将整数转换为字符串
		str := strconv.Itoa(num)
		// 将字符串添加到切片中
		stringSlice = append(stringSlice, str)
	}
	return stringSlice
}

// IntToStringSlice2 将int 切片转成string 切片
func IntToStringSlice2(numbers []int64) []string {
	var stringSlice []string

	for _, num := range numbers {

		str := fmt.Sprintf("%d", num)
		// 将字符串添加到切片中
		stringSlice = append(stringSlice, str)
	}
	return stringSlice
}

func StringArrayToIntArray(strArr []string) []int {
	var intArr []int
	for _, s := range strArr {
		i, err := strconv.Atoi(s)
		if err != nil {
			return nil
		}
		intArr = append(intArr, i)
	}
	return intArr
}

func StringArrayToInt64Array(strArr []string) []int64 {
	var intArr []int64
	for _, s := range strArr {
		i, err := strconv.ParseInt(s, 10, 64) // Parse as int64
		if err != nil {
			return nil
		}
		intArr = append(intArr, i)
	}
	return intArr
}

func IsZeroValue(value interface{}) bool {
	switch v := value.(type) {
	case int:
		return v == 0
	case int16:
		return v == 0
	case int32:
		return v == 0
	case int64:
		return v == 0
	case float32:
		return v == 0.0
	case float64:
		return v == 0.0
	case nil:
		return true // nil is considered as "zero" for our purposes
	default:
		return false
	}
}

// RemoveDuplicates 切片去重
func RemoveDuplicates(slice []int) []int {
	encountered := map[int]bool{}
	var result []int
	for _, v := range slice {
		if !encountered[v] {
			encountered[v] = true
			result = append(result, v)
		}
	}
	return result
}

// RemoveDuplicates2 切片去重
func RemoveDuplicates2(nums []int) []int {
	if len(nums) == 0 {
		return nums
	}
	// 排序整数切片
	sort.Ints(nums)
	// 使用两个指针，一个用于遍历，一个用于记录不重复的元素位置
	i, j := 1, 1
	for i < len(nums) {
		// 如果当前元素和前一个元素不相同，将其复制到指定位置
		if nums[i] != nums[i-1] {
			nums[j] = nums[i]
			j++
		}
		i++
	}
	// 返回不重复元素的切片
	return nums[:j]
}

// RemoveSliceStr 移除切片指定内容
func RemoveSliceStr(slice []string, remSlice []string) []string {
	result := make([]string, 0)
	for _, v := range slice {
		if FindTargetStr(remSlice, v) {
			continue
		}
		result = append(result, v)
	}
	return result
}

// IsNumeric 校验是否纯数字
func IsNumeric(str string) bool {
	// 正则表达式匹配纯数字
	numericRegex := regexp.MustCompile(`^[0-9]+$`)
	return numericRegex.MatchString(str)
}

// SerializeStruct 将结构体序列化为 JSON 字符串
func SerializeStruct(data interface{}) (string, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

// SerializeQuoteStruct 配合生成java 类似的转义json字符串
func SerializeQuoteStruct(data interface{}) (string, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	str := string(jsonBytes)
	str = strconv.Quote(str)
	return str, nil
}

// GetDigit 获取数字
func GetDigit(str string) []int {
	re := regexp.MustCompile("[0-9]+")
	numbers := re.FindAllString(str, -1)
	var number []int
	for _, v := range numbers {
		number = append(number, gconv.Int(v))
	}
	return number
}

// IsSQLInjection 检查字符串是否包含SQL注入风险
func IsSQLInjection(input string) bool {
	// 匹配常见的SQL注入关键词，忽略大小写
	pattern := `(?i)(?:\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?|EXECUTE)\b)|(?:--)|(/\\*(?:.|\n)*?\\*/)`
	regex := regexp.MustCompile(pattern)
	return regex.MatchString(input)
}

// TrimTrailingZeros 去除.后面末尾0
func TrimTrailingZeros(s string) string {
	if strings.Contains(s, ".") {
		for strings.HasSuffix(s, "0") {
			s = strings.TrimSuffix(s, "0")
		}
	}

	if strings.HasSuffix(s, ".") {
		s = strings.TrimSuffix(s, ".")
	}
	return s
}

// FindMinMax 返回整数数组中的最大值和最小值
func FindMinMax(nums []int) (int, int) {
	if len(nums) == 0 {
		return 0, 0 // 如果数组为空，则返回0作为最大和最小值
	} else if len(nums) == 1 {
		return 0, nums[0]
	}

	min := nums[0]
	max := nums[0]

	for _, num := range nums {
		if num < min {
			min = num
		}
		if num > max {
			max = num
		}
	}

	return min, max
}

// ReplaceTestOssUrl oss url替换成带test.前缀的
func ReplaceTestOssUrl(url string) string {
	videoUrlSplits := strings.Split(url, consts.OssUrlScheme)
	if !strings.HasPrefix(videoUrlSplits[1], consts.OssUrlTestPrefix) {
		url = videoUrlSplits[0] + consts.OssUrlScheme + consts.OssUrlTestPrefix + videoUrlSplits[1]
	}
	return url
}

// FindTargetStr 判断切片中是否有目标元素
func FindTargetStr(slice []string, target string) bool {
	for _, element := range slice {
		if element == target {
			return true
		}
	}
	return false
}

// FindTargetStrCount 查询切片中目标元素个数
func FindTargetStrCount(slice []string, target string) int {
	var count int
	for _, element := range slice {
		if element == target {
			count++
		}
	}
	return count
}

// FindTargetInt 判断切片中是否有目标元素
func FindTargetInt(ids []int, target int) bool {
	for _, element := range ids {
		if element == target {
			return true
		}
	}
	return false
}

// IsValidStr 校验字符只允许使用字母、数字、'_'、'-'
func IsValidStr(str string) bool {
	validRegex := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	return validRegex.MatchString(str)
}
func MultiplyAndRound(a, b float64, scale int, mode RoundMode) float64 {
	if b == 0 {
		return 0
	}
	result := a * b
	return ToRound(result, scale, mode)
}

// parameterAddToHeaderOrQuery adds the provided object to the request header or url query
// supporting deep object syntax
func ParameterAddToHeaderOrQuery(headerOrQueryParams interface{}, keyPrefix string, obj interface{}) {
	var v = reflect.ValueOf(obj)
	var value = ""
	if v == reflect.ValueOf(nil) {
		value = "null"
	} else {
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}
		switch v.Kind() {
		case reflect.String:
			value = v.String()
		case reflect.Ptr:
			v = v.Elem()
			if v.Kind() == reflect.String {
				value = v.String()
			} else {
				b, _ := json.Marshal(obj)
				value = string(b)
			}
		default:
			b, _ := json.Marshal(obj)
			value = string(b)
		}
	}

	switch valuesMap := headerOrQueryParams.(type) {
	case url.Values:
		if value != "null" {
			valuesMap.Add(keyPrefix, value)
		}
		break
	case map[string]string:
		valuesMap[keyPrefix] = value
		break
	}
}
func InterfaceToMapByReflection(v interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	val := reflect.ValueOf(v)
	for i := 0; i < val.NumField(); i++ {
		fieldName := val.Type().Field(i).Name
		fieldValue := val.Field(i).Interface()
		result[fieldName] = fieldValue
	}
	return result
}
func InterfaceToMapByAssertion(iface interface{}) (map[string]interface{}, bool) {
	t := reflect.TypeOf(iface)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	if t.Kind() != reflect.Struct {
		return nil, false
	}

	v := reflect.ValueOf(iface)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	fields := make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fieldValue := v.Field(i).Interface()
		fields[field.Name] = fieldValue
	}

	return fields, true
}
func AddAndRound(a, b float64, scale int, mode RoundMode) float64 {

	result := a + b
	return ToRound(result, scale, mode)
}

func MapToStruct(m map[string]string, result interface{}) error {
	// 获取传入结构体的类型和值
	structValue := reflect.ValueOf(result).Elem()
	structType := structValue.Type()

	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)   // 获取字段
		mapKey := field.Tag.Get("orm") // 获取字段对应的 map key
		if mapKey == "" {              // 如果没有 tag，跳过
			mapKey = field.Name
		}

		mapValue, exists := m[mapKey] // 获取 map 中的值
		if !exists {
			continue // 如果 map 中没有这个 key，跳过
		}

		// 设置字段值
		fieldValue := structValue.Field(i)
		if !fieldValue.CanSet() {
			continue
		}

		// 根据字段类型设置值
		switch fieldValue.Kind() {
		case reflect.String:
			fieldValue.SetString(mapValue)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			intValue, err := strconv.ParseInt(mapValue, 10, 64)
			if err != nil {
				return fmt.Errorf("failed to convert field %s to int: %v", field.Name, err)
			}
			fieldValue.SetInt(intValue)
		case reflect.Float32, reflect.Float64:
			floatValue, err := strconv.ParseFloat(mapValue, 64)
			if err != nil {
				return fmt.Errorf("failed to convert field %s to float: %v", field.Name, err)
			}
			fieldValue.SetFloat(floatValue)
		case reflect.Bool:
			boolValue, err := strconv.ParseBool(mapValue)
			if err != nil {
				return fmt.Errorf("failed to convert field %s to bool: %v", field.Name, err)
			}
			fieldValue.SetBool(boolValue)
		default:
			return fmt.Errorf("unsupported field type: %s", field.Type.Name())
		}
	}

	return nil
}

func MapToStruct2(m map[string]string, result interface{}) error {
	// 获取传入结构体的类型和值
	structValue := reflect.ValueOf(result).Elem()
	structType := structValue.Type()

	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)    // 获取字段
		mapKey := field.Tag.Get("json") // 获取字段对应的 map key
		if mapKey == "" {               // 如果没有 tag，跳过
			mapKey = field.Name
		}

		mapValue, exists := m[mapKey] // 获取 map 中的值
		if !exists {
			continue // 如果 map 中没有这个 key，跳过
		}

		// 设置字段值
		fieldValue := structValue.Field(i)
		if !fieldValue.CanSet() {
			continue
		}

		// 根据字段类型设置值
		switch fieldValue.Kind() {
		case reflect.String:
			fieldValue.SetString(mapValue)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			//intValue, err := strconv.ParseInt(mapValue, 10, 64)
			intValue := gconv.Int64(mapValue)
			//if err != nil {
			//	return fmt.Errorf("failed to convert field %s to int: %v", field.Name, err)
			//}
			fieldValue.SetInt(intValue)
		case reflect.Float32, reflect.Float64:
			floatValue, err := strconv.ParseFloat(mapValue, 64)
			if err != nil {
				return fmt.Errorf("failed to convert field %s to float: %v", field.Name, err)
			}
			fieldValue.SetFloat(floatValue)
		case reflect.Bool:
			boolValue, err := strconv.ParseBool(mapValue)
			if err != nil {
				return fmt.Errorf("failed to convert field %s to bool: %v", field.Name, err)
			}
			fieldValue.SetBool(boolValue)
		default:
			return fmt.Errorf("unsupported field type: %s", field.Type.Name())
		}
	}

	return nil
}

func GetDailyNum(dailyNum *int) string {
	*dailyNum++
	var dailyNumStr = strconv.Itoa(*dailyNum)
	if *dailyNum < 10 {
		dailyNumStr = "0" + dailyNumStr
	}
	return dailyNumStr
}

// GetFieldsWithCustomTag 获取带有 custom 标签的字段及其值
func GetFieldsWithCustomTag(data interface{}) map[string]string {
	result := make(map[string]string)
	val := reflect.ValueOf(data)
	typ := val.Type()
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		// 获取 custom 标签的值
		tag := field.Tag.Get("custom")
		if tag != "" {
			// key为标签值 value为字段描述
			result[tag] = fmt.Sprintf("%v", field.Tag.Get("dc"))
		}
	}
	return result
}
