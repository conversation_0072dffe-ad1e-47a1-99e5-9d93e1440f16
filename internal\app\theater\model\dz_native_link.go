// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/model/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzNativeLinkInfoRes is the golang structure for table dz_native_link.
type DzNativeLinkInfoRes struct {
	gmeta.Meta        `orm:"table:dz_native_link"`
	Id                uint64      `orm:"id,primary" json:"id" dc:"主键ID"`                                        // 主键ID
	ProjectId         string      `orm:"project_id" json:"project_id" dc:"项目ID"`                                // 项目ID
	BookId            string      `orm:"book_id" json:"book_id" dc:"书籍ID"`                                      // 书籍ID
	BookName          string      `orm:"book_name" json:"book_name" dc:"书籍名称"`                                  // 书籍名称
	DyBookId          string      `orm:"dy_book_id" json:"dy_book_id" dc:"抖音书籍ID"`                              // 抖音书籍ID
	AccountUserName   string      `orm:"account_user_name" json:"account_user_name" dc:"账户用户名"`                 // 账户用户名
	DistributorId     string      `orm:"distributor_id" json:"distributor_id" dc:"分销商ID"`                       // 分销商ID
	DistributorName   string      `orm:"distributor_name" json:"distributor_name" dc:"分销商名称"`                   // 分销商名称
	ChannelId         int64       `orm:"channel_id" json:"channel_id" dc:"渠道ID"`                                // 渠道ID
	PurchasePanelId   string      `orm:"purchase_panel_id" json:"purchase_panel_id" dc:"购买面板ID"`                // 购买面板ID
	PurchasePanelName string      `orm:"purchase_panel_name" json:"purchase_panel_name" dc:"购买面板名称"`            // 购买面板名称
	PurchasePanelJson string      `orm:"purchase_panel_json" json:"purchase_panel_json" dc:"购买面板JSON配置"`        // 购买面板JSON配置
	AdvertiseLink     string      `orm:"advertise_link" json:"advertise_link" dc:"广告推广链接"`                      // 广告推广链接
	TaskStatus        int         `orm:"task_status" json:"task_status" dc:"任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败"` // 任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败
	Response          string      `orm:"response" json:"response" dc:"接口响应信息"`                                  // 接口响应信息
	CreateTime        *gtime.Time `orm:"create_time" json:"create_time" dc:"创建时间"`                              // 创建时间
	UpdateTime        *gtime.Time `orm:"update_time" json:"update_time" dc:"更新时间"`                              // 更新时间
	BookStatus        int         `orm:"book_status" json:"book_status" dc:"书籍状态：1-未上架，2-已上架，3-已下架"`            // 书籍状态：1-未上架，2-已上架，3-已下架
	BookType          int         `orm:"book_type" json:"book_type" dc:"书籍类型：1-短剧，2-小说"`                        // 书籍类型：1-短剧，2-小说
	Connect           bool        `orm:"connect" json:"connect" dc:"连接状态：0-未连接，1-已连接"`                          // 连接状态：0-未连接，1-已连接
	PutTimeText       string      `orm:"put_time_text" json:"put_time_text" dc:"投放时间文本"`                        // 投放时间文本
	Type              int         `orm:"type" json:"type" dc:"类型 1-付费 2-免费"`                                    // 类型 1-付费 2-免费
}

type DzNativeLinkListRes struct {
	Id                uint64      `json:"id" dc:"主键ID"`
	ProjectId         string      `json:"projectId" dc:"项目ID"`
	BookId            string      `json:"bookId" dc:"书籍ID"`
	BookName          string      `json:"bookName" dc:"书籍名称"`
	DyBookId          string      `json:"dyBookId" dc:"抖音书籍ID"`
	AccountUserName   string      `json:"accountUserName" dc:"账户用户名"`
	DistributorId     string      `json:"distributorId" dc:"分销商ID"`
	DistributorName   string      `json:"distributorName" dc:"分销商名称"`
	ChannelId         int64       `json:"channelId" dc:"渠道ID"`
	PurchasePanelId   string      `json:"purchasePanelId" dc:"购买面板ID"`
	PurchasePanelName string      `json:"purchasePanelName" dc:"购买面板名称"`
	PurchasePanelJson string      `json:"purchasePanelJson" dc:"购买面板JSON配置"`
	AdvertiseLink     string      `json:"advertiseLink" dc:"广告推广链接"`
	TaskStatus        int         `json:"taskStatus" dc:"任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败"`
	Response          string      `json:"response" dc:"接口响应信息"`
	CreateTime        *gtime.Time `json:"createTime" dc:"创建时间"`
	UpdateTime        *gtime.Time `json:"updateTime" dc:"更新时间"`
	BookStatus        int         `json:"bookStatus" dc:"书籍状态：1-未上架，2-已上架，3-已下架"`
	BookType          int         `json:"bookType" dc:"书籍类型：1-短剧，2-小说"`
	Connect           int         `json:"connect" dc:"连接状态：0-未连接，1-已连接"`
	PutTimeText       string      `json:"putTimeText" dc:"投放时间文本"`
	Type              int         `json:"type" dc:"类型 1-付费 2-免费"`
}

// DzNativeLinkSearchReq 分页请求参数
type DzNativeLinkSearchReq struct {
	comModel.PageReq
	Id                string `p:"id" dc:"主键ID"`                                                                                                   //主键ID
	ProjectId         string `p:"projectId" dc:"项目ID"`                                                                                            //项目ID
	BookId            string `p:"bookId" dc:"书籍ID"`                                                                                               //书籍ID
	BookName          string `p:"bookName" dc:"书籍名称"`                                                                                             //书籍名称
	DyBookId          string `p:"dyBookId" dc:"抖音书籍ID"`                                                                                           //抖音书籍ID
	AccountUserName   string `p:"accountUserName" dc:"账户用户名"`                                                                                     //账户用户名
	DistributorId     string `p:"distributorId" dc:"分销商ID"`                                                                                       //分销商ID
	DistributorName   string `p:"distributorName" dc:"分销商名称"`                                                                                     //分销商名称
	ChannelId         string `p:"channelId" v:"channelId@integer#渠道ID需为整数" dc:"渠道ID"`                                                             //渠道ID
	PurchasePanelId   string `p:"purchasePanelId" dc:"购买面板ID"`                                                                                    //购买面板ID
	PurchasePanelName string `p:"purchasePanelName" dc:"购买面板名称"`                                                                                  //购买面板名称
	PurchasePanelJson string `p:"purchasePanelJson" dc:"购买面板JSON配置"`                                                                              //购买面板JSON配置
	AdvertiseLink     string `p:"advertiseLink" dc:"广告推广链接"`                                                                                      //广告推广链接
	TaskStatus        string `p:"taskStatus" v:"taskStatus@integer#任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败需为整数" dc:"任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败"` //任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败
	Response          string `p:"response" dc:"接口响应信息"`                                                                                           //接口响应信息
	CreateTime        string `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                       //创建时间
	UpdateTime        string `p:"updateTime" v:"updateTime@datetime#更新时间需为YYYY-MM-DD hh:mm:ss格式" dc:"更新时间"`                                       //更新时间
	BookStatus        string `p:"bookStatus" v:"bookStatus@integer#书籍状态：1-未上架，2-已上架，3-已下架需为整数" dc:"书籍状态：1-未上架，2-已上架，3-已下架"`                       //书籍状态：1-未上架，2-已上架，3-已下架
	BookType          string `p:"bookType" v:"bookType@integer#书籍类型：1-短剧，2-小说需为整数" dc:"书籍类型：1-短剧，2-小说"`                                           //书籍类型：1-短剧，2-小说
	Connect           string `p:"connect" v:"connect@integer#连接状态：0-未连接，1-已连接需为整数" dc:"连接状态：0-未连接，1-已连接"`                                         //连接状态：0-未连接，1-已连接
	PutTimeText       string `p:"putTimeText" dc:"投放时间文本"`                                                                                        //投放时间文本
	Type              string `p:"type" v:"type@integer#类型 1-付费 2-免费需为整数" dc:"类型 1-付费 2-免费"`                                                       //类型 1-付费 2-免费
}

// DzNativeLinkSearchRes 列表返回结果
type DzNativeLinkSearchRes struct {
	comModel.ListRes
	List []*DzNativeLinkListRes `json:"list"`
}

// DzNativeLinkAddReq 添加操作请求参数
type DzNativeLinkAddReq struct {
	Id                uint64      `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	ProjectId         string      `p:"projectId"  dc:"项目ID"`
	BookId            string      `p:"bookId"  dc:"书籍ID"`
	BookName          string      `p:"bookName" v:"required#书籍名称不能为空" dc:"书籍名称"`
	DyBookId          string      `p:"dyBookId"  dc:"抖音书籍ID"`
	AccountUserName   string      `p:"accountUserName" v:"required#账户用户名不能为空" dc:"账户用户名"`
	DistributorId     string      `p:"distributorId"  dc:"分销商ID"`
	DistributorName   string      `p:"distributorName" v:"required#分销商名称不能为空" dc:"分销商名称"`
	ChannelId         int64       `p:"channelId"  dc:"渠道ID"`
	PurchasePanelId   string      `p:"purchasePanelId"  dc:"购买面板ID"`
	PurchasePanelName string      `p:"purchasePanelName" v:"required#购买面板名称不能为空" dc:"购买面板名称"`
	PurchasePanelJson string      `p:"purchasePanelJson"  dc:"购买面板JSON配置"`
	AdvertiseLink     string      `p:"advertiseLink"  dc:"广告推广链接"`
	TaskStatus        int         `p:"taskStatus" v:"required#任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败不能为空" dc:"任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败"`
	Response          string      `p:"response"  dc:"接口响应信息"`
	CreateTime        *gtime.Time `p:"createTime"  dc:"创建时间"`
	UpdateTime        *gtime.Time `p:"updateTime"  dc:"更新时间"`
	BookStatus        int         `p:"bookStatus" v:"required#书籍状态：1-未上架，2-已上架，3-已下架不能为空" dc:"书籍状态：1-未上架，2-已上架，3-已下架"`
	BookType          int         `p:"bookType"  dc:"书籍类型：1-短剧，2-小说"`
	Connect           int         `p:"connect"  dc:"连接状态：0-未连接，1-已连接"`
	PutTimeText       string      `p:"putTimeText"  dc:"投放时间文本"`
	Type              int         `p:"type"  dc:"类型 1-付费 2-免费"`
}

// DzNativeLinkEditReq 修改操作请求参数
type DzNativeLinkEditReq struct {
	Id                uint64      `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	ProjectId         string      `p:"projectId"  dc:"项目ID"`
	BookId            string      `p:"bookId"  dc:"书籍ID"`
	BookName          string      `p:"bookName" v:"required#书籍名称不能为空" dc:"书籍名称"`
	DyBookId          string      `p:"dyBookId"  dc:"抖音书籍ID"`
	AccountUserName   string      `p:"accountUserName" v:"required#账户用户名不能为空" dc:"账户用户名"`
	DistributorId     string      `p:"distributorId"  dc:"分销商ID"`
	DistributorName   string      `p:"distributorName" v:"required#分销商名称不能为空" dc:"分销商名称"`
	ChannelId         int64       `p:"channelId"  dc:"渠道ID"`
	PurchasePanelId   string      `p:"purchasePanelId"  dc:"购买面板ID"`
	PurchasePanelName string      `p:"purchasePanelName" v:"required#购买面板名称不能为空" dc:"购买面板名称"`
	PurchasePanelJson string      `p:"purchasePanelJson"  dc:"购买面板JSON配置"`
	AdvertiseLink     string      `p:"advertiseLink"  dc:"广告推广链接"`
	TaskStatus        int         `p:"taskStatus" v:"required#任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败不能为空" dc:"任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败"`
	Response          string      `p:"response"  dc:"接口响应信息"`
	CreateTime        *gtime.Time `p:"createTime"  dc:"创建时间"`
	UpdateTime        *gtime.Time `p:"updateTime"  dc:"更新时间"`
	BookStatus        int         `p:"bookStatus" v:"required#书籍状态：1-未上架，2-已上架，3-已下架不能为空" dc:"书籍状态：1-未上架，2-已上架，3-已下架"`
	BookType          int         `p:"bookType"  dc:"书籍类型：1-短剧，2-小说"`
	Connect           int         `p:"connect"  dc:"连接状态：0-未连接，1-已连接"`
	PutTimeText       string      `p:"putTimeText"  dc:"投放时间文本"`
	Type              int         `p:"type"  dc:"类型 1-付费 2-免费"`
}

// LoginRequest 登录请求参数
type LoginRequest struct {
	Username string `json:"username" v:"required#用户名不能为空"`
	Password string `json:"password" v:"required#密码不能为空"`
}

// TaskListRequest 任务列表请求参数
type TaskListRequest struct {
	Offset    int    `json:"offset" v:"min:0#偏移量不能小于0"`
	Limit     int    `json:"limit" v:"required|min:1|max:100#每页数量不能为空|每页数量不能小于1|每页数量不能大于100"`
	Filter    string `json:"filter"` // JSON格式的过滤条件
	PHPSESSID string `json:"phpsessid" v:"required#PHPSESSID不能为空"`
	Sort      string `json:"sort"`  // 排序字段，默认为id
	Order     string `json:"order"` // 排序方向，默认为desc
}

// TaskListResponse 任务列表响应结构
type TaskListResponse struct {
	Total int                    `json:"total"` // 总数
	Rows  []*DzNativeLinkInfoRes `json:"rows"`  // 数据列表
}

// CrawlConfig 爬取配置
type CrawlConfig struct {
	Username string `json:"username"` // 用户名
	Password string `json:"password"` // 密码
	Url      string `json:"url"`      // 爬取URL
	IsPaid   bool   `json:"is_paid"`  // 是否付费账号
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	Title  string // 消息标题
	ChatId string // 群组ID
	IsPaid bool   // 是否付费模板
}
