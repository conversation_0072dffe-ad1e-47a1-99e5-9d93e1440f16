// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:26
// 生成路径: internal/app/ad/logic/dz_ad_account.go
// 生成人：cyao
// desc:点众账号管理表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"github.com/xuri/excelize/v2"
)

func init() {
	service.RegisterDzAdAccount(New())
}

func New() service.IDzAdAccount {
	return &sDzAdAccount{}
}

type sDzAdAccount struct{}

func (s *sDzAdAccount) List(ctx context.Context, req *model.DzAdAccountSearchReq) (listRes *model.DzAdAccountSearchRes, err error) {
	listRes = new(model.DzAdAccountSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.DzAdAccount.Ctx(ctx).WithAll().As("a")
		if !admin {
			m = m.InnerJoin(dao.DzAdAccountChannel.Table(), "b", " a.account_id = b.account_id").
				LeftJoin(dao.DzAdAccountDepts.Table(), "d", " b.channel_id = d.channel_id").
				LeftJoin(dao.DzAdAccountUsers.Table(), "u", " b.channel_id = u.channel_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if req.Id != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().Id+" = ?", req.Id)
		}
		if req.AccountName != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().AccountName+" like ?", "%"+req.AccountName+"%")
		}
		if req.AccountId != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().AccountId+" = ?", req.AccountId)
		}
		if len(req.AccountIds) > 0 {
			m = m.WhereIn("a."+dao.DzAdAccount.Columns().AccountId, req.AccountIds)
		}
		if req.Token != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().Token+" = ?", req.Token)
		}
		if len(req.DateRange) != 0 {
			m = m.Where("a."+dao.DzAdAccount.Columns().CreatedAt+" >=? AND "+"a."+dao.DzAdAccount.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Fields("DISTINCT a.account_id").Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzAdAccountListRes
		err = m.Fields("DISTINCT a.*").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdAccountListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzAdAccountListRes{
				Id:          v.Id,
				AccountName: v.AccountName,
				AccountId:   v.AccountId,
				Token:       v.Token,
				Remark:      v.Remark,
				CreatedAt:   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sDzAdAccount) List2(ctx context.Context, req *model.DzAdAccountSearchReq) (listRes *model.DzAdAccountSearchRes, err error) {
	listRes = new(model.DzAdAccountSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdAccount.Ctx(ctx).WithAll().As("a")
		if req.Id != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().Id+" = ?", req.Id)
		}
		if req.AccountName != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().AccountName+" like ?", "%"+req.AccountName+"%")
		}
		if req.AccountId != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().AccountId+" = ?", req.AccountId)
		}
		if len(req.AccountIds) > 0 {
			m = m.WhereIn("a."+dao.DzAdAccount.Columns().AccountId, req.AccountIds)
		}
		if req.Token != "" {
			m = m.Where("a."+dao.DzAdAccount.Columns().Token+" = ?", req.Token)
		}
		if len(req.DateRange) != 0 {
			m = m.Where("a."+dao.DzAdAccount.Columns().CreatedAt+" >=? AND "+"a."+dao.DzAdAccount.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzAdAccountListRes
		err = m.Fields("DISTINCT a.*").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdAccountListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzAdAccountListRes{
				Id:          v.Id,
				AccountName: v.AccountName,
				AccountId:   v.AccountId,
				Token:       v.Token,
				Remark:      v.Remark,
				CreatedAt:   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sDzAdAccount) Import(ctx context.Context, file *ghttp.UploadFile) (err error) {
	if file == nil {
		err = errors.New("请上传数据文件")
		return
	}
	var data []do.DzAdAccount
	err = g.Try(ctx, func(ctx context.Context) {
		f, err := file.Open()
		liberr.ErrIsNil(ctx, err)
		defer f.Close()
		exFile, err := excelize.OpenReader(f)
		liberr.ErrIsNil(ctx, err)
		defer exFile.Close()
		rows, err := exFile.GetRows("Sheet1")
		liberr.ErrIsNil(ctx, err)
		if len(rows) == 0 {
			liberr.ErrIsNil(ctx, errors.New("表格内容不能为空"))
		}
		d := make([]interface{}, len(rows[0]))
		data = make([]do.DzAdAccount, len(rows)-1)
		for k, v := range rows {
			if k == 0 {
				continue
			}
			for kv, vv := range v {
				d[kv] = vv
			}
			data[k-1] = do.DzAdAccount{
				AccountName: d[0],
				AccountId:   d[1],
				Token:       d[2],
				Remark:      d[3],
				CreatedAt:   gconv.GTime(d[4]),
				UpdatedAt:   gconv.GTime(d[5]),
			}
		}
		if len(data) > 0 {
			err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
				_, err = dao.DzAdAccount.Ctx(ctx).Batch(500).Insert(data)
				return err
			})
			liberr.ErrIsNil(ctx, err)
		}
	})
	return
}

func (s *sDzAdAccount) GetById(ctx context.Context, id uint64) (res *model.DzAdAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdAccount.Ctx(ctx).WithAll().Where(dao.DzAdAccount.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdAccount) GetByAccountId(ctx context.Context, accountId string) (res *model.DzAdAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdAccount.Ctx(ctx).WithAll().Where(dao.DzAdAccount.Columns().AccountId, accountId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")

	})
	return
}

func (s *sDzAdAccount) Add(ctx context.Context, req *model.DzAdAccountAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccount.Ctx(ctx).Insert(do.DzAdAccount{
			AccountName: req.AccountName,
			AccountId:   req.AccountId,
			Token:       req.Token,
			Remark:      req.Remark,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
		// 添加的时候同步点众的渠道
		service.DzAdAccountChannel().SyncDzAdAccountChannel(ctx)
	})
	return
}

func (s *sDzAdAccount) Edit(ctx context.Context, req *model.DzAdAccountEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccount.Ctx(ctx).WherePri(req.Id).Update(do.DzAdAccount{
			AccountName: req.AccountName,
			AccountId:   req.AccountId,
			Token:       req.Token,
			Remark:      req.Remark,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
		// 添加的时候同步点众的渠道
		service.DzAdAccountChannel().SyncDzAdAccountChannel(ctx)
	})
	return
}

func (s *sDzAdAccount) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccount.Ctx(ctx).Delete(dao.DzAdAccount.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
