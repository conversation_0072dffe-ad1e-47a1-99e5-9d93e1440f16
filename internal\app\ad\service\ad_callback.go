// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/service/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IAdCallback interface {
	KsAdCallBack(ctx context.Context, req *model.KsAdCallBackReq) (res *model.KsAdCallBackRes, err error)
	DzAdCallBack(ctx context.Context, req *model.DzAdCallBackReq) (err error)
	OceanEngineCallback(ctx context.Context, req *model.OceanEngineCallbackReq) (res *model.OceanEngineCallbackRes, err error)
	OceanEngineXTCallback(ctx context.Context, req *model.OceanEngineXTCallbackReq) (res *model.OceanEngineCallbackRes, err error)
	OceanEngineSubscribeValid(ctx context.Context, req *model.OceanEngineSubscribeValidReq) (res *model.OceanEngineSubscribeValidRes, err error)
	OceanEngineSubscribe(ctx context.Context) (res *model.OceanEngineSubscribeValidRes, err error)
}

var localAdCallback IAdCallback

func AdCallback() IAdCallback {
	if localAdCallback == nil {
		panic("implement not found for interface IAdAppConfig, forgot register?")
	}
	return localAdCallback
}

func RegisterAdCallback(i IAdCallback) {
	localAdCallback = i
}
