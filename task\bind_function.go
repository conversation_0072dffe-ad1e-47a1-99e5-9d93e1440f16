/*
* @desc:定时任务配置
* @company:云南省奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON>
* @Date:   2021/7/16 15:45
 */

package task

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	adxService "github.com/tiger1103/gfast/v3/internal/app/adx/service"
	appletService "github.com/tiger1103/gfast/v3/internal/app/applet/service"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	memberService "github.com/tiger1103/gfast/v3/internal/app/member/service"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	orderService "github.com/tiger1103/gfast/v3/internal/app/order/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterService "github.com/tiger1103/gfast/v3/internal/app/theater/service"
)

func Run() {
	task1 := &model.TimeTask{
		FuncName: "test1",
		Run:      Test1,
	}
	task2 := &model.TimeTask{
		FuncName: "test2",
		Run:      Test2,
	}

	hourRefreshCoinTask := &model.TimeTask{
		FuncName: "HourRefreshCoinTask",
		Run:      HourRefreshCoinTask,
	}
	setUserAdditionalVideoByTimeTask := &model.TimeTask{
		FuncName: "SetUserAdditionalVideoByTimeTask",
		Run:      SetUserAdditionalVideoByTimeTask,
	}
	updateMetricsTask := &model.TimeTask{
		FuncName: "UpdateMetricsTask",
		Run:      UpdateMetricsTask,
	}
	orderStatTask := &model.TimeTask{
		FuncName: "OrderStatTask",
		Run:      OrderStatTask,
	}
	channelStatTask := &model.TimeTask{
		FuncName: "ChannelStatTask",
		Run:      ChannelStatTask,
	}
	channelRechargeStatTask := &model.TimeTask{
		FuncName: "ChannelRechargeStatTask",
		Run:      ChannelRechargeStatTask,
	}
	channelRechargeStatTodayTask := &model.TimeTask{
		FuncName: "ChannelRechargeStatTodayTask",
		Run:      ChannelRechargeStatTodayTask,
	}

	noRechargeHaveCoinConsumeChannelStatTask := &model.TimeTask{
		FuncName: "NoRechargeHaveCoinConsumeChannelStatTask",
		Run:      NoRechargeHaveCoinConsumeChannelStatTask,
	}
	wxNoRechargeHaveCoinConsumeChannelStatTask := &model.TimeTask{
		FuncName: "WxNoRechargeHaveCoinConsumeChannelStatTask",
		Run:      WxNoRechargeHaveCoinConsumeChannelStatTask,
	}
	channelDetailStatTask := &model.TimeTask{
		FuncName: "ChannelDetailStatTask",
		Run:      ChannelDetailStatTask,
	}
	syncFqPromotionUrlTask := &model.TimeTask{
		FuncName: "SyncFqPromotionUrlTask",
		Run:      channelService.SChannel().SyncFqPromotionUrlTask,
	}
	syncDzPromotionUrlTask := &model.TimeTask{
		FuncName: "SyncDzPromotionUrlTask",
		Run:      channelService.SChannel().SyncDzPromotionUrlTask,
	}
	videoStatTask := &model.TimeTask{
		FuncName: "VideoStatTask",
		Run:      VideoStatTask,
	}
	videoRechargeStatisticsTask := &model.TimeTask{
		FuncName: "VideoRechargeStatisticsTask",
		Run:      VideoRechargeStatisticsTask,
	}
	refreshTokenTask := &model.TimeTask{
		FuncName: "RefreshTokenTask",
		Run:      RefreshTokenTask,
	}

	syncTheaterRetentionStat := &model.TimeTask{
		FuncName: "SyncTheaterRetentionStat",
		Run:      SyncTheaterRetentionStat,
	}
	sendSignRemind := &model.TimeTask{
		FuncName: "SendSignRemind",
		Run:      SendSignRemind,
	}

	sAppletDepositRetentionStatistics := &model.TimeTask{
		FuncName: "SAppletDepositRetentionStatistics",
		Run:      SAppletDepositRetentionStatistics,
	}
	sAppletDepositRetentionStatisticsToday := &model.TimeTask{
		FuncName: "SAppletDepositRetentionStatisticsToday",
		Run:      SAppletDepositRetentionStatisticsToday,
	}
	syncTheaterRetentionStatToday := &model.TimeTask{
		FuncName: "SyncTheaterRetentionStatToday",
		Run:      SyncTheaterRetentionStatToday,
	}

	checkUserOnlineTask := &model.TimeTask{
		FuncName: "checkUserOnline",
		Run:      service.SysUserOnline().CheckUserOnline,
	}
	checkAlbumReviewResultTask := &model.TimeTask{
		FuncName: "CheckAlbumReviewResultTask",
		Run:      theaterService.SDyAlbumLibrary().CheckAlbumReviewResultTask,
	}
	queryDpaAlbumStatusTask := &model.TimeTask{
		FuncName: "QueryDpaAlbumStatusTask",
		Run:      theaterService.TheaterInfo().QueryDpaAlbumStatusTask,
	}
	sendNewTheaterDataReportTask := &model.TimeTask{
		FuncName: "SendNewTheaterDataReportTask",
		Run:      theaterService.TheaterInfo().SendNewTheaterDataReportTask,
	}
	sendOutsourcedTheaterDataReportTask := &model.TimeTask{
		FuncName: "SendOutsourcedTheaterDataReportTask",
		Run:      theaterService.TheaterInfo().SendOutsourcedTheaterDataReportTask,
	}
	sendBookDeliveryStatusReportTask := &model.TimeTask{
		FuncName: "SendBookDeliveryStatusReportTask",
		Run:      theaterService.ChangduBookInfo().SendBookDeliveryStatusReportTask,
	}
	calcDistributionStatisticsTask := &model.TimeTask{
		FuncName: "CalcDistributionStatisticsTask",
		Run:      orderService.SDistributionStatistics().CalcDistributionStatisticsTask,
	}

	calcDistributionStatisticsTodayTask := &model.TimeTask{
		FuncName: "CalcDistributionStatisticsTodayTask",
		Run:      CalcDistributionStatisticsTodayTask,
	}
	initVideoPlayLinkTask := &model.TimeTask{
		FuncName: "InitVideoPlayLinkTask",
		Run:      theaterService.TheaterInfo().InitVideoPlayLinkTask,
	}
	userWatchStatTask := &model.TimeTask{
		FuncName: "UserWatchStatTask",
		Run:      memberService.MMemberWatchStatistics().UserWatchStatTask,
	}
	runOrderPaySuccessRateTask := &model.TimeTask{
		FuncName: "RunOrderPaySuccessRateTask",
		Run:      orderService.OrderInfo().RunOrderPaySuccessRate,
	}
	checkRefundResultTask := &model.TimeTask{
		FuncName: "CheckRefundResultTask",
		Run:      orderService.OrderInfo().CheckRefundResultTask,
	}
	channelHourStatTask := &model.TimeTask{
		FuncName: "ChannelHourStatTask",
		Run:      ChannelHourStatTask,
	}
	calcAdCallbackStatisticsTask := &model.TimeTask{
		FuncName: "CalcAdCallbackStatisticsTask",
		Run:      channelService.MMemberAdCallbackStatistics().CalcAdCallbackStatisticsTask,
	}
	calcAdCallbackAppletStatisticsTask := &model.TimeTask{
		FuncName: "CalcAdCallbackAppletStatisticsTask",
		Run:      channelService.MMemberAdCallbackAppletStatistics().CalcAdCallbackAppletStatisticsTask,
	}
	calcAdCallbackWxPitcherStatisticsTask := &model.TimeTask{
		FuncName: "CalcAdCallbackWxPitcherStatisticsTask",
		Run:      channelService.MMemberAdCallbackWxPitcherStatistics().CalcAdCallbackWxPitcherStatisticsTask,
	}
	calcAdHourStatisticsTask := &model.TimeTask{
		FuncName: "CalcAdHourStatisticsTask",
		Run:      channelService.MMemberAdHourStatistics().CalcAdHourStatisticsTask,
	}
	calcAdCallbackCostTask := &model.TimeTask{
		FuncName: "CalcAdCallbackCostTask",
		Run:      channelService.MMemberAdCallback().CalcAdCallbackCostTask,
	}
	theaterPlayRechargeStatTask := &model.TimeTask{
		FuncName: "TheaterPlayRechargeStatTask",
		Run:      TheaterPlayRechargeStatTask,
	}
	calcTaskStatisticsTask := &model.TimeTask{
		FuncName: "CalcTaskStatisticsTask",
		Run:      memberService.MMemberTaskStatistics().CalcTaskStatisticsTask,
	}
	synCouponCodeTask := &model.TimeTask{
		FuncName: "SynCouponCodeTask",
		Run:      appletService.SCouponCode().SynCouponCodeSpecsTask,
	}
	couponStockNotifyTask := &model.TimeTask{
		FuncName: "CouponStockNotifyTask",
		Run:      appletService.SCouponCode().CouponStockNotifyTaskNew,
	}
	calcAppletRechargeTask := &model.TimeTask{
		FuncName: "CalcAppletRechargeTask",
		Run:      CalcAppletRechargeTask,
	}
	calcPitcherVideoRechargeTask := &model.TimeTask{
		FuncName: "CalcPitcherVideoRechargeTask",
		Run:      CalcPitcherVideoRechargeTask,
	}
	calcPitcherVideoYesterRechargeTask := &model.TimeTask{
		FuncName: "CalcPitcherVideoYesterRechargeTask",
		Run:      CalcPitcherVideoYesterRechargeTask,
	}
	theaterPlayRechargeYesterStatTask := &model.TimeTask{
		FuncName: "TheaterPlayRechargeYesterStatTask",
		Run:      TheaterPlayRechargeYesterStatTask,
	}
	calcAppletRechargeYesterTask := &model.TimeTask{
		FuncName: "CalcAppletRechargeYesterTask",
		Run:      CalcAppletRechargeYesterTask,
	}
	orderCouponCodeRecoveryTask := &model.TimeTask{
		FuncName: "OrderCouponCodeRecoveryTask",
		Run:      OrderCouponCodeRecoveryTask,
	}
	theaterHourTimesStatTask := &model.TimeTask{
		FuncName: "TheaterHourTimesStatTask",
		Run:      theaterService.TheaterHourTimesStat().TheaterHourTimesStatTask,
	}
	theaterHotRankingStatTask := &model.TimeTask{
		FuncName: "TheaterHotRankingStatTask",
		Run:      theaterService.TheaterHotRanking().TheaterHotRankingStatTask,
	}
	checkPanelBindTask := &model.TimeTask{
		FuncName: "CheckPanelBindTask",
		Run:      channelService.SDyPanelBind().CheckPanelBindTask,
	}
	syncAllAdvertiserInfoTask := &model.TimeTask{
		FuncName: "SyncAllAdvertiserInfoTask",
		Run:      oceanengineService.AdAdvertiserAccount().SyncAllAdvertiserInfoTask,
	}
	calcAdAccountSubjectDataStatTask := &model.TimeTask{
		FuncName: "CalcAdAccountSubjectDataStatTask",
		Run:      oceanengineService.AdAccountSubjectDataStat().CalcAdAccountSubjectDataStatTask,
	}
	calcTodayAdAccountSubjectDataStatTask := &model.TimeTask{
		FuncName: "CalcTodayAdAccountSubjectDataStatTask",
		Run:      oceanengineService.AdAccountSubjectDataStat().CalcTodayAdAccountSubjectDataStatTask,
	}
	calcAdOptimizerDataStatTask := &model.TimeTask{
		FuncName: "CalcAdOptimizerDataStatTask",
		Run:      oceanengineService.AdOptimizerDataStat().CalcAdOptimizerDataStatTask,
	}
	calcTodayAdOptimizerDataStatTask := &model.TimeTask{
		FuncName: "CalcTodayAdOptimizerDataStatTask",
		Run:      oceanengineService.AdOptimizerDataStat().CalcTodayAdOptimizerDataStatTask,
	}
	syncAdAdvertiserAccountHourMetricsDataTask := &model.TimeTask{
		FuncName: "SyncAdAdvertiserAccountHourMetricsDataTask",
		Run:      oceanengineService.AdAdvertiserAccountHourMetricsData().SyncAdAdvertiserAccountHourMetricsDataTask,
	}
	createSignPayTask := &model.TimeTask{
		FuncName: "CreateSignPayTask",
		Run:      orderService.SignDyPay().CreateSignPayTask,
	}
	calcAdOrderSettleStatTask := &model.TimeTask{
		FuncName: "CalcAdOrderSettleStatTask",
		Run:      adService.KsAdOrderSettle().CalcAdOrderSettleStatTask,
	}
	calcAdOrderDetailStatTask := &model.TimeTask{
		FuncName: "CalcAdOrderDetailStatTask",
		Run:      adService.KsAdOrderDetail().CalcAdOrderDetailStatTask,
	}
	calcAdSalerCopyRightStatTask := &model.TimeTask{
		FuncName: "CalcAdSalerCopyRightStatTask",
		Run:      adService.KsAdSalerCopyRight().CalcAdSalerCopyRightStatTask,
	}

	ksSeriesReportCoreData := &model.TimeTask{
		FuncName: "KsSeriesReportCoreDataTask",
		Run:      KsSeriesReportCoreDataTask,
	}

	ksADDataTask := &model.TimeTask{
		FuncName: "KsADDataTask",
		Run:      KsADDataTask,
	}

	xTADYesterdayDataTask := &model.TimeTask{
		FuncName: "XTADYesterdayDataTask",
		Run:      XTADYesterdayDataTask,
	}
	//XTADDetailDataTask
	xTADDetailDataTask := &model.TimeTask{
		FuncName: "XTADDetailDataTask",
		Run:      XTADDetailDataTask,
	}
	fqUserInfoDataTask := &model.TimeTask{
		FuncName: "FqUserInfoDataTask",
		Run:      FqUserInfoDataTask,
	}
	//DzOrderInfoDataTask
	dzOrderInfoDataTask := &model.TimeTask{
		FuncName: "DzOrderInfoDataTask",
		Run:      DzOrderInfoDataTask,
	}

	//补偿昨天的dz订单数据
	dzOrderInfoYesterdayDataTask := &model.TimeTask{
		FuncName: "DzOrderInfoYesterdayDataTask",
		Run:      DzOrderInfoYesterdayDataTask,
	}

	dzNativeLinkCrawlTask := &model.TimeTask{
		FuncName: "DzNativeLinkCrawlTask",
		Run:      theaterService.DzNativeLink().DzNativeLinkCrawlTask,
	}

	//FqAnalyzeDataTask
	fqAnalyzeDataTask := &model.TimeTask{
		FuncName: "FqAnalyzeDataTask",
		Run:      FqAnalyzeDataTask,
	}
	//FqUserPayDataTask
	fqUserPayDataTask := &model.TimeTask{
		FuncName: "FqUserPayDataTask",
		Run:      FqUserPayDataTask,
	}
	//FqUserPayTodayDataTask
	fqUserPayTodayDataTask := &model.TimeTask{
		FuncName: "FqUserPayTodayDataTask",
		Run:      FqUserPayTodayDataTask,
	}
	// ADXHotRankingYesterdayDataTask
	adxHotRankingYesterdayDataTask := &model.TimeTask{
		FuncName: "ADXHotRankingYesterdayDataTask",
		Run:      ADXHotRankingYesterdayDataTask,
	}
	// ADXHotRankingNowDataTask
	adxHotRankingNowDataTask := &model.TimeTask{
		FuncName: "ADXHotRankingNowDataTask",
		Run:      ADXHotRankingNowDataTask,
	}
	//SyncMpAdEventTask
	syncMpAdEventTask := &model.TimeTask{
		FuncName: "SyncMpAdEventTask",
		Run:      SyncMpAdEventTask,
	}
	//AdxPlayletDataTask
	adxPlayletDataTask := &model.TimeTask{
		FuncName: "AdxPlayletDataTask",
		Run:      AdxPlayletDataTask,
	}
	// PullAdDesignerMaterialReport
	adDesignerMaterialReport := &model.TimeTask{
		FuncName: "PullAdDesignerMaterialReport",
		Run:      PullAdDesignerMaterialReport,
	}

	//AdxHotRankingDetailDataTask
	adxHotRankingDetailDataTask := &model.TimeTask{
		FuncName: "AdxHotRankingDetailDataTask",
		Run:      AdxHotRankingDetailDataTask,
	}

	//AdxPlayletYesterdayDataTask
	adxPlayletYesterdayDataTask := &model.TimeTask{
		FuncName: "AdxPlayletYesterdayDataTask",
		Run:      AdxPlayletYesterdayDataTask,
	}
	//AdxPlayletNowDataTask
	adxPlayletNowDataTask := &model.TimeTask{
		FuncName: "AdxPlayletNowDataTask",
		Run:      AdxPlayletNowDataTask,
	}

	syncAdxMaterialTask := &model.TimeTask{
		FuncName: "SyncAdxMaterialTask",
		Run:      adxService.AdxMaterial().SyncAdxMaterialTask,
	}
	syncTodayAdxMaterialTask := &model.TimeTask{
		FuncName: "SyncTodayAdxMaterialTask",
		Run:      adxService.AdxMaterial().SyncTodayAdxMaterialTask,
	}
	syncAdxCreativeTask := &model.TimeTask{
		FuncName: "SyncAdxCreativeTask",
		Run:      adxService.AdxCreative().SyncAdxCreativeTask,
	}
	syncTodayAdxCreativeTask := &model.TimeTask{
		FuncName: "SyncTodayAdxCreativeTask",
		Run:      adxService.AdxCreative().SyncTodayAdxCreativeTask,
	}
	syncAdxProductTask := &model.TimeTask{
		FuncName: "SyncAdxProductTask",
		Run:      adxService.AdxProduct().SyncAdxProductTask,
	}
	syncAdxPublisherTask := &model.TimeTask{
		FuncName: "SyncAdxPublisherTask",
		Run:      adxService.AdxPublisher().SyncAdxPublisherTask,
	}
	syncAdxMediaTask := &model.TimeTask{
		FuncName: "SyncAdxMediaTask",
		Run:      adxService.AdxMedia().SyncAdxMediaTask,
	}
	//点众定时任务
	service.TaskList().AddTask(dzOrderInfoDataTask)
	service.TaskList().AddTask(adDesignerMaterialReport)
	// 添加同步 mp_ad_event
	service.TaskList().AddTask(syncMpAdEventTask)
	service.TaskList().AddTask(dzOrderInfoYesterdayDataTask)
	// 点众原生链接爬取任务
	service.TaskList().AddTask(dzNativeLinkCrawlTask)
	// adx 剧集 热力榜
	service.TaskList().AddTask(adxHotRankingDetailDataTask)
	service.TaskList().AddTask(adxHotRankingYesterdayDataTask)
	service.TaskList().AddTask(adxHotRankingNowDataTask)
	service.TaskList().AddTask(adxPlayletDataTask)
	service.TaskList().AddTask(adxPlayletYesterdayDataTask)
	service.TaskList().AddTask(adxPlayletNowDataTask)

	service.TaskList().AddTask(fqUserPayTodayDataTask)
	service.TaskList().AddTask(fqUserInfoDataTask)
	service.TaskList().AddTask(fqAnalyzeDataTask)
	service.TaskList().AddTask(fqUserPayDataTask)

	service.TaskList().AddTask(xTADYesterdayDataTask)
	service.TaskList().AddTask(xTADDetailDataTask)

	service.TaskList().AddTask(ksSeriesReportCoreData)
	service.TaskList().AddTask(ksADDataTask)

	service.TaskList().AddTask(setUserAdditionalVideoByTimeTask)
	service.TaskList().AddTask(updateMetricsTask)

	service.TaskList().AddTask(task1)
	service.TaskList().AddTask(task2)
	service.TaskList().AddTask(videoRechargeStatisticsTask)
	//==============开始{}渠道充值统计==============
	service.TaskList().AddTask(orderStatTask)
	service.TaskList().AddTask(channelStatTask)
	service.TaskList().AddTask(channelRechargeStatTask)
	service.TaskList().AddTask(channelRechargeStatTodayTask)
	service.TaskList().AddTask(noRechargeHaveCoinConsumeChannelStatTask)
	service.TaskList().AddTask(wxNoRechargeHaveCoinConsumeChannelStatTask)
	service.TaskList().AddTask(channelDetailStatTask)
	service.TaskList().AddTask(syncFqPromotionUrlTask)
	service.TaskList().AddTask(syncDzPromotionUrlTask)
	service.TaskList().AddTask(videoStatTask)
	//==============结束{}渠道充值统计==============
	//按小时去刷新当天的数据
	service.TaskList().AddTask(hourRefreshCoinTask)
	//==============开始刷新巨量Refresh Token==============
	service.TaskList().AddTask(refreshTokenTask)
	//==============结束刷新巨量Refresh Token==============

	// 发送订阅消息
	service.TaskList().AddTask(sendSignRemind)
	//==============用户看剧留存==============
	service.TaskList().AddTask(syncTheaterRetentionStat)
	service.TaskList().AddTask(syncTheaterRetentionStatToday)
	//==============结束用户看剧留存==============

	// 留存
	service.TaskList().AddTask(sAppletDepositRetentionStatistics)
	service.TaskList().AddTask(sAppletDepositRetentionStatisticsToday)

	service.TaskList().AddTask(checkUserOnlineTask)
	service.TaskList().AddTask(checkAlbumReviewResultTask)
	service.TaskList().AddTask(queryDpaAlbumStatusTask)
	service.TaskList().AddTask(sendNewTheaterDataReportTask)
	service.TaskList().AddTask(sendOutsourcedTheaterDataReportTask)
	service.TaskList().AddTask(sendBookDeliveryStatusReportTask)
	service.TaskList().AddTask(calcDistributionStatisticsTask)
	service.TaskList().AddTask(calcDistributionStatisticsTodayTask)
	service.TaskList().AddTask(initVideoPlayLinkTask)
	service.TaskList().AddTask(userWatchStatTask)
	service.TaskList().AddTask(runOrderPaySuccessRateTask) //支付成功率检查
	service.TaskList().AddTask(checkRefundResultTask)
	service.TaskList().AddTask(channelHourStatTask)
	service.TaskList().AddTask(calcAdCallbackStatisticsTask)
	service.TaskList().AddTask(calcAdCallbackAppletStatisticsTask)
	service.TaskList().AddTask(calcAdCallbackWxPitcherStatisticsTask)
	service.TaskList().AddTask(calcAdHourStatisticsTask)
	service.TaskList().AddTask(calcAdCallbackCostTask)
	service.TaskList().AddTask(theaterPlayRechargeStatTask)
	service.TaskList().AddTask(calcTaskStatisticsTask)
	service.TaskList().AddTask(synCouponCodeTask)                  //同步乘车劵数据
	service.TaskList().AddTask(couponStockNotifyTask)              //同步乘车劵数据
	service.TaskList().AddTask(calcAppletRechargeTask)             //统计小程序充值信息
	service.TaskList().AddTask(calcPitcherVideoRechargeTask)       //统计投手剧充值信息
	service.TaskList().AddTask(calcPitcherVideoYesterRechargeTask) //统计投手剧充值信息
	service.TaskList().AddTask(theaterPlayRechargeYesterStatTask)
	service.TaskList().AddTask(calcAppletRechargeYesterTask)
	service.TaskList().AddTask(orderCouponCodeRecoveryTask) //未支付订单乘车劵回收
	service.TaskList().AddTask(theaterHourTimesStatTask)
	service.TaskList().AddTask(theaterHotRankingStatTask)
	service.TaskList().AddTask(checkPanelBindTask)
	// 巨量广告任务
	service.TaskList().AddTask(syncAllAdvertiserInfoTask)
	service.TaskList().AddTask(calcAdAccountSubjectDataStatTask)
	service.TaskList().AddTask(calcTodayAdAccountSubjectDataStatTask)
	service.TaskList().AddTask(calcAdOptimizerDataStatTask)
	service.TaskList().AddTask(calcTodayAdOptimizerDataStatTask)
	service.TaskList().AddTask(syncAdAdvertiserAccountHourMetricsDataTask)
	service.TaskList().AddTask(createSignPayTask)
	// 快手短剧经营者任务
	service.TaskList().AddTask(calcAdOrderSettleStatTask)
	service.TaskList().AddTask(calcAdOrderDetailStatTask)
	service.TaskList().AddTask(calcAdSalerCopyRightStatTask)
	// adx任务
	service.TaskList().AddTask(syncAdxMaterialTask)
	service.TaskList().AddTask(syncTodayAdxMaterialTask)
	service.TaskList().AddTask(syncAdxCreativeTask)
	service.TaskList().AddTask(syncTodayAdxCreativeTask)
	service.TaskList().AddTask(syncAdxProductTask)
	service.TaskList().AddTask(syncAdxPublisherTask)
	service.TaskList().AddTask(syncAdxMediaTask)
	ctx := gctx.New()
	//自动执行已开启的任务
	jobs, err := service.SysJob().GetJobs(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}
	for _, job := range jobs {
		service.SysJob().JobStart(ctx, job)
	}
}
