package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/consumer"
	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/context"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/personal"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sPlatRules"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysAuthRule"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysDept"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysJob"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysJobLog"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysLoginLog"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysMqTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysNotice"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysNoticeRead"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysNoticeTemplate"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysOperLog"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysPost"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysRole"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUserOnline"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUserPageTableConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUserWxAdConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/timeTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/token"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/toolsGenTable"

	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/toolsGenTableColumn"
)
