package libUtils

import (
	"fmt"
	"github.com/mozillazg/go-pinyin"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"unicode"
)

// IsNullOrEmpty 判断是否为空
func IsNullOrEmpty(val string) bool {
	return len(val) == 0 || strings.Trim(val, " ") == ""
}

// FirstLetterOfPinYinToUpper 根据中文获取拼音大写
func FirstLetterOfPinYinToUpper(r string) string {
	if IsNullOrEmpty(r) {
		return ""
	}
	var a = pinyin.NewArgs()
	result := pinyin.Pinyin(r, a)
	if len(result) == 0 {
		return r[:1]
	}
	return strings.ToUpper(string(result[0][0][0]))
}

// FirstLetterOfPinYinToLower 根据中文获取第一个拼音小写首字母
func FirstLetterOfPinYinToLower(r string) string {
	if IsNullOrEmpty(r) {
		return ""
	}
	var a = pinyin.NewArgs()
	result := pinyin.Pinyin(r, a)
	if len(result) == 0 {
		return r[:1]
	}
	return strings.ToLower(string(result[0][0][0]))
}

// AllFirstLetterOfPinYinToLower 根据中文获所有取拼音小写首字母
func AllFirstLetterOfPinYinToLower(r string) string {
	if IsNullOrEmpty(r) {
		return ""
	}
	var a = pinyin.NewArgs()
	a.Style = pinyin.FirstLetter
	result := pinyin.Pinyin(r, a)
	if len(result) == 0 {
		return r[:1]
	}
	str := ""
	for _, array := range result {
		str += strings.ToLower(array[0])
	}
	return str
}

// GetNumberByStr 从字符串获取数字
func GetNumberByStr(val string) string {
	re := regexp.MustCompile("[0-9]+")
	array := re.FindAllString(val, -1)
	if len(array) > 0 {
		return array[len(array)-1]
	}
	return ""
}

// GetUrlName 兩位数字斜杠到.mp4之间的string
func GetUrlName(str string) string {
	// 定义正则表达式，匹配最后一个斜杠和句点之间的字符串
	re := regexp.MustCompile(`[\d]{2}/(.*)\.mp4`)
	match := re.FindStringSubmatch(str)
	if len(match) > 1 {
		return match[1]
	}
	return ""
}

// ExtractNumbers 返回字符串中的所有数字最后一个匹配的数字
func ExtractNumbers(str string) int {
	// 定义正则表达式，匹配数字
	re, err := regexp.Compile(`\d+`)
	if err != nil {
		return 0
	}
	// 在字符串中查找所有匹配的数字
	matches := re.FindAllString(str, -1)
	num, err := strconv.Atoi(matches[len(matches)-1])
	if err != nil {
		return 0
	}
	return num
}

func GetFileNameAndMiMe(val string) (string, string) {
	if IsNullOrEmpty(val) {
		return "", ""
	}
	strArray := strings.Split(val, ".")
	if len(strArray) > 0 {
		name := ""
		mime := strArray[len(strArray)-1]

		for i := 0; i < len(strArray)-1; i++ {
			name += strArray[i]
		}
		return name, mime
	}
	return "", ""
}

// HaveHanZi 查看当前字符串是否包含中文
func HaveHanZi(val string) bool {
	for _, item := range val {
		if unicode.Is(unicode.Han, item) {
			return true
		}
	}
	return false
}

// RemoveDuplicatesString  去除字符串中重复的元素
func RemoveDuplicatesString(inputStr string) string {
	numStrs := strings.Split(inputStr, ",")
	encountered := map[string]bool{}
	var result []string

	for _, num := range numStrs {
		num = strings.TrimSpace(num)
		if num != "" && !encountered[num] {
			encountered[num] = true
			result = append(result, num)
		}
	}

	uniqueStr := strings.Join(result, ",")
	return uniqueStr
}

// Encode url 不 encode http://
func EncodeURL(original string) string {
	if len(original) == 0 {
		return ""
	}

	u, err := url.Parse(original)
	if err != nil {
		return ""
	}

	// 分割路径为各个段并编码
	var encodedSegments []string
	segments := strings.Split(u.Path, "/")
	for _, seg := range segments {
		encodedSegments = append(encodedSegments, url.QueryEscape(seg))
	}
	u.Path = strings.Join(encodedSegments, "/")

	return fmt.Sprintf("%s://%s%s", u.Scheme, u.Host, u.Path)
}

func StringParsInt(str string) int {
	i, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return i
}

func StringToInt64(s string) int64 {
	result, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return 0
	}
	return result
}

// SnakeToCamel 将下划线分隔的字符串转换为驼峰格式
func SnakeToCamel(snake string) string {
	// 分割字符串
	parts := strings.Split(snake, "_")
	for i := 0; i < len(parts); i++ {
		if i == 0 {
			// 首字母小写处理
			parts[i] = strings.ToLower(parts[i])
		} else {
			// 首字母大写处理
			parts[i] = strings.Title(parts[i])
		}
	}
	// 拼接成驼峰格式
	return strings.Join(parts, "")
}

// CamelToSnake 将驼峰变成下划线
func CamelToSnake(s string) string {
	if len(s) == 0 {
		return ""
	}
	var result strings.Builder
	result.Grow(len(s) + 5) // 预分配空间，减少扩容开销

	for i, r := range s {
		if unicode.IsUpper(r) {
			// 首字母或前一个字符不是大写时，添加下划线
			if i > 0 && (i == 0 || !unicode.IsUpper(rune(s[i-1]))) {
				result.WriteByte('_')
			}
			result.WriteRune(unicode.ToLower(r))
		} else {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// StringParsFloat 计算消费除100000
func StringParsFloat(str string) float64 {
	f, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0
	}
	return DivideAndRound(f, 100000, 5, RoundHalfEven)
}
