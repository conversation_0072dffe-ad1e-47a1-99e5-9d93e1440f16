// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/controller/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adCallbackController struct {
	systemController.BaseController
}

var AdCallback = new(adCallbackController)

// OceanEngineCallback 列表
func (c *adCallbackController) OceanEngineCallback(ctx context.Context, req *ad.OceanEngineCallbackReq) (res *ad.OceanEngineCallbackRes, err error) {
	res = new(ad.OceanEngineCallbackRes)
	res.OceanEngineCallbackRes, err = service.AdCallback().OceanEngineCallback(ctx, req.OceanEngineCallbackReq)
	return
}

// KsAdCallBack ks回调
func (c *adCallbackController) KsAdCallBack(ctx context.Context, req *ad.KsAdCallBackReq) (res *ad.KsAdCallBackRes, err error) {
	res = new(ad.KsAdCallBackRes)
	res.KsAdCallBackRes, err = service.AdCallback().KsAdCallBack(ctx, req.KsAdCallBackReq)
	return
}

// DzCallback 点众回调
func (c *adCallbackController) DzCallback(ctx context.Context, req *ad.DzCallBackReq) (res *ad.DzCallBackRes, err error) {
	res = new(ad.DzCallBackRes)
	err = service.AdCallback().DzAdCallBack(ctx, req.DzAdCallBackReq)
	return
}

// OceanEngineXTCallback
func (c *adCallbackController) OceanEngineXTCallback(ctx context.Context, req *ad.OceanEngineXTCallbackReq) (res *ad.OceanEngineCallbackRes, err error) {
	res = new(ad.OceanEngineCallbackRes)
	res.OceanEngineCallbackRes, err = service.AdCallback().OceanEngineXTCallback(ctx, req.OceanEngineXTCallbackReq)
	return
}

// OceanEngineSubscribeValid 巨量订阅回调验证
func (c *adCallbackController) OceanEngineSubscribeValid(ctx context.Context, req *ad.OceanEngineSubscribeValidReq) (res *ad.OceanEngineSubscribeValidRes, err error) {
	res = new(ad.OceanEngineSubscribeValidRes)
	res.OceanEngineSubscribeValidRes, err = service.AdCallback().OceanEngineSubscribeValid(ctx, req.OceanEngineSubscribeValidReq)
	return
}

// OceanEngineSubscribe 巨量订阅回调
func (c *adCallbackController) OceanEngineSubscribe(ctx context.Context, req *ad.OceanEngineSubscribeReq) (res *ad.OceanEngineSubscribeRes, err error) {
	res = new(ad.OceanEngineSubscribeRes)
	res.OceanEngineSubscribeValidRes, err = service.AdCallback().OceanEngineSubscribe(ctx)
	return
}
