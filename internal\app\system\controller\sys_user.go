package controller

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/system"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model/entity"
	"github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
)

var (
	User = userController{}
)

type userController struct {
	BaseController
}

// GetUserMenus 获取用户菜单及按钮权限
func (c *userController) GetUserMenus(ctx context.Context, req *system.UserMenusReq) (res *system.UserMenusRes, err error) {
	var (
		permissions []string
		menuList    []*model.UserMenus
	)
	userId := service.Context().GetUserId(ctx)
	menuList, permissions, err = service.SysUser().GetAdminRules(ctx, userId)
	res = &system.UserMenusRes{
		MenuList:    menuList,
		Permissions: permissions,
	}
	return
}

func (c *userController) ChangeLogin(ctx context.Context, req *system.ChangeLoginReq) (res *system.UserLoginRes, err error) {
	// 首先判断当前请求的用户id 是否在当前登录用户的组织下
	loginUser := service.Context().GetLoginUser(ctx)
	if loginUser == nil {
		return nil, gerror.New("未获取到前用户登录信息！")
	}
	ids, isAdmin, _ := service.SysUser().GetContainUser(ctx, loginUser)
	//	additional, _ := sysService.SysUser().GetUserAdditional(ctx, int(loginUser.Id))
	//查询该用户所拥有的小程序
	if !isAdmin {
		var have = false
		//非admin,直接查投手下面的数据
		for i := 0; i < len(ids); i++ {
			if ids[i] == int(req.UserId) {
				have = true
				break
			}
		}
		if !have {
			return nil, gerror.New("当前登录用户没有切换登录权限！")
		}
	}
	// 清除当前用户登录信息
	err = service.GfToken().RemoveToken(ctx, service.GfToken().GetRequestToken(g.RequestFromCtx(ctx)))
	// 模拟新用户进行登录
	var (
		token       string
		permissions []string
		menuList    []*model.UserMenus
	)
	ip := libUtils.GetClientIp(ctx)
	userAgent := libUtils.GetUserAgent(ctx)
	user, err := service.SysUser().GetUserById(ctx, req.UserId)
	if err != nil {
		return
	}
	err = service.SysUser().UpdateLoginInfo(ctx, user.Id, ip)
	if err != nil {
		return
	}
	service.SysLoginLog().Invoke(gctx.New(), &model.LoginLogParams{
		Status:    1,
		Username:  user.UserName,
		Ip:        ip,
		UserAgent: userAgent,
		Msg:       "登录成功",
		Module:    "系统后台",
	})
	key := gconv.String(user.Id) + "-" + gmd5.MustEncryptString(user.UserName) + gmd5.MustEncryptString(user.UserPassword)
	if g.Cfg().MustGet(ctx, "gfToken.multiLogin").Bool() {
		key = gconv.String(user.Id) + "-" + gmd5.MustEncryptString(user.UserName) + gmd5.MustEncryptString(user.UserPassword+ip+userAgent)
	}
	//给当前用户添加角色相关信息 RoleId 和RoleName
	user.RoleInfo = service.SysUser().GetNowUserRole(ctx, user.Id)
	token, err = service.GfToken().GenerateToken(ctx, key, user)
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.New("登录失败，后端服务出现错误")
		return
	}
	//获取用户菜单数据
	menuList, permissions, err = service.SysUser().GetAdminRules(ctx, user.Id)
	if err != nil {
		return
	}
	res = &system.UserLoginRes{
		UserInfo:    user,
		Token:       token,
		MenuList:    menuList,
		Permissions: permissions,
	}
	//用户在线状态保存
	service.SysUserOnline().Invoke(gctx.New(), &model.SysUserOnlineParams{
		UserAgent: userAgent,
		Uuid:      gmd5.MustEncrypt(token),
		Token:     token,
		Username:  user.UserName,
		Ip:        ip,
	})
	return
}

// GetDesignerSupervisor
func (c *userController) GetDesignerSupervisor(ctx context.Context, req *system.GetDesignerSupervisorReq) (res *system.GetDesignerSupervisorRes, err error) {
	res = new(system.GetDesignerSupervisorRes)
	res.DesignerSupervisor, err = service.SysUser().GetDesignerSupervisor(ctx, req)
	return
}

// GetUserInfo
func (c *userController) GetUserInfo(ctx context.Context, req *system.GetUserInfoReq) (res *system.GetUserInfoRes, err error) {
	res = new(system.GetUserInfoRes)
	userInfo := service.Context().GetLoginUser(ctx)
	res.UserInfo = userInfo.LoginUserRes
	return
}

// List 用户列表
func (c *userController) List(ctx context.Context, req *system.UserSearchReq) (res *system.UserSearchRes, err error) {
	var (
		total    interface{}
		userList []*entity.SysUser
	)
	res = new(system.UserSearchRes)
	userInfo := service.Context().GetLoginUser(ctx)
	if userInfo == nil {
		return
	}
	if len(req.DeptId) == 0 {
		req.DeptId = gconv.String(userInfo.DeptId) //没有传使用用户
		//当是超管时，直接空
		if service.SysUser().IsSupperAdmin(ctx, userInfo.Id) || service.SysUser().IsAllData(ctx, userInfo) {
			req.DeptId = ""
		}
	}
	// 只能看自己
	if service.SysUser().IsOnlyMe(ctx, userInfo) {
		req.UserId = userInfo.Id
	}

	total, userList, err = service.SysUser().List(ctx, req)
	if err != nil || total == 0 {
		return
	}
	res.Total = total
	res.UserList, err = service.SysUser().GetUsersRoleDept(ctx, userList)
	var ids []int
	for _, user := range userList {
		ids = append(ids, int(user.Id))
	}
	userAdditionals, _ := service.SysUser().GetUserAdditionals(ctx, ids)
	miniInfoRes, err := service.SPlatRules().GetMiniInfoAllList(ctx, &comModel.PageReq{
		PageNum:  1,
		PageSize: 9999,
	})

	if len(userAdditionals) > 0 {
		for _, additional := range userAdditionals {
			for _, user := range res.UserList {
				if int(user.Id) == additional.UserId {
					appIds := strings.Split(additional.AppId, ",")
					for _, item := range miniInfoRes.List {
						for i, appId := range appIds {
							if item.AppId == appId {
								appIds[i] = fmt.Sprintf("%s(%s)", item.AppId, item.AppName)
							}
						}
					}
					user.AppId = strings.Join(appIds, ",")
					user.Percentage = additional.Percentage
					user.VideoIds = additional.VideoIds
					user.DeductOrderScale = additional.DeductOrderScale
					user.HaveNewDrama = additional.HaveNewDrama
				}
			}
		}
	}
	return
}

func (c *userController) AdList(ctx context.Context, adReq *system.AdUserSearchReq) (res *system.UserSearchRes, err error) {
	req := new(system.UserSearchReq)
	err = gconv.Struct(adReq, req)
	roleRes, err := service.SysRole().GetAdRoleListSearch(ctx, new(system.AdRoleListReq))
	req.RoleIds = make([]uint, 0)
	for _, role := range roleRes.List {
		req.RoleIds = append(req.RoleIds, role.Id)
	}
	if adReq.RoleId > 0 {
		// 判断req.RoleId 是否再 roleRes.List 中
		for _, v := range roleRes.List {
			if v.Id == req.RoleId {
				req.RoleIds = make([]uint, 0)
				req.RoleIds = append(req.RoleIds, v.Id)
				break
			}
		}
	} else if len(adReq.RoleIds) > 0 {
		// 取 adReq.RoleIds 和 req.RoleIds 的交集
		var tempRoleIds []uint
		for _, v := range adReq.RoleIds {
			for _, v1 := range req.RoleIds {
				if v == v1 {
					tempRoleIds = append(tempRoleIds, v)
				}
			}
		}
		req.RoleIds = tempRoleIds
	}

	return c.List(ctx, req)
}

// GetSuperAdminIds 获取配置的超级管理员Ids
func (c *userController) GetSuperAdminIds(ctx context.Context, req *system.GetSuperAdminIdsReq) (res *system.GetSuperAdminIdsRes, err error) {
	res, err = service.SysUser().GetSuperAdminIds(ctx)
	return
}

// GetParams 获取用户维护相关参数
func (c *userController) GetParams(ctx context.Context, req *system.UserGetParamsReq) (res *system.UserGetParamsRes, err error) {
	res = new(system.UserGetParamsRes)
	res.RoleList, err = service.SysRole().GetRoleList(ctx)
	if err != nil {
		return
	}
	res.Posts, err = service.SysPost().GetUsedPost(ctx)
	if err != nil {
		return
	}
	userId := service.Context().GetUserId(ctx)
	//判断是否超管
	if service.SysUser().IsSupperAdmin(ctx, userId) {
		//自己创建的角色可以被授权
		for _, v := range res.RoleList {
			res.RoleAccess = append(res.RoleAccess, v.Id)
		}
	} else {
		res.RoleAccess, err = service.SysUser().GetAdminRoleIds(ctx, userId, true)
		if err != nil {
			return
		}
		//自己创建的角色可以被授权
		for _, v := range res.RoleList {
			if v.CreatedBy == userId {
				res.RoleAccess = append(res.RoleAccess, v.Id)
			}
		}
	}
	return
}

// Add 添加用户
func (c *userController) Add(ctx context.Context, req *system.UserAddReq) (res *system.UserAddRes, err error) {
	err = service.SysUser().Add(ctx, req)
	return
}

// GetEditUser 获取修改用户信息
func (c *userController) GetEditUser(ctx context.Context, req *system.UserGetEditReq) (res *system.UserGetEditRes, err error) {
	res, err = service.SysUser().GetEditUser(ctx, req.Id)
	return
}

// Edit 修改用户
func (c *userController) Edit(ctx context.Context, req *system.UserEditReq) (res *system.UserEditRes, err error) {
	err = service.SysUser().Edit(ctx, req)
	return
}

// EditUserRole 修改用户角色
func (c *userController) EditUserRole(ctx context.Context, req *system.UserEditRoleReq) (res *system.UserEditRes, err error) {
	err = service.SysUser().EditUserRole(ctx, req.RoleIds, req.UserId)
	return
}

// ResetPwd 重置密码
func (c *userController) ResetPwd(ctx context.Context, req *system.UserResetPwdReq) (res *system.UserResetPwdRes, err error) {
	err = service.SysUser().ResetUserPwd(ctx, req)
	return
}

// SetStatus 修改用户状态
func (c *userController) SetStatus(ctx context.Context, req *system.UserStatusReq) (res *system.UserStatusRes, err error) {
	err = service.SysUser().ChangeUserStatus(ctx, req)
	return
}

// Delete 删除用户
func (c *userController) Delete(ctx context.Context, req *system.UserDeleteReq) (res *system.UserDeleteRes, err error) {
	err = service.SysUser().Delete(ctx, req.Ids)
	return
}

// AsyncUser 同步老系统用户到新系统
func (c *userController) AsyncUser(ctx context.Context, req *system.AsyncUserReq) (res *system.AsyncUserRes, err error) {
	err = service.SysUser().AsyncUser(ctx, req.Ids, req.DeptId)
	if err != nil {
		return &system.AsyncUserRes{
			Success: false,
			Message: err.Error()}, err
	}
	return &system.AsyncUserRes{
		Success: true,
	}, nil
}

// GetVideoAuthUserList 获取短剧授权用户列表
func (c *userController) GetVideoAuthUserList(ctx context.Context, req *system.VideoAuthUserListReq) (res *system.VideoAuthUserListRes, err error) {
	res = new(system.VideoAuthUserListRes)
	res.VideoAuthUserListRes, err = service.SysUser().GetVideoAuthUserList(ctx, req.VideoAuthUserListReq)
	return
}

// VideoAuth 短剧授权用户
func (c *userController) VideoAuth(ctx context.Context, req *system.VideoAuthReq) (res *system.VideoAuthRes, err error) {
	err = service.SysUser().VideoAuth(ctx, req.VideoAuthReq)
	return
}
