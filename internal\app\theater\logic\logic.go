package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/changduBookInfo"
	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/dzNativeLink"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sAliShh"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyAlbumAuth"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyAlbumBind"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyAlbumLibrary"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyAlbumPostReview"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyAuthAuthorize"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyCoverLibrary"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyEpisodeAuditInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sDyVideoLibrary"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sPitcherVideoRechargeStatistics"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sVideoChannelHourRechargeStatistics"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sVideoStatistics"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sWxAuthAuthorize"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/sWxDramaInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/tAppTheaterInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterCategory"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterCategoryMap"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterDetail"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterHotRanking"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterHourTimesStat"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterInfo"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterPlayRechargeStat"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterUserHourRetentionStat"

	_ "github.com/tiger1103/gfast/v3/internal/app/theater/logic/theaterUserRetentionStat"
)
