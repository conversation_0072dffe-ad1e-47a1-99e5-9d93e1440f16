package toutiao

import (
	"github.com/go-resty/resty/v2"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
)

type Client struct {
	ApiClient *APIClient
}

//func Init(cfg *Configuration) *Client {
//	client := &Client{
//		ApiClient: NewAPIClient(cfg),
//	}
//
//	return client
//}

func NewAPIClient(cfg *Configuration) *APIClient {
	if cfg.HTTPClient == nil {
		cfg.HTTPClient = resty.New()
	}

	c := &APIClient{}
	c.Cfg = cfg
	c.Oauth2RefreshTokenApi = new(Oauth2RefreshTokenApiService)
	c.Oauth2RefreshTokenApi.SetCfg(c.Cfg)

	c.MajordomoAdvertiserSelectV2Api = new(MajordomoAdvertiserSelectV2Service)
	c.MajordomoAdvertiserSelectV2Api.SetCfg(c.Cfg)

	c.ReportCustomGetV30Api = new(ReportCustomGetV30ApiService)
	c.ReportCustomGetV30Api.SetCfg(c.Cfg)

	c.Oauth2AccessTokenApiService = new(Oauth2AccessTokenApiService)
	c.Oauth2AccessTokenApiService.SetCfg(c.Cfg)

	c.Oauth2AdvertiserGetApiService = new(Oauth2AdvertiserGetApiService)
	c.Oauth2AdvertiserGetApiService.SetCfg(c.Cfg)

	c.AdvertiserInfoV2ApiService = new(AdvertiserInfoV2ApiService)
	c.AdvertiserInfoV2ApiService.SetCfg(c.Cfg)

	c.AdvertiserPublicInfoV2ApiService = new(AdvertiserPublicInfoV2ApiService)
	c.AdvertiserPublicInfoV2ApiService.SetCfg(c.Cfg)

	c.UserInfoV2ApiService = new(UserInfoV2ApiService)
	c.UserInfoV2ApiService.SetCfg(c.Cfg)

	c.CustomerCenterAdvertiserListV2ApiService = new(CustomerCenterAdvertiserListV2ApiService)
	c.CustomerCenterAdvertiserListV2ApiService.SetCfg(c.Cfg)

	c.AccountFundGetV3ApiService = new(AccountFundGetV3ApiService)
	c.AccountFundGetV3ApiService.SetCfg(c.Cfg)

	c.BusinessPlatformCompanyInfoGetV3ApiService = new(BusinessPlatformCompanyInfoGetV3ApiService)
	c.BusinessPlatformCompanyInfoGetV3ApiService.SetCfg(c.Cfg)

	c.AdvertiserFundDailyStatV2ApiService = new(AdvertiserFundDailyStatV2ApiService)
	c.AdvertiserFundDailyStatV2ApiService.SetCfg(c.Cfg)

	//c.ReportAdvertiserGetV2ApiService = new(ReportAdvertiserGetV2ApiService)
	//c.ReportAdvertiserGetV2ApiService.SetCfg(c.Cfg)

	c.AdvertiserBudgetGetV2ApiService = new(AdvertiserBudgetGetV2ApiService)
	c.AdvertiserBudgetGetV2ApiService.SetCfg(c.Cfg)

	c.ProjectListV3ApiService = new(ProjectListV3ApiService)
	c.ProjectListV3ApiService.SetCfg(c.Cfg)

	c.PromotionListV3ApiService = new(PromotionListV3ApiService)
	c.PromotionListV3ApiService.SetCfg(c.Cfg)

	c.ProjectStatusUpdateV3ApiService = new(ProjectStatusUpdateV3ApiService)
	c.ProjectStatusUpdateV3ApiService.SetCfg(c.Cfg)

	c.PromotionStatusUpdateV3ApiService = new(PromotionStatusUpdateV3ApiService)
	c.PromotionStatusUpdateV3ApiService.SetCfg(c.Cfg)

	c.AdvertiserBudgetUpdateV2ApiService = new(AdvertiserBudgetUpdateV2ApiService)
	c.AdvertiserBudgetUpdateV2ApiService.SetCfg(c.Cfg)

	c.ProjectBudgetUpdateV3ApiService = new(ProjectBudgetUpdateV3ApiService)
	c.ProjectBudgetUpdateV3ApiService.SetCfg(c.Cfg)

	c.ProjectCreateV3ApiService = new(ProjectCreateV3ApiService)
	c.ProjectCreateV3ApiService.SetCfg(c.Cfg)

	c.PromotionBudgetUpdateV3ApiService = new(PromotionBudgetUpdateV3ApiService)
	c.PromotionBudgetUpdateV3ApiService.SetCfg(c.Cfg)

	c.PromotionBidUpdateV3ApiService = new(PromotionBidUpdateV3ApiService)
	c.PromotionBidUpdateV3ApiService.SetCfg(c.Cfg)

	c.ToolsWechatAppletCreateV3ApiService = new(ToolsWechatAppletCreateV3ApiService)
	c.ToolsWechatAppletCreateV3ApiService.SetCfg(c.Cfg)

	c.ToolsWechatAppletListV3ApiService = new(ToolsWechatAppletListV3ApiService)
	c.ToolsWechatAppletListV3ApiService.SetCfg(c.Cfg)

	c.ToolsMicroAppCreateV3ApiService = new(ToolsMicroAppCreateV3ApiService)
	c.ToolsMicroAppCreateV3ApiService.SetCfg(c.Cfg)

	c.ToolsMicroAppListV3ApiService = new(ToolsMicroAppListV3ApiService)
	c.ToolsMicroAppListV3ApiService.SetCfg(c.Cfg)

	c.ToolsMicroAppUpdateV3ApiService = new(ToolsMicroAppUpdateV3ApiService)
	c.ToolsMicroAppUpdateV3ApiService.SetCfg(c.Cfg)

	c.ToolsAssetLinkListV3ApiService = new(ToolsAssetLinkListV3ApiService)
	c.ToolsAssetLinkListV3ApiService.SetCfg(c.Cfg)

	c.DmpCustomAudienceSelectV2ApiService = new(DmpCustomAudienceSelectV2ApiService)
	c.DmpCustomAudienceSelectV2ApiService.SetCfg(c.Cfg)

	c.DmpCustomAudienceReadV2ApiService = new(DmpCustomAudienceReadV2ApiService)
	c.DmpCustomAudienceReadV2ApiService.SetCfg(c.Cfg)

	c.DmpCustomAudiencePublishV2ApiService = new(DmpCustomAudiencePublishV2ApiService)
	c.DmpCustomAudiencePublishV2ApiService.SetCfg(c.Cfg)

	c.DmpCustomAudiencePushV2ApiService = new(DmpCustomAudiencePushV2ApiService)
	c.DmpCustomAudiencePushV2ApiService.SetCfg(c.Cfg)

	c.DmpCustomAudienceDeleteV2ApiService = new(DmpCustomAudienceDeleteV2ApiService)
	c.DmpCustomAudienceDeleteV2ApiService.SetCfg(c.Cfg)

	c.ToolsEventAllAssetsListGetV2ApiService = new(ToolsEventAllAssetsListGetV2ApiService)
	c.ToolsEventAllAssetsListGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsEventAllAssetsDetailGetV2ApiService = new(ToolsEventAllAssetsDetailGetV2ApiService)
	c.ToolsEventAllAssetsDetailGetV2ApiService.SetCfg(c.Cfg)

	c.EventManagerEventConfigsGetV2ApiService = new(EventManagerEventConfigsGetV2ApiService)
	c.EventManagerEventConfigsGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsSiteGetV2ApiService = new(ToolsSiteGetV2ApiService)
	c.ToolsSiteGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsSitePreviewV2ApiService = new(ToolsSitePreviewV2ApiService)
	c.ToolsSitePreviewV2ApiService.SetCfg(c.Cfg)

	c.ToolsSiteUpdateStatusV2ApiService = new(ToolsSiteUpdateStatusV2ApiService)
	c.ToolsSiteUpdateStatusV2ApiService.SetCfg(c.Cfg)

	c.ToolsSiteHandselV2ApiService = new(ToolsSiteHandselV2ApiService)
	c.ToolsSiteHandselV2ApiService.SetCfg(c.Cfg)
	c.AudiencePackageCreateV2ApiService = new(AudiencePackageCreateV2ApiService)
	c.AudiencePackageCreateV2ApiService.SetCfg(c.Cfg)

	c.AudiencePackageDeleteV2ApiService = new(AudiencePackageDeleteV2ApiService)
	c.AudiencePackageDeleteV2ApiService.SetCfg(c.Cfg)

	c.AudiencePackageGetV3ApiService = new(AudiencePackageGetV3ApiService)
	c.AudiencePackageGetV3ApiService.SetCfg(c.Cfg)

	c.ToolsEstimateAudienceV2ApiService = new(ToolsEstimateAudienceV2ApiService)
	c.ToolsEstimateAudienceV2ApiService.SetCfg(c.Cfg)

	c.ToolsAdminInfoGetV2ApiService = new(ToolsAdminInfoGetV2ApiService)
	c.ToolsAdminInfoGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsInterestActionActionCategoryV2ApiService = new(ToolsInterestActionActionCategoryV2ApiService)
	c.ToolsInterestActionActionCategoryV2ApiService.SetCfg(c.Cfg)

	c.ToolsInterestActionInterestCategoryV2ApiService = new(ToolsInterestActionInterestCategoryV2ApiService)
	c.ToolsInterestActionInterestCategoryV2ApiService.SetCfg(c.Cfg)

	c.ToolsInterestActionActionKeywordV2ApiService = new(ToolsInterestActionActionKeywordV2ApiService)
	c.ToolsInterestActionActionKeywordV2ApiService.SetCfg(c.Cfg)

	c.ToolsInterestActionKeywordSuggestV2ApiService = new(ToolsInterestActionKeywordSuggestV2ApiService)
	c.ToolsInterestActionKeywordSuggestV2ApiService.SetCfg(c.Cfg)

	c.ToolsInterestActionInterestKeywordV2ApiService = new(ToolsInterestActionInterestKeywordV2ApiService)
	c.ToolsInterestActionInterestKeywordV2ApiService.SetCfg(c.Cfg)

	c.ToolsInterestActionActionId2wordV2ApiService = new(ToolsInterestActionActionId2wordV2ApiService)
	c.ToolsInterestActionActionId2wordV2ApiService.SetCfg(c.Cfg)

	c.ToolsAwemeMultiLevelCategoryGetV2ApiService = new(ToolsAwemeMultiLevelCategoryGetV2ApiService)
	c.ToolsAwemeMultiLevelCategoryGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsAwemeCategoryTopAuthorGetV2ApiService = new(ToolsAwemeCategoryTopAuthorGetV2ApiService)
	c.ToolsAwemeCategoryTopAuthorGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsAwemeInfoSearchGetV2ApiService = new(ToolsAwemeInfoSearchGetV2ApiService)
	c.ToolsAwemeInfoSearchGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsAwemeSimilarAuthorSearchGetV2ApiService = new(ToolsAwemeSimilarAuthorSearchGetV2ApiService)
	c.ToolsAwemeSimilarAuthorSearchGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsQuotaGetV2ApiService = new(ToolsQuotaGetV2ApiService)
	c.ToolsQuotaGetV2ApiService.SetCfg(c.Cfg)

	c.ToolsOrangeSiteGetV3ApiService = new(ToolsOrangeSiteGetV3ApiService)
	c.ToolsOrangeSiteGetV3ApiService.SetCfg(c.Cfg)

	c.AgentInfoGetV2ApiService = new(AgentInfoGetV2ApiService)
	c.AgentInfoGetV2ApiService.SetCfg(c.Cfg)

	c.AgentAdvertiserSelectV2ApiService = new(AgentAdvertiserSelectV2ApiService)
	c.AgentAdvertiserSelectV2ApiService.SetCfg(c.Cfg)

	// ----------------------------- 素材相关 -------------
	// 获取同主体下广告主图片素材
	c.FileImageAdGetV2ApiService = new(FileImageAdGetV2ApiService)
	c.FileImageAdGetV2ApiService.SetCfg(c.Cfg)
	// 获取同主体下广告主视频素材
	c.FileVideoAdGetV2ApiService = new(FileVideoAdGetV2ApiService)
	c.FileVideoAdGetV2ApiService.SetCfg(c.Cfg)
	// 上传广告视频 (同步)
	c.FileVideoAdV2ApiService = new(FileVideoAdV2ApiService)
	c.FileVideoAdV2ApiService.SetCfg(c.Cfg)
	// 上传广告图片
	c.FileImageAdV2ApiService = new(FileImageAdV2ApiService)
	c.FileImageAdV2ApiService.SetCfg(c.Cfg)
	// 异步上传视频文件
	c.FileUploadTaskCreateV2ApiService = new(FileUploadTaskCreateV2ApiService)
	c.FileUploadTaskCreateV2ApiService.SetCfg(c.Cfg)
	// 获取异步上传视频文件结果
	c.FileVideoUploadTaskListV2ApiService = new(FileVideoUploadTaskListV2ApiService)
	c.FileVideoUploadTaskListV2ApiService.SetCfg(c.Cfg)
	// 素材推送
	c.FileMaterialBindV2ApiService = new(FileMaterialBindV2ApiService)
	c.FileMaterialBindV2ApiService.SetCfg(c.Cfg)
	// 获取图片素材
	c.FileImageGetV2ApiService = new(FileImageGetV2ApiService)
	c.FileImageGetV2ApiService.SetCfg(c.Cfg)
	//FileVideoGetV2ApiService 获取视频素材
	c.FileVideoGetV2ApiService = new(FileVideoGetV2ApiService)
	c.FileVideoGetV2ApiService.SetCfg(c.Cfg)

	c.AdvertiserAvatarUploadV2ApiService = new(AdvertiserAvatarUploadV2ApiService)
	c.AdvertiserAvatarUploadV2ApiService.SetCfg(c.Cfg)

	c.AdvertiserAvatarSubmitV2ApiService = new(AdvertiserAvatarSubmitV2ApiService)
	c.AdvertiserAvatarSubmitV2ApiService.SetCfg(c.Cfg)

	c.AdvertiserAvatarGetV2ApiService = new(AdvertiserAvatarGetV2ApiService)
	c.AdvertiserAvatarGetV2ApiService.SetCfg(c.Cfg)

	c.PromotionCreateV30ApiService = new(PromotionCreateV30ApiService)
	c.PromotionCreateV30ApiService.SetCfg(c.Cfg)

	c.DpaProductAvailablesV2ApiService = new(DpaProductAvailablesV2ApiService)
	c.DpaProductAvailablesV2ApiService.SetCfg(c.Cfg)

	c.DpaDetailGetV2ApiService = new(DpaDetailGetV2ApiService)
	c.DpaDetailGetV2ApiService.SetCfg(c.Cfg)

	c.DpaProductDetailGetV2ApiService = new(DpaProductDetailGetV2ApiService)
	c.DpaProductDetailGetV2ApiService.SetCfg(c.Cfg)

	c.DpaClueProductListV2ApiService = new(DpaClueProductListV2ApiService)
	c.DpaClueProductListV2ApiService.SetCfg(c.Cfg)

	c.DpaClueProductDetailV2ApiService = new(DpaClueProductDetailV2ApiService)
	c.DpaClueProductDetailV2ApiService.SetCfg(c.Cfg)

	c.ToolsAwemeAuthListV2ApiService = new(ToolsAwemeAuthListV2ApiService)
	c.ToolsAwemeAuthListV2ApiService.SetCfg(c.Cfg)

	c.NativeAnchorDeleteV30ApiService = new(NativeAnchorDeleteV30ApiService)
	c.NativeAnchorDeleteV30ApiService.SetCfg(c.Cfg)

	c.NativeAnchorCreateV30ApiService = new(NativeAnchorCreateV30ApiService)
	c.NativeAnchorCreateV30ApiService.SetCfg(c.Cfg)

	c.NativeAnchorUpdateV30ApiService = new(NativeAnchorUpdateV30ApiService)
	c.NativeAnchorUpdateV30ApiService.SetCfg(c.Cfg)

	c.NativeAnchorQrcodePreviewGetV30ApiService = new(NativeAnchorQrcodePreviewGetV30ApiService)
	c.NativeAnchorQrcodePreviewGetV30ApiService.SetCfg(c.Cfg)

	c.NativeAnchorGetV30ApiService = new(NativeAnchorGetV30ApiService)
	c.NativeAnchorGetV30ApiService.SetCfg(c.Cfg)

	c.NativeAnchorGetDetailV30ApiService = new(NativeAnchorGetDetailV30ApiService)
	c.NativeAnchorGetDetailV30ApiService.SetCfg(c.Cfg)

	//AssetsCreativeComponentUpdateV2ApiService
	c.AssetsCreativeComponentUpdateV2ApiService = new(AssetsCreativeComponentUpdateV2ApiService)
	c.AssetsCreativeComponentUpdateV2ApiService.SetCfg(c.Cfg)
	//AssetsCreativeComponentGetV2ApiService
	c.AssetsCreativeComponentGetV2ApiService = new(AssetsCreativeComponentGetV2ApiService)
	c.AssetsCreativeComponentGetV2ApiService.SetCfg(c.Cfg)
	//AssetsCreativeComponentCreateV2ApiService
	c.AssetsCreativeComponentCreateV2ApiService = new(AssetsCreativeComponentCreateV2ApiService)
	c.AssetsCreativeComponentCreateV2ApiService.SetCfg(c.Cfg)
	//获取可用优化目标（巨量广告升级版）
	c.EventManagerOptimizedGoalGetV2V30ApiService = new(EventManagerOptimizedGoalGetV2V30ApiService)
	c.EventManagerOptimizedGoalGetV2V30ApiService.SetCfg(c.Cfg)
	// 获取可用深度优化方式（广告投放升级版）
	c.EventManagerDeepBidTypeGetV30ApiService = new(EventManagerDeepBidTypeGetV30ApiService)
	c.EventManagerDeepBidTypeGetV30ApiService.SetCfg(c.Cfg)

	//*StarDemandOmGetChallengeItemsDataV2ApiService
	c.StarDemandOmGetChallengeItemsDataV2ApiService = new(StarDemandOmGetChallengeItemsDataV2ApiService)
	c.StarDemandOmGetChallengeItemsDataV2ApiService.SetCfg(c.Cfg)
	//*StarDemandOmGetChallengeV2ApiService
	c.StarDemandOmGetChallengeV2ApiService = new(StarDemandOmGetChallengeV2ApiService)
	c.StarDemandOmGetChallengeV2ApiService.SetCfg(c.Cfg)
	//*StarDemandOmGetDemandListV2ApiService
	c.StarDemandOmGetDemandListV2ApiService = new(StarDemandOmGetDemandListV2ApiService)
	c.StarDemandOmGetDemandListV2ApiService.SetCfg(c.Cfg)
	//EventManagerEventsCreateV2ApiService
	c.EventManagerEventsCreateV2ApiService = new(EventManagerEventsCreateV2ApiService)
	c.EventManagerEventsCreateV2ApiService.SetCfg(c.Cfg)
	//EventManagerAssetsCreateV2ApiService
	c.EventManagerAssetsCreateV2ApiService = new(EventManagerAssetsCreateV2ApiService)
	c.EventManagerAssetsCreateV2ApiService.SetCfg(c.Cfg)
	//EventManagerAvailableEventsGetV2ApiService
	c.EventManagerAvailableEventsGetV2ApiService = new(EventManagerAvailableEventsGetV2ApiService)
	c.EventManagerAvailableEventsGetV2ApiService.SetCfg(c.Cfg)
	// 更新代理商广告主信息
	c.AgentAdvertiserUpdateV2ApiService = new(AgentAdvertiserUpdateV2ApiService)
	c.AgentAdvertiserUpdateV2ApiService.SetCfg(c.Cfg)
	//ToolsSiteCreateV2ApiService
	c.ToolsSiteCreateV2ApiService = new(ToolsSiteCreateV2ApiService)
	c.ToolsSiteCreateV2ApiService.SetCfg(c.Cfg)
	//*DpaAlbumStatusGetApiService
	c.DpaAlbumStatusGetApiService = new(DpaAlbumStatusGetApiService)
	c.DpaAlbumStatusGetApiService.SetCfg(c.Cfg)
	//*DpaAlbumCreateApiService
	c.DpaAlbumCreateApiService = new(DpaAlbumCreateApiService)
	c.DpaAlbumCreateApiService.SetCfg(c.Cfg)
	//Oauth2AppAccessTokenApiService
	c.GetOauth2AppAccessTokenApiService = new(GetOauth2AppAccessTokenApiService)
	c.GetOauth2AppAccessTokenApiService.SetCfg(c.Cfg)
	return c
}
