// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/logic/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	oceanModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	oceanService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdBatchTask(New())
}

func New() service.IAdBatchTask {
	return &sAdBatchTask{}
}

type sAdBatchTask struct{}

func (s *sAdBatchTask) List(ctx context.Context, req *model.AdBatchTaskSearchReq) (listRes *model.AdBatchTaskSearchRes, err error) {
	listRes = new(model.AdBatchTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdBatchTask.Ctx(ctx).WithAll().As("ad").
			LeftJoin("sys_user u", "ad.user_id = u.id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdBatchTask.Columns().UserId, userIds)
		}
		if req.MediaType != "" {
			m = m.Where("ad."+dao.AdBatchTask.Columns().MediaType+" = ?", gconv.Int(req.MediaType))
		}
		if req.OptObject != "" {
			m = m.Where("ad."+dao.AdBatchTask.Columns().OptObject+" = ?", gconv.Int(req.OptObject))
		}
		if req.OptType != "" {
			m = m.Where("ad."+dao.AdBatchTask.Columns().OptType+" = ?", gconv.Int(req.OptType))
		}
		if req.OptStatus != "" {
			m = m.Where("ad."+dao.AdBatchTask.Columns().OptStatus+" = ?", req.OptStatus)
		}
		if req.StartTime != "" && req.EndTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.WhereGTE("ad."+dao.AdBatchTask.Columns().CreatedAt, dayStartTime)
			m = m.WhereLTE("ad."+dao.AdBatchTask.Columns().CreatedAt, dayEndTime)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "ad.id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdBatchTaskListRes
		err = m.Fields("ad.id as id").
			Fields("ad.task_id as taskId").
			Fields("ad.task_name as taskName").
			Fields("ad.media_type as mediaType").
			Fields("ad.opt_object as optObject").
			Fields("ad.opt_type as optType").
			Fields("ad.opt_num as optNum").
			Fields("ad.opt_status as optStatus").
			Fields("ad.success_num as successNum").
			Fields("ad.fail_num as failNum").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ad.created_at as createdAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdBatchTaskListRes, len(res))
		for k, v := range res {
			v.MediaTypeName = commonConsts.MediaTypeMapping[v.MediaType]
			v.OptTypeName = commonConsts.OptTypeMapping[v.OptType]
			v.OptObjectName = commonConsts.OptObjectMapping[v.OptObject]
			v.OptStatusName = commonConsts.OptStatusMapping[v.OptStatus]
			listRes.List[k] = v
		}
	})
	return
}

func (s *sAdBatchTask) GetById(ctx context.Context, id int64) (res *model.AdBatchTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdBatchTask.Ctx(ctx).WithAll().Where(dao.AdBatchTask.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdBatchTask) GetByTaskId(ctx context.Context, taskId string) (res *model.AdBatchTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdBatchTask.Ctx(ctx).WithAll().Where(dao.AdBatchTask.Columns().TaskId, taskId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdBatchTask) Add(ctx context.Context, req *model.AdBatchTaskAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdBatchTask.Ctx(ctx).Insert(do.AdBatchTask{
			TaskId:     req.TaskId,
			TaskName:   req.TaskName,
			MediaType:  req.MediaType,
			OptObject:  req.OptObject,
			OptType:    req.OptType,
			OptNum:     req.OptNum,
			OptStatus:  req.OptStatus,
			SuccessNum: req.SuccessNum,
			FailNum:    req.FailNum,
			UserId:     req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdBatchTask) Edit(ctx context.Context, req *model.AdBatchTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		adBatchTask := do.AdBatchTask{
			UpdatedAt: gtime.Now(),
		}
		if req.TaskId != "" {
			adBatchTask.TaskId = req.TaskId
		}
		if req.TaskName != "" {
			adBatchTask.TaskName = req.TaskName
		}
		if req.MediaType > 0 {
			adBatchTask.MediaType = req.MediaType
		}
		if req.OptObject > 0 {
			adBatchTask.OptObject = req.OptObject
		}
		if req.OptType > 0 {
			adBatchTask.OptType = req.OptType
		}
		if req.OptNum > 0 {
			adBatchTask.OptNum = req.OptNum
		}
		if req.OptStatus != "" {
			adBatchTask.OptStatus = req.OptStatus
		}
		if req.SuccessNum > 0 {
			adBatchTask.SuccessNum = req.SuccessNum
		}
		if req.FailNum > 0 {
			adBatchTask.FailNum = req.FailNum
		}
		if req.UserId > 0 {
			adBatchTask.UserId = req.UserId
		}
		_, err = dao.AdBatchTask.Ctx(ctx).WherePri(req.Id).Update(adBatchTask)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdBatchTask) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdBatchTask.Ctx(ctx).Delete(dao.AdBatchTask.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// BatchEditAdvertiser 批量修改账户信息
func (s *sAdBatchTask) BatchEditAdvertiser(ctx context.Context, req *model.AdBatchTaskEditAdvertiserReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userId := sysService.Context().GetUserId(ctx)
		if len(req.AdvertiserList) > 100 {
			liberr.ErrIsNil(ctx, errors.New("最多只能修改100个账户！"))
		}
		var taskId = libUtils.GenerateID()
		taskName := fmt.Sprintf("%s-%s-%s-%s",
			commonConsts.OptObjectMapping[commonConsts.OptObjectAdvertiser],
			commonConsts.OptTypeMapping[req.OptType],
			gtime.Now().Format("ymd"),
			gtime.Now().Format("His"))
		err1 := s.Add(ctx, &model.AdBatchTaskAddReq{
			TaskId:     taskId,
			TaskName:   taskName,
			MediaType:  commonConsts.MediaOcean,
			OptObject:  commonConsts.OptObjectAdvertiser,
			OptType:    req.OptType,
			OptNum:     len(req.AdvertiserList),
			OptStatus:  commonConsts.OptStatusExecuting,
			SuccessNum: 0,
			FailNum:    0,
			UserId:     userId,
		})
		liberr.ErrIsNil(ctx, err1, "添加任务失败")
		libUtils.SafeGo(func() {
			innerCtx, cancel := context.WithCancel(context.Background())
			defer cancel()
			var successNum int
			var failNum int
			var batchAddReq = make([]*model.AdBatchTaskDetailAddReq, 0)
			var fileBytes []byte
			var fileName string
			if req.File != nil {
				fp, _ := req.File.Open()
				defer fp.Close()
				fileBytes = make([]byte, req.File.Size)
				_, _ = fp.Read(fileBytes)
				fileName = req.File.Filename
			}
			for k, v := range req.AdvertiserList {
				taskDetail := &model.AdBatchTaskDetailAddReq{
					TaskId:                   taskId,
					SerialNumber:             k + 1,
					AdvertiserId:             v.AdvertiserId,
					OriginalAdvertiserName:   v.OriginalAdvertiserName,
					NewAdvertiserName:        v.NewAdvertiserName,
					OriginalAdvertiserRemark: v.OriginalAdvertiserRemark,
					NewAdvertiserRemark:      v.NewAdvertiserRemark,
				}
				var errMsg string
				var optStatus = commonConsts.OptResultSuccess
				tokenRes, err2 := oceanService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(innerCtx, v.AdvertiserId)
				if err2 != nil {
					optStatus = commonConsts.OptResultFail
					errMsg = commonConsts.ErrMsgGetAccessToken
					failNum++
				} else if tokenRes.MajordomoType != commonConsts.AGENT && req.OptType != commonConsts.OptTypeEditAdvertiserAvatar {
					optStatus = commonConsts.OptResultFail
					if req.OptType == commonConsts.OptTypeEditAdvertiserName {
						errMsg = commonConsts.ErrMsgEditAdvertiserName
					} else if req.OptType == commonConsts.OptTypeEditAdvertiserRemark {
						errMsg = commonConsts.ErrMsgEditAdvertiserRemark
					}
					failNum++
				} else {
					// 修改账户名称和备注
					if req.OptType == commonConsts.OptTypeEditAdvertiserName || req.OptType == commonConsts.OptTypeEditAdvertiserRemark {
						agentAdvertiserUpdateReq := models.AgentAdvertiserUpdateV2Request{
							AdvertiserId: gconv.Int64(v.AdvertiserId),
						}
						if v.NewAdvertiserName != "" {
							agentAdvertiserUpdateReq.Name = &v.NewAdvertiserName
						}
						if v.NewAdvertiserRemark != "" {
							agentAdvertiserUpdateReq.Note = &v.NewAdvertiserRemark
						}
						advertiserUpdateRes, err3 := advertiser.GetToutiaoApiClient().AgentAdvertiserUpdateV2ApiService.
							AccessToken(tokenRes.AccessToken).
							AgentAdvertiserUpdateV2Request(agentAdvertiserUpdateReq).Do()
						if err3 != nil {
							optStatus = commonConsts.OptResultFail
							errMsg = err3.Error()
							failNum++
						} else {
							g.Log().Infof(innerCtx, "修改账户名称或头像结果：%v", advertiserUpdateRes.Data)
							successNum++
						}
					} else if req.OptType == commonConsts.OptTypeEditAdvertiserAvatar {
						avatarUploadRes, err3 := advertiser.GetToutiaoApiClient().AdvertiserAvatarUploadV2ApiService.
							AccessToken(tokenRes.AccessToken).
							AdvertiserAvatarUploadV2Request(models.AdvertiserAvatarUploadV2Request{
								AdvertiserId: gconv.Int64(v.AdvertiserId),
								ImageFile: &models.FormFileInfo{
									FileBytes: fileBytes,
									FileName:  fileName,
								},
							}).Do()
						if err3 != nil {
							optStatus = commonConsts.OptResultFail
							errMsg = err3.Error()
							failNum++
						} else {
							avatarSubmitRes, err4 := advertiser.GetToutiaoApiClient().AdvertiserAvatarSubmitV2ApiService.
								AccessToken(tokenRes.AccessToken).
								AdvertiserUpdateBudgetV2Request(models.AdvertiserAvatarSubmitV2Request{
									AdvertiserId: gconv.Int64(v.AdvertiserId),
									ImageId:      *avatarUploadRes.Data.ImageId,
								}).Do()
							if err4 != nil {
								optStatus = commonConsts.OptResultFail
								errMsg = err4.Error()
								failNum++
							} else {
								g.Log().Infof(innerCtx, "修改账户头像结果：%v", avatarSubmitRes.Data)
								successNum++
							}
						}
					}
				}
				taskDetail.OptResult = optStatus
				taskDetail.ErrMsg = errMsg
				batchAddReq = append(batchAddReq, taskDetail)
			}
			adBatchTaskInfo, _ := s.GetByTaskId(innerCtx, taskId)
			err3 := s.Edit(innerCtx, &model.AdBatchTaskEditReq{
				Id:         adBatchTaskInfo.Id,
				SuccessNum: successNum,
				FailNum:    failNum,
				OptStatus:  commonConsts.OptStatusCompleted,
			})
			if err3 != nil {
				g.Log().Errorf(innerCtx, "修改任务失败: %v", err3)
				return
			}
			err4 := service.AdBatchTaskDetail().BatchAdd(innerCtx, batchAddReq)
			if err4 != nil {
				g.Log().Errorf(innerCtx, "添加任务详情失败: %v", err4)
				return
			}
			// 修改成功后更新广告主信息
			var updateData = make([]*oceanModel.AdAdvertiserAccountEditReq, 0)
			for _, v := range batchAddReq {
				if v.OptResult != commonConsts.OptResultSuccess {
					continue
				}
				item := &oceanModel.AdAdvertiserAccountEditReq{
					AdvertiserId: v.AdvertiserId,
				}
				switch req.OptType {
				case commonConsts.OptTypeEditAdvertiserName:
					item.AdvertiserNick = v.NewAdvertiserName
				case commonConsts.OptTypeEditAdvertiserRemark:
					item.Remark = v.NewAdvertiserRemark
				}
				updateData = append(updateData, item)
			}
			err5 := oceanService.AdAdvertiserAccount().BatchEdit(innerCtx, updateData)
			if err5 != nil {
				g.Log().Errorf(innerCtx, "修改广告主信息失败: %v", err5)
			}
		})
	})
	return
}
