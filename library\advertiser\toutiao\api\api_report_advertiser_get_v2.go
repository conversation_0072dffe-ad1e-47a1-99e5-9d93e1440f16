/*
Oceanengine Open Api
巨量引擎开放平台 Open Api
*/
// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api
//
//import (
//	"context"
//	"encoding/json"
//	"errors"
//	"fmt"
//	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
//	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
//	"github.com/tiger1103/gfast/v3/library/libUtils"
//)
//
//// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service
//type ReportAdvertiserGetV2ApiService struct {
//	ctx     context.Context
//	cfg     *conf.Configuration
//	token   string
//	Request *ApiOpenApi2ReportAdvertiserGetGetRequest
//}

type ApiOpenApi2ReportAdvertiserGetGetRequest struct {
	AdvertiserId int64
	StartDate    string
	EndDate      string
	Fields       []string
	Page         int64
	PageSize     int64
}
//
//func (r *ReportAdvertiserGetV2ApiService) SetCfg(cfg *conf.Configuration) *ReportAdvertiserGetV2ApiService {
//	r.cfg = cfg
//	return r
//}
//
//func (r *ReportAdvertiserGetV2ApiService) SetRequest(accountFundGetV3Request ApiOpenApi2ReportAdvertiserGetGetRequest) *ReportAdvertiserGetV2ApiService {
//	r.Request = &accountFundGetV3Request
//	return r
//}
//
//func (r *ReportAdvertiserGetV2ApiService) AccessToken(accessToken string) *ReportAdvertiserGetV2ApiService {
//	r.token = accessToken
//	return r
//}
//
//// Do 执行HttpClient请求 外层无需关注是get还是post
//func (r *ReportAdvertiserGetV2ApiService) Do() (data *models.ReportAdvertiserGetV2Response, err error) {
//	localBasePath := r.cfg.GetBasePath()
//	localVarPath := localBasePath + "/open_api/2/report/advertiser/get/"
//	localVarQueryParams := map[string]string{}
//	if r.Request == nil {
//		return nil, errors.New("请求参数不能为空")
//	}
//	if r.Request.AdvertiserId > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
//	}
//	if len(r.Request.StartDate) > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "start_date", r.Request.StartDate)
//	}
//	if len(r.Request.EndDate) > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "end_date", r.Request.EndDate)
//	}
//	if r.Request.Fields != nil {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "fields", r.Request.Fields)
//	}
//	if r.Request.Page > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
//	}
//	if r.Request.PageSize > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
//	}
//
//	response, err := r.cfg.HTTPClient.R().
//		SetHeader("Content-Type", "application/json").
//		SetHeader("Access-Token", r.token).
//		SetQueryParams(localVarQueryParams).
//		SetResult(&models.ReportAdvertiserGetV2Response{}).
//		Get(localVarPath)
//	if err != nil {
//		return nil, err
//	}
//	resp := new(models.ReportAdvertiserGetV2Response)
//	// 将 JSON 响应解码到结构体中
//	err = json.Unmarshal(response.Body(), &resp)
//	if err != nil {
//		return nil, errors.New(fmt.Sprintf("/open_api/2/report/advertiser/get/解析响应出错: %v\n", err))
//	}
//	if *resp.Code == 0 && resp.Data != nil {
//		return resp, nil
//	} else {
//		return resp, errors.New(*resp.Message)
//	}
//}

// Execute executes the request
//fund_daily_stat
//	@return ReportAdvertiserGetV2Response
//func (a *ReportAdvertiserGetV2ApiService) getExecute(r *ApiOpenApi2ReportAdvertiserGetGetRequest) (*ReportAdvertiserGetV2Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodGet
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *ReportAdvertiserGetV2Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/2/report/advertiser/get/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//
//	if r.advertiserId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
//	}
//	if r.endDate != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "end_date", r.endDate)
//	}
//	if r.fields != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "fields", r.fields)
//	}
//	if r.filtering != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.filtering)
//	}
//	if r.groupBy != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "group_by", r.groupBy)
//	}
//	if r.orderField != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "order_field", r.orderField)
//	}
//	if r.orderType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "order_type", r.orderType)
//	}
//	if r.page != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "page", r.page)
//	}
//	if r.pageSize != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.pageSize)
//	}
//	if r.startDate != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "start_date", r.startDate)
//	}
//	if r.timeGranularity != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "time_granularity", r.timeGranularity)
//	}
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
