// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/dao/internal/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzNativeLinkDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzNativeLinkDao struct {
	table   string              // Table is the underlying table name of the DAO.
	group   string              // Group is the database configuration group name of current DAO.
	columns DzNativeLinkColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzNativeLinkColumns defines and stores column names for table dz_native_link.
type DzNativeLinkColumns struct {
	Id                string // 主键ID
	ProjectId         string // 项目ID
	BookId            string // 书籍ID
	BookName          string // 书籍名称
	DyBookId          string // 抖音书籍ID
	AccountUserName   string // 账户用户名
	DistributorId     string // 分销商ID
	DistributorName   string // 分销商名称
	ChannelId         string // 渠道ID
	PurchasePanelId   string // 购买面板ID
	PurchasePanelName string // 购买面板名称
	PurchasePanelJson string // 购买面板JSON配置
	AdvertiseLink     string // 广告推广链接
	TaskStatus        string // 任务状态：0-待处理，1-已创建，2-执行中，3-已完成，4-失败
	Response          string // 接口响应信息
	CreateTime        string // 创建时间
	UpdateTime        string // 更新时间
	BookStatus        string // 书籍状态：1-未上架，2-已上架，3-已下架
	BookType          string // 书籍类型：1-短剧，2-小说
	Connect           string // 连接状态：0-未连接，1-已连接
	PutTimeText       string // 投放时间文本
	Type              string // 类型 1-付费 2-免费
}

var dzNativeLinkColumns = DzNativeLinkColumns{
	Id:                "id",
	ProjectId:         "project_id",
	BookId:            "book_id",
	BookName:          "book_name",
	DyBookId:          "dy_book_id",
	AccountUserName:   "account_user_name",
	DistributorId:     "distributor_id",
	DistributorName:   "distributor_name",
	ChannelId:         "channel_id",
	PurchasePanelId:   "purchase_panel_id",
	PurchasePanelName: "purchase_panel_name",
	PurchasePanelJson: "purchase_panel_json",
	AdvertiseLink:     "advertise_link",
	TaskStatus:        "task_status",
	Response:          "response",
	CreateTime:        "create_time",
	UpdateTime:        "update_time",
	BookStatus:        "book_status",
	BookType:          "book_type",
	Connect:           "connect",
	PutTimeText:       "put_time_text",
	Type:              "type",
}

// NewDzNativeLinkDao creates and returns a new DAO object for table data access.
func NewDzNativeLinkDao() *DzNativeLinkDao {
	return &DzNativeLinkDao{
		group:   "default",
		table:   "dz_native_link",
		columns: dzNativeLinkColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzNativeLinkDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzNativeLinkDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzNativeLinkDao) Columns() DzNativeLinkColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzNativeLinkDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzNativeLinkDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzNativeLinkDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
