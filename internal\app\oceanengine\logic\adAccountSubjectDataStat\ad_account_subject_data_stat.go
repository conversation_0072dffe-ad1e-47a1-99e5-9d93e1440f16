// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/logic/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"slices"
	"sort"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAccountSubjectDataStat(New())
}

func New() service.IAdAccountSubjectDataStat {
	return &sAdAccountSubjectDataStat{}
}

type sAdAccountSubjectDataStat struct{}

func (s *sAdAccountSubjectDataStat) List(ctx context.Context, req *model.AdAccountSubjectDataStatSearchReq) (listRes *model.AdAccountSubjectDataStatSearchRes, err error) {
	listRes = new(model.AdAccountSubjectDataStatSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAccountSubjectDataStat.Ctx(ctx).As("a").
			LeftJoin("sys_user u", "a.user_id = u.id")
		summaryM := dao.AdAdvertiserAccount.Ctx(ctx)
		var admin bool
		var userIds []int
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a.user_id", req.UserIds)
			summaryM = summaryM.WhereIn("user_id", req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ = sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a.user_id", userIds)
				summaryM = summaryM.WhereIn("user_id", req.UserIds)
			}
		}
		if len(req.AdvertiserCompanies) > 0 {
			m = m.WhereIn("a.advertiser_company", req.AdvertiserCompanies)
			summaryM = summaryM.WhereIn("advertiser_company", req.AdvertiserCompanies)
		}
		if req.StartTime != "" {
			m = m.WhereGTE("a.create_date", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.WhereLTE("a.create_date", req.EndTime)
			_, endTime := libUtils.GetDayStartAndEnd(req.EndTime, req.EndTime)
			summaryM = summaryM.WhereLTE("created_at", endTime)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var groupBy string
		var orderBy string
		fields := make([]string, 0)
		if req.Merge == 1 {
			groupBy = "a.advertiser_company"
			fields = append(fields, "CONCAT(MIN(a.create_date), ' - ', MAX(a.create_date)) as createDate")
		} else {
			groupBy = "a.advertiser_company, a.user_id, a.create_date"
			fields = append(fields, "ANY_VALUE(a.create_date) as createDate")
			fields = append(fields, "ANY_VALUE(a.user_id) as userId")
			fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		}
		orderBy = "statPayAmount desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields = append(fields, "ANY_VALUE(a.advertiser_company) as advertiserCompany")
		fields = append(fields, "SUM(a.total_accounts) as totalAccounts")
		fields = append(fields, "SUM(a.active_accounts) as activeAccounts")
		fields = append(fields, "ROUND(SUM(a.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost),2) as payAmountRoi")
		fields = append(fields, "SUM(a.active) as active")
		fields = append(fields, "ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate")
		fields = append(fields, "SUM(a.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost),2) as attributionGameInAppRoi1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost),2) as attributionMicroGame0DRoi")
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		if listRes.List == nil || len(listRes.List) == 0 {
			return
		}
		if req.Merge == 1 {
			err = s.SummaryList(ctx, admin, userIds, req, listRes)
			liberr.ErrIsNil(ctx, err, "获取合并数据失败")
		}
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
		// 计算汇总总账户数和在投总账户数
		err2 := summaryM.FieldCount("DISTINCT advertiser_id", "totalAccounts").
			FieldCount("DISTINCT CASE WHEN ad_status = 1 THEN advertiser_id END", "activeAccounts").
			Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err2, "计算总账户数和在投总账户数失败")
	})
	return
}

// SummaryList 合并数据需要实时查询
func (s *sAdAccountSubjectDataStat) SummaryList(
	ctx context.Context,
	admin bool,
	userIds []int,
	req *model.AdAccountSubjectDataStatSearchReq,
	listRes *model.AdAccountSubjectDataStatSearchRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAdvertiserAccount.Ctx(ctx)
		statM := dao.AdAccountSubjectDataStat.Ctx(ctx)
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("user_id", userIds)
			statM = statM.WhereIn("user_id", userIds)
		}
		if len(req.AdvertiserCompanies) > 0 {
			m = m.WhereIn("advertiser_company", req.AdvertiserCompanies)
			statM = statM.WhereIn("advertiser_company", req.AdvertiserCompanies)
		}
		if req.StartTime != "" {
			statM = statM.WhereGTE("create_date", req.StartTime)
		}
		if req.EndTime != "" {
			_, endTime := libUtils.GetDayStartAndEnd(req.EndTime, req.EndTime)
			m = m.WhereLTE("created_at", endTime)
			statM = statM.WhereLTE("create_date", req.EndTime)
		}

		// 查询所有的账户主体对应的账户数和账户在投数
		var accountSubjectList []*model.AdAccountSubjectDataStatListRes
		err = m.Fields("advertiser_company as advertiserCompany").
			FieldCount("DISTINCT advertiser_id", "totalAccounts").
			FieldCount("DISTINCT CASE WHEN ad_status = 1 THEN advertiser_id END", "activeAccounts").
			Group("advertiser_company").
			Scan(&accountSubjectList)
		liberr.ErrIsNil(ctx, err, "查询账户主体数据失败")

		// 获取账户主体对应的指标数据
		var statMetricsList []*model.AdAccountSubjectDataStatListRes
		err = statM.
			Fields("MIN(create_date) as minTime").
			Fields("MAX(create_date) as maxTime").
			Fields("advertiser_company as advertiserCompany").
			Fields("SUM(stat_cost) as statCost").
			Fields("ROUND(SUM(stat_pay_amount)/SUM(stat_cost),2) as payAmountRoi").
			Fields("SUM(stat_pay_amount) as statPayAmount").
			Fields("SUM(active) as active").
			Fields("ROUND(SUM(active)/SUM(click_cnt)*100,2) as activeRate").
			Fields("SUM(click_cnt) as clickCnt").
			Fields("SUM(convert_cnt) as convertCnt").
			Fields("ROUND(SUM(convert_cnt)/SUM(click_cnt)*100,2) as conversionRate").
			Fields("SUM(attribution_game_in_app_ltv_1day) as attributionGameInAppLtv1Day").
			Fields("ROUND(SUM(attribution_game_in_app_ltv_1day)/SUM(stat_cost),2) as attributionGameInAppRoi1Day").
			Fields("SUM(attribution_micro_game_0d_ltv) as attributionMicroGame0DLtv").
			Fields("ROUND(SUM(attribution_micro_game_0d_ltv)/SUM(stat_cost),2) as attributionMicroGame0DRoi").
			Group("advertiser_company").
			Scan(&statMetricsList)
		liberr.ErrIsNil(ctx, err, "查询指标数据失败")

		for _, res := range accountSubjectList {
			for _, stat := range statMetricsList {
				if res.AdvertiserCompany == stat.AdvertiserCompany {
					res.CreateDate = stat.MinTime + " - " + stat.MaxTime
					res.StatCost = stat.StatCost
					res.StatPayAmount = stat.StatPayAmount
					res.PayAmountRoi = stat.PayAmountRoi
					res.Active = stat.Active
					res.ActiveRate = stat.ActiveRate
					res.ConvertCnt = stat.ConvertCnt
					res.ConversionRate = stat.ConversionRate
					res.AttributionGameInAppLtv1Day = stat.AttributionGameInAppLtv1Day
					res.AttributionGameInAppRoi1Day = stat.AttributionGameInAppRoi1Day
					res.AttributionMicroGame0DLtv = stat.AttributionMicroGame0DLtv
					res.AttributionMicroGame0DRoi = stat.AttributionMicroGame0DRoi
					break
				}
			}
		}

		// 通用排序实现
		s.sortAccountSubjectList(accountSubjectList, req.OrderBy, req.OrderType)

		// 计算总数
		listRes.Total = len(accountSubjectList)
		pageRes := make([]*model.AdAccountSubjectDataStatListRes, 0)
		linq.From(accountSubjectList).Skip(req.PageSize * (req.PageNum - 1)).Take(req.PageSize).ToSlice(&pageRes)
		listRes.List = pageRes
	})
	return
}

func (s *sAdAccountSubjectDataStat) Add(ctx context.Context, req *model.AdAccountSubjectDataStatAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAccountSubjectDataStat.Ctx(ctx).Insert(do.AdAccountSubjectDataStat{
			CreateDate:                  req.CreateDate,
			AdvertiserCompany:           req.AdvertiserCompany,
			UserId:                      req.UserId,
			TotalAccounts:               req.TotalAccounts,
			ActiveAccounts:              req.ActiveAccounts,
			StatCost:                    req.StatCost,
			StatPayAmount:               req.StatPayAmount,
			Active:                      req.Active,
			ClickCnt:                    req.ClickCnt,
			ConvertCnt:                  req.ConvertCnt,
			AttributionGameInAppLtv1Day: req.AttributionGameInAppLtv1Day,
			AttributionMicroGame0DLtv:   req.AttributionMicroGame0DLtv,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAccountSubjectDataStat) RunAdAccountSubjectDataStat(ctx context.Context, req *model.AdAccountSubjectDataStatSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		for {
			if startTime > endTime {
				break
			}
			errors := s.CalcAdAccountSubjectDataStat(innerContext, startTime)
			if errors != nil {
				g.Log().Error(ctx, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdAccountSubjectDataStat) CalcAdAccountSubjectDataStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdAccountSubjectDataStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdAccountSubjectDataStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
		err = s.CalcAdAccountSubjectDataStat(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "账户主体数据统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcAdAccountSubjectDataStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "账户主体数据统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdAccountSubjectDataStat) CalcTodayAdAccountSubjectDataStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdAccountSubjectDataStatTodayLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdAccountSubjectDataStatTodayLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		today := gtime.Now().Format("Y-m-d")
		err = s.CalcAdAccountSubjectDataStat(ctx, today)
		liberr.ErrIsNil(ctx, err, "当天账户主体数据统计")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcTodayAdAccountSubjectDataStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "当天账户主体数据统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// CalcAdAccountSubjectDataStat 账户主体数据统计
func (s *sAdAccountSubjectDataStat) CalcAdAccountSubjectDataStat(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accountSubjectList, _ := s.CalcAccountSubjectList(ctx, statDate)
		accountMetricsDataList, _ := s.CalcAccountMetricsDataList(ctx, statDate)
		var statList = make([]*model.AdAccountSubjectDataStatInfoRes, 0)
		for _, v := range accountSubjectList {
			v.CreateDate = statDate
			for _, item := range accountMetricsDataList {
				if v.AdvertiserCompany == item.AdvertiserCompany && v.UserId == item.UserId {
					v.StatCost = item.StatCost
					v.StatPayAmount = item.StatPayAmount
					v.Active = item.Active
					v.ClickCnt = item.ClickCnt
					v.ConvertCnt = item.ConvertCnt
					v.AttributionGameInAppLtv1Day = item.AttributionGameInAppLtv1Day
					v.AttributionMicroGame0DLtv = item.AttributionMicroGame0DLtv
					break
				}
			}
			statList = append(statList, v)
			if len(statList) >= 100 {
				_, err = dao.AdAccountSubjectDataStat.Ctx(ctx).Save(statList)
				statList = slices.Delete(statList, 0, len(statList))
			}
		}
		if len(statList) > 0 {
			_, err = dao.AdAccountSubjectDataStat.Ctx(ctx).Save(statList)
		}
	})
	return
}

// CalcAccountSubjectList 查询所有账户主体列表
func (s *sAdAccountSubjectDataStat) CalcAccountSubjectList(ctx context.Context, statDate string) (res []*model.AdAccountSubjectDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.AdAdvertiserAccount.Ctx(ctx).
			WhereLTE("created_at", endTime).
			Fields("advertiser_company as advertiserCompany").
			Fields("user_id as userId").
			FieldCount("DISTINCT advertiser_id", "totalAccounts").
			FieldCount("DISTINCT CASE WHEN ad_status = 1 THEN advertiser_id END", "activeAccounts").
			Group("advertiser_company, user_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询账户主体列表失败")
	})
	return
}

// CalcAccountMetricsDataList 查询账户指标统计数据
func (s *sAdAccountSubjectDataStat) CalcAccountMetricsDataList(ctx context.Context, statDate string) (res []*model.AdAccountSubjectDataStatInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccountMetricsDataAnalytic.Ctx(ctx).As("a").
			LeftJoin("ad_advertiser_account", "b", "a.advertiser_id = b.advertiser_id").
			Where("a.create_date", statDate).
			Fields("b.advertiser_company as advertiserCompany").
			Fields("b.user_id as userId").
			Fields("ROUND(SUM(a.stat_cost),2) as statCost").
			Fields("ROUND(SUM(a.stat_pay_amount),2) as statPayAmount").
			Fields("ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost),2) as payAmountRoi").
			Fields("SUM(a.active) as active").
			Fields("SUM(a.click_cnt) as clickCnt").
			Fields("ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate").
			Fields("SUM(a.convert_cnt) as convertCnt").
			Fields("ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate").
			Fields("ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day").
			Fields("ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost)*100,2) as attributionGameInAppRoi1Day").
			Fields("ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv").
			Fields("ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost)*100,2) as attributionMicroGame0DRoi").
			Group("b.advertiser_company, b.user_id").Scan(&res)
		liberr.ErrIsNil(ctx, err, "查询账户指标统计数据失败")
	})
	return
}

// sortAccountSubjectList 对账户主体数据列表进行排序
func (s *sAdAccountSubjectDataStat) sortAccountSubjectList(list []*model.AdAccountSubjectDataStatListRes, orderBy, orderType string) {
	if len(list) <= 1 {
		return
	}
	if orderBy == "" {
		orderBy = "statPayAmount"
	}
	if orderType == "" {
		orderType = "desc"
	}
	// 使用标准库sort包进行高效排序
	sort.Slice(list, func(i, j int) bool {
		var less bool
		switch orderBy {
		case "advertiserCompany":
			less = list[i].AdvertiserCompany < list[j].AdvertiserCompany
		case "totalAccounts":
			less = list[i].TotalAccounts < list[j].TotalAccounts
		case "activeAccounts":
			less = list[i].ActiveAccounts < list[j].ActiveAccounts
		case "statCost":
			less = list[i].StatCost < list[j].StatCost
		case "statPayAmount":
			less = list[i].StatPayAmount < list[j].StatPayAmount
		case "payAmountRoi":
			less = list[i].PayAmountRoi < list[j].PayAmountRoi
		case "active":
			less = list[i].Active < list[j].Active
		case "activeRate":
			less = list[i].ActiveRate < list[j].ActiveRate
		case "convertCnt":
			less = list[i].ConvertCnt < list[j].ConvertCnt
		case "conversionRate":
			less = list[i].ConversionRate < list[j].ConversionRate
		case "attributionGameInAppLtv1Day":
			less = list[i].AttributionGameInAppLtv1Day < list[j].AttributionGameInAppLtv1Day
		case "attributionGameInAppRoi1Day":
			less = list[i].AttributionGameInAppRoi1Day < list[j].AttributionGameInAppRoi1Day
		case "attributionMicroGame0DLtv":
			less = list[i].AttributionMicroGame0DLtv < list[j].AttributionMicroGame0DLtv
		case "attributionMicroGame0DRoi":
			less = list[i].AttributionMicroGame0DRoi < list[j].AttributionMicroGame0DRoi
		default:
			// 默认按付费金额排序
			less = list[i].StatPayAmount < list[j].StatPayAmount
		}
		// 如果是降序，则反转比较结果
		if orderType == "desc" {
			return !less
		}
		return less
	})
}
