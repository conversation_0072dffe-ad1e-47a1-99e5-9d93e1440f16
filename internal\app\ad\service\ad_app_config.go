// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/service/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IAdAppConfig interface {
	List(ctx context.Context, req *model.AdAppConfigSearchReq) (res *model.AdAppConfigSearchRes, err error)
	GetById(ctx context.Context, Id int) (res *model.AdAppConfigInfoRes, err error)
	Add(ctx context.Context, req *model.AdAppConfigAddReq) (err error)
	Edit(ctx context.Context, req *model.AdAppConfigEditReq) (err error)
	Delete(ctx context.Context, Id []int) (err error)
	GetAuthUrl(ctx context.Context, envType int32, appType int32, authUserType *int32, authUserId *string) (authUrl string, err error)
	GetByAppId(ctx context.Context, appId string) (res *model.AdAppConfigInfoRes, err error)
}

var localAdAppConfig IAdAppConfig

func AdAppConfig() IAdAppConfig {
	if localAdAppConfig == nil {
		panic("implement not found for interface IAdAppConfig, forgot register?")
	}
	return localAdAppConfig
}

func RegisterAdAppConfig(i IAdAppConfig) {
	localAdAppConfig = i
}
