// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/router/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/theater/controller"
)

func (router *Router) BindDzNativeLinkController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/dzNativeLink", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.DzNativeLink,
		)
	})
}
